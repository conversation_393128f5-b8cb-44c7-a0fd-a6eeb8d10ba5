from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash

from models import db
from forms import LoginForm, RegistrationForm
from models.models_customer import Customer

auth_bp = Blueprint('auth', __name__)


# In auth.py
@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = LoginForm()
    if form.validate_on_submit():
        email = form.email.data
        password = form.password.data
        user = Customer.query.filter_by(email=email).first()
        if user and user.check_password(password):
            login_user(user, remember=form.remember.data)

            # The typical code: see if a 'next' argument was provided
            next_page = request.args.get('next')

            # Force admin users to admin.dashboard
            if user.is_admin:
                return redirect(url_for('admin.dashboard'))

            # Otherwise fallback to next page or customer dashboard
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('customer.dashboard')
            return redirect(next_page)

        flash('Invalid credentials', 'danger')
    return render_template('auth/login.html', form=form)


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        # Check if email already exists
        if Customer.query.filter_by(email=form.email.data).first():
            flash('Email address already registered', 'danger')
            return render_template('auth/register.html', form=form)

        # Create new customer
        customer = Customer(
            name=form.name.data,
            email=form.email.data
        )
        customer.set_password(form.password.data)

        db.session.add(customer)
        db.session.commit()

        flash('Registration successful! You can now log in.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/register.html', form=form)


@auth_bp.route('/logout')
@login_required
def logout():
    """Handle user logout"""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))