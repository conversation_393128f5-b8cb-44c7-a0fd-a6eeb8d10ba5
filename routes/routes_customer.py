from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
import pandas as pd
from datetime import datetime, timedelta
from flask import make_response

from utils.chart_builders import build_allocation_chart
from utils.fancy_pdf_generator import generate_fancy_pdf_report
from utils.allocation_charts import generate_allocation_chart_as_bytes

from models import db
from forms import NewInvestmentForm, ReportGenerationForm
from models.investment import Investment
from models.performance import Performance
from models.portfolio import Portfolio
from routes.admin import admin_required, admin_bp
from utils.performance_calculator import calculate_customer_performance, calculate_historical_performance
from utils.report_generator import generate_customer_report, generate_investment_report, export_customer_data_to_csv
from utils.market_data import get_market_summary

customer_bp = Blueprint('customer', __name__)
from xhtml2pdf import pisa
from io import BytesIO

# In routes/routes_customer.py (or wherever the dashboard logic is):
# Summaries for total_invested and total_current_value now use each investment's updated get_current_value().

@customer_bp.route('/', methods=['GET'], endpoint='dashboard')
@login_required
def dashboard():
    from flask import current_app, render_template
    from models.investment import Investment
    from models.performance import Performance
    from utils.performance_calculator import calculate_customer_performance
    from utils.report_generator import generate_customer_report

    # Retrieve all investments for the current customer (includes pending, active, and closed)
    investments = Investment.query.filter_by(customer_id=current_user.id).all()

    # Recalculate performance for each investment
    for inv in investments:
        current_app.logger.info(f"Recalculating performance for investment {inv.id}")
        result = calculate_customer_performance(inv.id)
        current_app.logger.info(f"Performance calculation result: {result}")

    # (Optional) Log summary for all investments (for debugging)
    total_invested_all = sum(inv.amount for inv in investments)
    total_current_value_all = sum(inv.get_current_value() for inv in investments)
    total_return_all = ((total_current_value_all - total_invested_all) / total_invested_all * 100) if total_invested_all > 0 else 0
    current_app.logger.info(
        f"All investments - Total Invested: {total_invested_all}, Total Current Value: {total_current_value_all}, "
        f"Total Return: {total_return_all}%"
    )

    # Filter out pending investments (only include those that are both active and funded)
    funded_investments = [inv for inv in investments if inv.is_active and inv.is_funded]
    total_invested = sum(inv.amount for inv in funded_investments)
    total_current_val = sum(inv.get_current_value() for inv in funded_investments)
    total_return_pct = ((total_current_val - total_invested) / total_invested * 100) if total_invested > 0 else 0
    current_app.logger.info(
        f"Funded investments - Total Invested: {total_invested}, Total Current Value: {total_current_val}, "
        f"Total Return: {total_return_pct}%"
    )

    # Generate the report (this function internally retrieves investments by customer_id)
    report = generate_customer_report(current_user.id)
    current_app.logger.info(
        f"Report status: {report.get('status')}, has performance chart: {'performance_chart' in report}"
    )

    # Optionally, recompute performance for each investment (if required)
    for inv in investments:
        calculate_customer_performance(inv.id)

    # Get real market data from Yahoo Finance
    # market_data = get_market_summary()
    market_data = {'S&P 500':0,'NASDAQ':0,'DOW':0,'10-YR':0,'USD/EUR':0}
    # Render the dashboard template
    # - "investments" is the full list (so the table shows all investments with status badges)
    # - Summary totals (total_invested, total_current_value, total_return) are calculated only from funded investments
    return render_template('customer/customer_dashboard.html',
                           investments=investments,
                           total_invested=total_invested,
                           total_current_value=total_current_val,
                           total_return=total_return_pct,
                           report=report,
                           market_data=market_data,
                           now=datetime.utcnow())



# Optionally, in routes/routes_customer.py (dashboard or investments route), add debug logs:

@customer_bp.route('/investments')
@login_required
def investments():
    from flask import current_app
    investments = Investment.query.filter_by(customer_id=current_user.id).all()
    current_app.logger.info(f"[DEBUG] Found {len(investments)} investments for customer {current_user.id}")
    return render_template('customer/investments_dashboard.html', investments=investments)


# routes_customer.py
@customer_bp.route('/investment/new', methods=['GET', 'POST'])
@login_required
def new_investment():
    form = NewInvestmentForm()
    portfolios = Portfolio.query.all()
    if not portfolios:
        flash('No portfolios are available.', 'warning')
        return redirect(url_for('customer.investments'))

    form.portfolio_id.choices = [(p.id, f"{p.name} ({p.risk_level} Risk)") for p in portfolios]

    if form.validate_on_submit():
        # Retrieve the subscription cutoff hour from configuration.
        cutoff = current_app.config.get('SUBSCRIPTION_CUTOFF_HOUR', 16)
        chosen_date = form.start_date.data  # This is a date object.
        now = datetime.utcnow()  # Adjust based on your timezone if necessary.

        # If the chosen date is today and the current hour is past the cutoff, move start_date to tomorrow.
        if chosen_date == now.date() and now.hour >= cutoff:
            chosen_date = now.date() + timedelta(days=1)
            flash('The subscription cutoff has been reached. Your investment will be processed on ' +
                  chosen_date.strftime('%Y-%m-%d') + '.', 'info')

        # Create the investment as pending (i.e. not subscribed yet).
        investment = Investment(
            customer_id=current_user.id,
            portfolio_id=form.portfolio_id.data,
            amount=form.amount.data,
            start_date=datetime.combine(chosen_date, datetime.min.time()),  # Force time to midnight
            is_paper=form.paper_investment.data,
            is_active=True,
            is_funded=False,  # Mark as pending (subscribe later)
            units=0,
            entry_nav=0,
            high_water_mark=0
        )
        db.session.add(investment)
        db.session.commit()
        flash('Investment submitted and pending subscription pricing.', 'success')
        return redirect(url_for('customer.investments'))

    return render_template('customer/investment_form.html', form=form, title='New Investment')


# In admin.py
# In admin.py (inside your admin_bp blueprint)

# routes_customer.py

# In admin.py

@customer_bp.route('/investment/<int:investment_id>', endpoint='investment_detail')
@login_required
def investment_detail(investment_id):
    """
    Customer-side investment detail view.
    This route shows the investment details for a customer.
    """
    investment = Investment.query.get_or_404(investment_id)
    # Ensure the customer can only view their own investment (unless an admin)
    if not current_user.is_admin and investment.customer_id != current_user.id:
        flash('You do not have permission to view this investment.', 'danger')
        return redirect(url_for('customer.dashboard'))
    performances = Performance.query.filter_by(investment_id=investment_id).order_by(Performance.date).all()
    return render_template('customer/customer_investment_detail.html',
                           investment=investment,
                           performances=performances)


@customer_bp.route('/investment/<int:investment_id>/close', methods=['POST'])
@login_required
def close_investment(investment_id):
    from utils.performance_calculator import close_investment_with_exit_fee
    investment = Investment.query.get_or_404(investment_id)

    if investment.customer_id != current_user.id:
        flash('You do not have permission to modify this investment', 'danger')
        return redirect(url_for('customer.investments'))
    if not investment.is_active:
        flash('This investment is already closed', 'warning')
        return redirect(url_for('customer.investment_detail', investment_id=investment_id))

    result = close_investment_with_exit_fee(investment_id)
    if result.get('status') == "success":
        flash('Investment closed successfully with exit fee applied', 'success')
    else:
        flash(result.get('message', 'Error closing investment'), 'danger')
    return redirect(url_for('customer.investment_detail', investment_id=investment_id))


@customer_bp.route('/investment/<int:investment_id>/report', methods=['GET'])
@login_required
def investment_report_pdf(investment_id):
    # Similar implementation but for a specific investment
    from utils.integration import fancy_pdf_report_route
    return fancy_pdf_report_route(current_user.id, investment_id=investment_id)

@customer_bp.route('/fancy-pdf-report', methods=['GET'])
@login_required
def fancy_pdf_report():
    """Generate an enhanced PDF report for the current user's investments"""
    try:
        from utils.integration import fancy_pdf_report_route
        # Call the function with the current user's ID (explicitly converted to int)
        customer_id = int(current_user.id)
        return fancy_pdf_report_route(customer_id)
    except Exception as e:
        current_app.logger.error(f"Error generating fancy PDF report: {str(e)}")
        flash(f"Error generating report: {str(e)}", "danger")
        return redirect(url_for('customer.reports'))

@customer_bp.route('/reports')
@login_required
def reports():
    """View and generate reports"""
    form = ReportGenerationForm()

    # Default dates
    form.start_date.data = datetime(datetime.now().year, 1, 1)  # Start of current year
    form.end_date.data = datetime.now()

    return render_template('customer/reports.html', form=form)


@customer_bp.route('/my-fancy-pdf', methods=['GET'])
@login_required
def my_fancy_pdf():
    from utils.integration import fancy_pdf_report_route
    # This function handles everything - data gathering, report generation and response creation
    return fancy_pdf_report_route(current_user.id)


@customer_bp.route('/generate-report', methods=['POST'])
@login_required
def generate_report():
    from flask import (
        current_app, render_template, redirect, url_for, flash,
        Response, make_response
    )
    from datetime import datetime

    form = ReportGenerationForm()
    if form.validate_on_submit():
        report_type = form.report_type.data
        start_date = form.start_date.data
        end_date = form.end_date.data
        format_type = form.format.data.lower()

        try:
            # Get basic report data
            report = generate_customer_report(current_user.id, end_date)

            if format_type == 'csv':
                csv_data = export_customer_data_to_csv(current_user.id)
                response = Response(csv_data, mimetype='text/csv')
                response.headers['Content-Disposition'] = (
                    'attachment;filename=customer_report.csv'
                )
                return response

            elif format_type == 'pdf':
                try:
                    # Use the direct PDF generator that avoids the NoneType error
                    from utils.direct_pdf_generator import generate_direct_pdf

                    pdf_bytes = generate_direct_pdf(
                        report=report,
                        client_info={'name': current_user.name},
                        start_date=start_date,
                        end_date=end_date
                    )

                    response = make_response(pdf_bytes)
                    response.headers['Content-Type'] = 'application/pdf'
                    report_filename = f"investment_report_{datetime.now().strftime('%Y%m%d')}.pdf"
                    response.headers['Content-Disposition'] = f'attachment; filename={report_filename}'
                    return response

                except Exception as e:
                    current_app.logger.error(f"Error generating enhanced PDF: {str(e)}")
                    flash(f"Error generating PDF report: {str(e)}", "danger")
                    return redirect(url_for('customer.reports'))

            else:
                # Render the HTML summary - use existing report_summary.html instead of fancy_report_summary.html
                return render_template(
                    'customer/report_summary.html',  # Changed from fancy_report_summary.html
                    report=report,
                    start_date=start_date,
                    end_date=end_date,
                    now=datetime.utcnow()
                )
        except Exception as e:
            current_app.logger.error(f"Error generating report: {str(e)}")
            flash(f"Error generating report: {str(e)}", "danger")
            return redirect(url_for('customer.reports'))
    else:
        flash("Invalid form submission or method is GET", "danger")
        return redirect(url_for('customer.reports'))


@customer_bp.route('/portfolios')
@login_required
def portfolios():
    from models.portfolio import Portfolio
    from models.investment import Investment

    portfolios = Portfolio.query.all()
    portfolio_data = []
    for portfolio in portfolios:
        # Retrieve only active and funded investments for this customer in this portfolio.
        investments = Investment.query.filter_by(
            customer_id=current_user.id,
            portfolio_id=portfolio.id,
            is_active=True,
            is_funded=True
        ).all()

        # If there is at least one such investment, sum the invested amount and current value.
        if investments:
            invested_amount = sum(inv.amount for inv in investments)
            current_value = sum(inv.get_current_value() for inv in investments)
        else:
            invested_amount = 0
            current_value = 0

        portfolio_data.append({
            'portfolio': portfolio,
            'invested': invested_amount,
            'current_value': current_value,
            'investments': investments  # you can list them if needed
        })

    return render_template('customer/portfolios_dashboard.html', portfolio_data=portfolio_data)


@customer_bp.route('/portfolio/<int:portfolio_id>')
@login_required
def portfolio_detail(portfolio_id):
    """View portfolio details"""
    portfolio = Portfolio.query.get_or_404(portfolio_id)
    allocations = portfolio.allocations.all()

    # Check if user has an investment in this portfolio
    investment = Investment.query.filter_by(
        customer_id=current_user.id,
        portfolio_id=portfolio.id,
        is_active=True
    ).first()

    return render_template('customer/portfolio_detail.html',
                           portfolio=portfolio,
                           allocations=allocations,
                           investment=investment)