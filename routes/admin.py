import os
from datetime import datetime
from functools import wraps
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename

from models import db, Performance
from forms import ImportReportForm, PortfolioForm, AllocationForm, SecurityForm, SecurityPriceForm, NewInvestmentForm
from models.models_customer import Customer
from models.investment import Investment
from models.portfolio import Portfolio, Security, PortfolioAllocation
from utils.ibkr_importer import process_ibkr_report

admin_bp = Blueprint('admin', __name__)


# Admin authorization decorator
def admin_required(func):
    from functools import wraps
    @login_required
    @wraps(func)
    def decorated_view(*args, **kwargs):
        if not current_user.is_admin:
            flash('You do not have permission to access this page.', 'danger')
            return redirect(url_for('index'))
        return func(*args, **kwargs)
    return decorated_view


@admin_bp.route('/')
@login_required
@admin_required
def dashboard():
    # Existing summary calculations
    customer_count = Customer.query.filter_by(is_admin=False).count()
    portfolio_count = Portfolio.query.count()
    security_count = Security.query.count()
    investment_count = Investment.query.count()
    recent_investments = Investment.query.order_by(Investment.created_at.desc()).limit(10).all()

    # Query portfolios for admin view (you can fetch all or a subset)
    portfolios = Portfolio.query.all()
    # Optionally, compute additional statistics for each portfolio.
    portfolio_stats = []
    for p in portfolios:
        funded_investments = [inv for inv in p.investments if inv.is_active and inv.is_funded]
        total_invested = sum(inv.amount for inv in funded_investments)
        investor_count = p.investments.with_entities(Investment.customer_id).distinct().count()
        portfolio_stats.append({
            'id': p.id,
            'name': p.name,
            'risk_level': p.risk_level,
            'total_invested': total_invested,
            'investor_count': investor_count
        })

    # Also, get pending subscription dates (if you already have that logic)
    pending_dates_raw = db.session.query(Investment.start_date).filter(
        Investment.is_active == True,
        Investment.is_funded == False
    ).distinct().all()
    pending_dates = [d[0].strftime("%Y-%m-%d") for d in pending_dates_raw if d[0] is not None]

    return render_template('admin/admin_dashboard.html',
                           customer_count=customer_count,
                           portfolio_count=portfolio_count,
                           security_count=security_count,
                           investment_count=investment_count,
                           recent_investments=recent_investments,
                           portfolio_stats=portfolio_stats,
                           pending_dates=pending_dates)


@admin_bp.route('/daily-subscription/<string:sub_date>', methods=['GET', 'POST'])
@admin_required
def daily_subscription(sub_date):
    """
    Admin view to process pending investments for a specific day (format: YYYY-MM-DD).
    """
    sub_date_dt = datetime.strptime(sub_date, '%Y-%m-%d')
    # Retrieve pending investments for that day.
    pending_investments = Investment.query.filter(
        Investment.start_date == sub_date_dt,
        Investment.is_active == True,
        Investment.is_funded == False
    ).all()

    if request.method == 'POST':
        from utils.daily_subscription import process_pending_investments
        process_pending_investments(sub_date_dt)
        flash(f'Pending investments for {sub_date} have been subscribed.', 'success')
        return redirect(url_for('admin.dashboard'))

    return render_template('admin/daily_subscription.html',
                           sub_date=sub_date_dt,
                           pending_investments=pending_investments)



@admin_bp.route('/customer/<int:customer_id>/delete', methods=['POST'])
@admin_required
def delete_customer(customer_id):
    from models.models_customer import Customer  # Ensure Customer is imported
    from models.investment import Investment  # Import the Investment model

    customer = Customer.query.get_or_404(customer_id)

    # First, delete all investments belonging to this customer
    investments = Investment.query.filter_by(customer_id=customer_id).all()
    for investment in investments:
        db.session.delete(investment)

    # Then delete the customer
    db.session.delete(customer)

    try:
        db.session.commit()
        flash(f'Customer {customer.name} and their investments deleted successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting customer: {str(e)}', 'danger')

    return redirect(url_for('admin.customers'))


@admin_bp.route('/investment/<int:investment_id>/activate', methods=['POST'])
@admin_required
def activate_investment(investment_id):
    from models.investment import Investment
    investment = Investment.query.get_or_404(investment_id)
    if investment.is_funded:
        flash("Investment is already activated.", "info")
    else:
        investment.is_funded = True
        db.session.commit()
        flash("Investment has been activated.", "success")
    # Redirect to the customer investment detail view
    return redirect(url_for('customer.investment_detail', investment_id=investment.id))


@admin_bp.route('/investment/<int:investment_id>/mark-funded', methods=['POST'])
@admin_required
def mark_investment_funded(investment_id):
    investment = Investment.query.get_or_404(investment_id)
    investment.is_funded = True
    db.session.commit()
    flash(f'Investment {investment.id} marked as funded.', 'success')
    return redirect(url_for('admin.investment_detail', investment_id=investment.id))


# New route: All Investments list for admin
@admin_bp.route('/investments', endpoint='all_investments')
@admin_required
def all_investments():
    """
    Admin view to list all investments.
    """
    all_investments = Investment.query.order_by(Investment.created_at.desc()).all()
    current_app.logger.info(f"Admin {current_user.id} is viewing all investments.")
    return render_template('admin/admin_all_investments.html', investments=all_investments)

# Existing route: Admin Single Investment Detail
@admin_bp.route('/investment/<int:investment_id>', endpoint='investment_detail')
@admin_required
def investment_detail(investment_id):
    """
    Admin view for a single investment’s details.
    """
    investment = Investment.query.get_or_404(investment_id)
    performances = Performance.query.filter_by(investment_id=investment_id).order_by(Performance.date).all()
    current_app.logger.info(f"Admin {current_user.id} is viewing details for investment {investment.id}")
    return render_template('admin/admin_investment_detail.html',
                           investment=investment,
                           performances=performances)

@admin_bp.route('/customers')
@admin_required
def customers():
    from models.models_customer import Customer

    # Get all non-admin customers
    customers = Customer.query.filter_by(is_admin=False).all()

    # Build a new list with adjusted totals excluding pending investments.
    customers_data = []
    for cust in customers:
        # Only include investments that are active and funded.
        funded_investments = [inv for inv in cust.investments if inv.is_active and inv.is_funded]
        total_invested = sum(inv.amount for inv in funded_investments)
        total_current_value = sum(inv.get_current_value() for inv in funded_investments)
        total_return = 0
        if total_invested > 0:
            total_return = (total_current_value - total_invested) / total_invested * 100

        customers_data.append({
            'id': cust.id,
            'name': cust.name,
            'email': cust.email,
            'created_at': cust.created_at,
            'total_invested': total_invested,
            'current_value': total_current_value,
            'return_pct': total_return
        })

    return render_template(
        'admin/customers.html',
        customers_data=customers_data
    )


# In routes/admin.py
@admin_bp.route('/customer/<int:customer_id>')
@admin_required
def customer_detail(customer_id):
    from models.models_customer import Customer

    customer = Customer.query.get_or_404(customer_id)
    all_investments = customer.investments.all()

    # Only consider funded AND active for total calculations
    active_funded_investments = [inv for inv in all_investments if inv.is_active and inv.is_funded]

    total_invested = sum(inv.amount for inv in active_funded_investments)
    total_current_value = sum(inv.get_current_value() for inv in active_funded_investments)
    total_return = 0
    if total_invested > 0:
        total_return = (total_current_value - total_invested) / total_invested * 100

    return render_template(
        'admin/customer_detail.html',
        customer=customer,
        investments=all_investments,        # so we can see pending vs active vs closed
        total_invested=total_invested,
        total_current_value=total_current_value,
        total_return=total_return
    )


@admin_bp.route('/customer/<int:customer_id>/edit', methods=['POST'])
@admin_required
def edit_customer(customer_id):
    """Edit an existing customer"""
    customer = Customer.query.get_or_404(customer_id)

    # Update customer details
    customer.name = request.form.get('name', customer.name)
    customer.email = request.form.get('email', customer.email)

    # Only update password if provided
    password = request.form.get('password')
    if password and password.strip():
        customer.set_password(password)

    db.session.commit()

    flash(f'Customer {customer.name} updated successfully', 'success')
    return redirect(url_for('admin.customers'))


# admin.py
@admin_bp.route('/customer/<int:customer_id>/investment/new', methods=['GET', 'POST'])
@admin_required
def new_investment_for_customer(customer_id):
    """Create a new investment for a specific customer."""
    # Retrieve the customer using the ID provided in the URL
    customer = Customer.query.get_or_404(customer_id)

    # Instantiate the form
    form = NewInvestmentForm()
    portfolios = Portfolio.query.all()
    if not portfolios:
        flash('No portfolios are available for investment.', 'warning')
        return redirect(url_for('admin.customers'))
    # Build the portfolio choices (showing portfolio name and risk level)
    form.portfolio_id.choices = [(p.id, f"{p.name} ({p.risk_level} Risk)") for p in portfolios]

    if form.validate_on_submit():
        # Create the investment using the customer_id from the URL.
        # Notice: We set customer_id=customer.id (not current_user.id)
        investment = Investment(
            customer_id=customer.id,   # <-- This is the key fix.
            portfolio_id=form.portfolio_id.data,
            amount=form.amount.data,
            start_date=form.start_date.data,
            is_paper=form.paper_investment.data,
            is_active=True,
            is_funded=False,  # Initially pending (later will be processed/subscribed)
            units=0,
            entry_nav=0,
            high_water_mark=0
        )
        db.session.add(investment)
        db.session.commit()

        flash(f'Investment for {customer.name} has been submitted as pending.', 'success')
        # Redirect to the customer detail page (shows that the investment belongs to this customer)
        return redirect(url_for('admin.customer_detail', customer_id=customer.id))

    # Render the same form template but with the customer information passed in.
    return render_template('customer/investment_form.html',
                           form=form,
                           customer=customer,
                           title=f'New Investment for {customer.name}')


@admin_bp.route('/customer/new', methods=['POST'])
@admin_required
def new_customer():
    name = request.form.get('name')
    email = request.form.get('email')
    password = request.form.get('password')
    # Read the new checkbox value; if checked, the value will be "on"
    is_admin_val = request.form.get('is_admin')
    is_admin = True if is_admin_val == 'on' else False

    if not name or not email or not password:
        flash('All fields are required', 'danger')
        return redirect(url_for('admin.customers'))
    existing = Customer.query.filter_by(email=email).first()
    if existing:
        flash('A customer with that email already exists', 'danger')
        return redirect(url_for('admin.customers'))

    customer = Customer(
        name=name,
        email=email,
        is_admin=is_admin  # Use the new value
    )
    customer.set_password(password)
    db.session.add(customer)
    db.session.commit()

    flash(f'Customer {name} created successfully', 'success')
    return redirect(url_for('admin.customers'))


@admin_bp.route('/import-report', methods=['GET', 'POST'])
@admin_required
def import_report():
    form = ImportReportForm()  # Now form.report_file exists
    if form.validate_on_submit():
        # Save the uploaded report file
        report_file = form.report_file.data
        filename = secure_filename(report_file.filename)
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        report_file.save(file_path)

        # Process the IBKR report
        result = process_ibkr_report(file_path, form.file_type.data)

        if result['status'] == 'success':
            flash(
                f"Report imported successfully. Securities: {result['securities_updated']}, Prices: {result['prices_updated']}",
                'success')
        else:
            flash(f"Error importing report: {result['message']}", 'danger')

        os.remove(file_path)
        return redirect(url_for('admin.import_report'))

    return render_template('admin/reports.html', form=form)


@admin_bp.route('/portfolios')
@admin_required
def portfolios():
    """
    Admin view: show all portfolios with summary statistics calculated across
    all active and funded investments.
    """
    from models.portfolio import Portfolio
    from models.investment import Investment

    portfolios = Portfolio.query.all()
    portfolio_data = []
    for portfolio in portfolios:
        # Get all investments for this portfolio that are active and funded.
        investments = Investment.query.filter_by(
            portfolio_id=portfolio.id,
            is_active=True,
            is_funded=True
        ).all()
        if investments:
            invested_amount = sum(inv.amount for inv in investments)
            current_value = sum(inv.get_current_value() for inv in investments)
        else:
            invested_amount = 0
            current_value = 0

        portfolio_data.append({
            'portfolio': portfolio,
            'invested': invested_amount,
            'current_value': current_value,
            'investments': investments  # Optional: pass the list if you need to drill down
        })

    # Render the admin template and pass portfolio_data
    return render_template('admin/portfolios.html', portfolio_data=portfolio_data)


@admin_bp.route('/portfolio/new', methods=['GET', 'POST'])
@admin_required
def new_portfolio():
    """Create a new portfolio"""
    form = PortfolioForm()

    if form.validate_on_submit():
        portfolio = Portfolio(
            name=form.name.data,
            description=form.description.data,
            risk_level=form.risk_level.data
        )

        db.session.add(portfolio)
        db.session.commit()

        flash('Portfolio created successfully', 'success')
        return redirect(url_for('admin.portfolios'))

    return render_template('admin/portfolio_form.html', form=form, title='New Portfolio')


@admin_bp.route('/portfolio/<int:portfolio_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_portfolio(portfolio_id):
    """Edit an existing portfolio"""
    portfolio = Portfolio.query.get_or_404(portfolio_id)
    form = PortfolioForm(obj=portfolio)

    if form.validate_on_submit():
        portfolio.name = form.name.data
        portfolio.description = form.description.data
        portfolio.risk_level = form.risk_level.data

        db.session.commit()

        flash('Portfolio updated successfully', 'success')
        return redirect(url_for('admin.portfolios'))

    return render_template('admin/portfolio_form.html', form=form, portfolio=portfolio, title='Edit Portfolio')



@admin_bp.route('/portfolio/<int:portfolio_id>')
@admin_required
def portfolio_detail(portfolio_id):
    """View portfolio details"""
    portfolio = Portfolio.query.get_or_404(portfolio_id)
    allocations = portfolio.allocations.all()
    investments = portfolio.investments.all()

    # Calculate total invested
    total_invested = sum(inv.amount for inv in investments)

    return render_template('admin/portfolio_detail.html',
                           portfolio=portfolio,
                           allocations=allocations,
                           investments=investments,
                           total_invested=total_invested)


@admin_bp.route('/portfolio/<int:portfolio_id>/allocations', methods=['GET', 'POST'])
@admin_required
def portfolio_allocations(portfolio_id):
    """Manage portfolio allocations"""
    portfolio = Portfolio.query.get_or_404(portfolio_id)
    allocations = portfolio.allocations.all()

    # Form for adding new allocations
    form = AllocationForm()
    form.security_id.choices = [(s.id, f"{s.symbol} - {s.name}") for s in Security.query.all()]

    if form.validate_on_submit():
        # Check if security already exists in portfolio
        existing = PortfolioAllocation.query.filter_by(
            portfolio_id=portfolio_id,
            security_id=form.security_id.data
        ).first()

        if existing:
            existing.percentage = form.percentage.data
            flash('Allocation updated successfully', 'success')
        else:
            allocation = PortfolioAllocation(
                portfolio_id=portfolio_id,
                security_id=form.security_id.data,
                percentage=form.percentage.data
            )
            db.session.add(allocation)
            flash('Allocation added successfully', 'success')

        db.session.commit()
        return redirect(url_for('admin.portfolio_allocations', portfolio_id=portfolio_id))

    return render_template('admin/portfolio_allocations.html',
                           portfolio=portfolio,
                           allocations=allocations,
                           form=form)


def admin_required(f):
    """
    Decorator for routes that should only be accessible to admin users.
    Must be used after @login_required
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # First check if user is logged in (handled by login_required)
        if not current_user.is_admin:
            flash('You must be an administrator to access this page.', 'danger')
            return redirect(url_for('customer.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/securities')
@admin_required
def securities():
    """Manage securities"""
    securities = Security.query.all()
    return render_template('admin/securities.html', securities=securities)


@admin_bp.route('/security/new', methods=['GET', 'POST'])
@admin_required
def new_security():
    """Add a new security"""
    form = SecurityForm()

    if form.validate_on_submit():
        # Check if symbol already exists
        if Security.query.filter_by(symbol=form.symbol.data).first():
            flash('Security with this symbol already exists', 'danger')
            return render_template('admin/security_form.html', form=form, title='New Security')

        security = Security(
            symbol=form.symbol.data,
            name=form.name.data,
            type=form.type.data
        )

        db.session.add(security)
        db.session.commit()

        flash('Security added successfully', 'success')
        return redirect(url_for('admin.securities'))

    return render_template('admin/security_form.html', form=form, title='New Security')


@admin_bp.route('/security/<int:security_id>/prices', methods=['GET', 'POST'])
@admin_required
def security_prices(security_id):
    """Manage security prices"""
    security = Security.query.get_or_404(security_id)

    # Form for adding new price
    form = SecurityPriceForm()
    form.security_id.data = security_id

    if form.validate_on_submit():
        from models import SecurityPrice

        # Check if price for this date already exists
        existing = SecurityPrice.query.filter_by(
            security_id=security_id,
            date=form.date.data
        ).first()

        if existing:
            existing.price = form.price.data
            flash('Price updated successfully', 'success')
        else:
            price = SecurityPrice(
                security_id=security_id,
                date=form.date.data,
                price=form.price.data
            )
            db.session.add(price)
            flash('Price added successfully', 'success')

        db.session.commit()
        return redirect(url_for('admin.security_prices', security_id=security_id))
    from models.security_price import SecurityPrice
    # Get existing prices
    prices = SecurityPrice.query.filter_by(security_id=security_id).order_by(SecurityPrice.date.desc()).all()

    return render_template('admin/security_prices.html',
                           security=security,
                           prices=prices,
                           form=form)