from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from datetime import datetime

from models import db
from models.models_customer import Customer
from models.investment import Investment
from models.performance import Performance
from models.portfolio import Portfolio
from utils.performance_calculator import calculate_customer_performance, calculate_historical_performance

api_bp = Blueprint('api', __name__)


@api_bp.route('/performance/<int:investment_id>')
@login_required
def get_performance(investment_id):
    """Get performance data for a specific investment"""
    # Check if investment belongs to current user
    investment = Investment.query.get_or_404(investment_id)

    if investment.customer_id != current_user.id and not current_user.is_admin:
        return jsonify({'error': 'Not authorized'}), 403

    # Get performance history
    performances = Performance.query.filter_by(investment_id=investment_id).order_by(Performance.date).all()

    # Format for chart.js
    data = {
        'labels': [p.date.strftime("%Y-%m-%d") for p in performances],
        'values': [float(p.value) for p in performances],
        'returns': [float(p.return_pct) for p in performances]
    }

    return jsonify(data)


@api_bp.route('/portfolio/<int:portfolio_id>/snapshot-history')
@login_required
def get_portfolio_snapshot_history(portfolio_id):
    """
    Returns the historical snapshots (dates and total_value)
    for a given portfolio.
    """
    from models.performance import PortfolioSnapshot  # ensure it's imported
    snapshots = PortfolioSnapshot.query.filter_by(portfolio_id=portfolio_id).order_by(PortfolioSnapshot.date).all()
    labels = [snap.date.strftime("%Y-%m-%d") for snap in snapshots]
    values = [snap.total_value for snap in snapshots]

    return jsonify({
        "labels": labels,
        "values": values
    })


@api_bp.route('/historical-performance/<int:investment_id>')
@login_required
def get_historical_performance(investment_id):
    """Get historical performance for an investment with specified parameters"""
    # Check if investment belongs to current user
    investment = Investment.query.get_or_404(investment_id)
    if investment.customer_id != current_user.id and not current_user.is_admin:
        return jsonify({'error': 'Not authorized'}), 403

    # Get query parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    interval = request.args.get('interval', 'month')  # Default to monthly

    # Parse dates
    try:
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        else:
            start_date = investment.start_date

        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        else:
            end_date = datetime.utcnow()
    except ValueError:
        return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400

    # Calculate historical performance
    history = calculate_historical_performance(investment_id, start_date, end_date, interval)

    return jsonify(history)


@api_bp.route('/portfolio/<int:portfolio_id>/allocations')
@login_required
def get_portfolio_allocations(portfolio_id):
    """Get allocation data for a portfolio"""
    portfolio = Portfolio.query.get_or_404(portfolio_id)
    allocations = portfolio.allocations.all()

    # Format for chart.js
    data = {
        'labels': [a.security.name for a in allocations],
        'values': [float(a.percentage) for a in allocations],
        'colors': [
                      'rgba(255, 99, 132, 0.8)',
                      'rgba(54, 162, 235, 0.8)',
                      'rgba(255, 206, 86, 0.8)',
                      'rgba(75, 192, 192, 0.8)',
                      'rgba(153, 102, 255, 0.8)',
                      'rgba(255, 159, 64, 0.8)',
                      'rgba(199, 199, 199, 0.8)',
                      'rgba(83, 102, 255, 0.8)',
                      'rgba(40, 159, 64, 0.8)',
                      'rgba(210, 199, 199, 0.8)'
                  ][:len(allocations)]  # Limit colors to number of allocations
    }

    return jsonify(data)


@api_bp.route('/customer/dashboard-data')
@login_required
def get_dashboard_data():
    """Get summary data for customer dashboard"""
    investments = Investment.query.filter_by(customer_id=current_user.id).all()

    # Calculate summary metrics
    total_invested = sum(inv.amount for inv in investments)
    total_current_value = sum(inv.get_current_value() for inv in investments)
    total_return = (total_current_value - total_invested) / total_invested * 100 if total_invested > 0 else 0

    # Get portfolio breakdown
    portfolio_data = {}
    for investment in investments:
        portfolio = investment.portfolio
        if portfolio.name in portfolio_data:
            portfolio_data[portfolio.name]['amount'] += investment.amount
            portfolio_data[portfolio.name]['current_value'] += investment.get_current_value()
        else:
            portfolio_data[portfolio.name] = {
                'id': portfolio.id,
                'amount': investment.amount,
                'current_value': investment.get_current_value(),
            }

    # Calculate returns for each portfolio
    for name, data in portfolio_data.items():
        if data['amount'] > 0:
            data['return'] = (data['current_value'] - data['amount']) / data['amount'] * 100
        else:
            data['return'] = 0

    # Format data for response
    response = {
        'summary': {
            'total_invested': total_invested,
            'total_current_value': total_current_value,
            'total_return': total_return
        },
        'investments': [
            {
                'id': inv.id,
                'portfolio_name': inv.portfolio.name,
                'amount': inv.amount,
                'current_value': inv.get_current_value(),
                'return': inv.get_return(),
                'start_date': inv.start_date.strftime('%Y-%m-%d')
            } for inv in investments
        ],
        'portfolios': [
            {
                'name': name,
                'amount': data['amount'],
                'current_value': data['current_value'],
                'return': data['return']
            } for name, data in portfolio_data.items()
        ]
    }

    return jsonify(response)


@api_bp.route('/admin/dashboard-data')
@login_required
def get_admin_dashboard_data():
    """Get summary data for admin dashboard"""
    # Check if user is admin
    if not current_user.is_admin:
        return jsonify({'error': 'Not authorized'}), 403

    # Get summary statistics
    customer_count = Customer.query.filter_by(is_admin=False).count()
    portfolio_count = Portfolio.query.count()
    investment_count = Investment.query.count()

    # Get portfolio statistics
    portfolios = Portfolio.query.all()
    portfolio_stats = []

    for portfolio in portfolios:
        investments = portfolio.investments.all()
        total_invested = sum(inv.amount for inv in investments)
        investor_count = portfolio.investments.with_entities(Investment.customer_id).distinct().count()

        portfolio_stats.append({
            'id': portfolio.id,
            'name': portfolio.name,
            'risk_level': portfolio.risk_level,
            'total_invested': total_invested,
            'investor_count': investor_count
        })

    # Get recent investments
    recent_investments = Investment.query.order_by(Investment.created_at.desc()).limit(10).all()
    recent_investments_data = [
        {
            'id': inv.id,
            'customer_name': inv.customer.name,
            'portfolio_name': inv.portfolio.name,
            'amount': inv.amount,
            'start_date': inv.start_date.strftime('%Y-%m-%d')
        } for inv in recent_investments
    ]

    response = {
        'summary': {
            'customer_count': customer_count,
            'portfolio_count': portfolio_count,
            'investment_count': investment_count
        },
        'portfolios': portfolio_stats,
        'recent_investments': recent_investments_data
    }

    return jsonify(response)


@api_bp.route('/calculate-performance/<int:investment_id>', methods=['POST'])
@login_required
def calculate_performance(investment_id):
    """Trigger performance calculation for an investment"""
    # Check if investment belongs to current user or user is admin
    investment = Investment.query.get_or_404(investment_id)
    if investment.customer_id != current_user.id and not current_user.is_admin:
        return jsonify({'error': 'Not authorized'}), 403

    # Calculate performance
    result = calculate_customer_performance(investment_id)

    return jsonify(result)