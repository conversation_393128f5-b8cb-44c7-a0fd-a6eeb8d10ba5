# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
.coverage
htmlcov/

# Virtual Environment
.venv/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Database
*.db
*.sqlite3

# Environment variables
.env
.env.*

# Logs
*.log

# Local development
.DS_Store
Thumbs.db

# Docker
Dockerfile
.dockerignore

# Documentation
README.md
LICENSE
docs/

# Test files
tests/
test_*.py
*_test.py

# Temporary files
*.tmp
*.bak
*.swp
*~ 