from app import create_app, db
from datetime import datetime

from models.portfolio import Security, Portfolio, PortfolioAllocation
from models.security_price import SecurityPrice

# Create app context
app = create_app()
with app.app_context():
    # STEP 1: Add the securities (ETFs) if they don't already exist,
    # including the new KAIF security.
    securities_data = [
        {"symbol": "SPUS", "name": "SP Funds S&P 500 Sharia Industry Exclusions ETF", "type": "ETF"},
        {"symbol": "SPWO", "name": "SP Funds Dow Jones Global Sukuk ETF", "type": "ETF"},
        {"symbol": "GLD", "name": "SPDR Gold Shares", "type": "ETF"},
        {"symbol": "SPSK", "name": "SP Funds Dow Jones Global Sukuk ETF", "type": "ETF"},
        {"symbol": "SPRE", "name": "SP Funds S&P Global REIT Sharia ETF", "type": "ETF"},
        {"symbol": "KAIF", "name": "KAIF", "type": "Other"}
    ]

    for sec in securities_data:
        existing = Security.query.filter_by(symbol=sec["symbol"]).first()
        if not existing:
            security = Security(
                symbol=sec["symbol"],
                name=sec["name"],
                type=sec["type"]
            )
            db.session.add(security)
    db.session.commit()
    print("Securities added successfully")

    # STEP 2: Create the five new portfolios with the desired names.
    portfolios = [
        {
            "name": "Dynamic Wealth",
            "description": "Portfolio for dynamic wealth creation.",
            "risk_level": "Medium"
        },
        {
            "name": "Growth Catalyst",
            "description": "Portfolio focused on high growth opportunities.",
            "risk_level": "Medium"
        },
        {
            "name": "Balanced Horizon",
            "description": "Portfolio balanced for long-term stability and moderate growth.",
            "risk_level": "Medium"
        },
        {
            "name": "Steady Stream",
            "description": "Portfolio focused on steady income and low volatility.",
            "risk_level": "Low"
        },
        {
            "name": "KAIF",
            "description": "Portfolio entirely invested in KAIF.",
            "risk_level": "Medium"
        }
    ]

    portfolio_objects = []
    for port in portfolios:
        existing = Portfolio.query.filter_by(name=port["name"]).first()
        if existing:
            portfolio_objects.append(existing)
        else:
            p = Portfolio(
                name=port["name"],
                description=port["description"],
                risk_level=port["risk_level"]
            )
            db.session.add(p)
            db.session.flush()  # To get the ID
            portfolio_objects.append(p)
    db.session.commit()
    print("Portfolios created successfully")

    # STEP 3: Create allocations for each portfolio with the desired percentages.
    # For portfolios 1 to 4, we use the five ETF securities. For portfolio 5, only KAIF.
    # First, get the securities (including KAIF) for reference.
    securities = {}
    for symbol in ["SPUS", "SPWO", "GLD", "SPSK", "SPRE", "KAIF"]:
        securities[symbol] = Security.query.filter_by(symbol=symbol).first()

    # Define new allocations for each portfolio.
    allocations = [
        # Dynamic Wealth: SPUS: 54%, SPWO: 20%, SPRE: 5%, SPSK: 11%, GLD: 10%
        [
            {"symbol": "SPUS", "percentage": 54},
            {"symbol": "SPWO", "percentage": 20},
            {"symbol": "SPRE", "percentage": 5},
            {"symbol": "SPSK", "percentage": 11},
            {"symbol": "GLD", "percentage": 10}
        ],
        # Growth Catalyst: SPUS: 35%, SPWO: 15%, SPRE: 10%, SPSK: 30%, GLD: 10%
        [
            {"symbol": "SPUS", "percentage": 35},
            {"symbol": "SPWO", "percentage": 15},
            {"symbol": "SPRE", "percentage": 10},
            {"symbol": "SPSK", "percentage": 30},
            {"symbol": "GLD", "percentage": 10}
        ],
        # Balanced Horizon: SPUS: 30%, SPWO: 10%, SPRE: 10%, SPSK: 40%, GLD: 10%
        [
            {"symbol": "SPUS", "percentage": 30},
            {"symbol": "SPWO", "percentage": 10},
            {"symbol": "SPRE", "percentage": 10},
            {"symbol": "SPSK", "percentage": 40},
            {"symbol": "GLD", "percentage": 10}
        ],
        # Steady Stream: SPUS: 20%, SPWO: 5%, SPRE: 5%, SPSK: 60%, GLD: 10%
        [
            {"symbol": "SPUS", "percentage": 20},
            {"symbol": "SPWO", "percentage": 5},
            {"symbol": "SPRE", "percentage": 5},
            {"symbol": "SPSK", "percentage": 60},
            {"symbol": "GLD", "percentage": 10}
        ],
        # KAIF: KAIF: 100%
        [
            {"symbol": "KAIF", "percentage": 100}
        ]
    ]

    # Clear existing allocations for these portfolios and add new ones.
    for i, portfolio in enumerate(portfolio_objects):
        PortfolioAllocation.query.filter_by(portfolio_id=portfolio.id).delete()
        for alloc in allocations[i]:
            security = securities[alloc["symbol"]]
            allocation = PortfolioAllocation(
                portfolio_id=portfolio.id,
                security_id=security.id,
                percentage=alloc["percentage"]
            )
            db.session.add(allocation)
    db.session.commit()
    print("Portfolio allocations set successfully")

    # STEP 4: Add initial prices for the securities.

    prices = {
        "SPUS": 52.75,
        "SPWO": 21.45,
        "GLD": 215.30,
        "SPSK": 19.85,
        "SPRE": 23.60,
        "KAIF": 1.00  # Set initial price for KAIF
    }

    current_date = datetime.utcnow()
    for symbol, price in prices.items():
        security = securities[symbol]
        # Check if a price already exists for today (comparing by date only)
        existing_price = SecurityPrice.query.filter_by(
            security_id=security.id,
            date=current_date.date()
        ).first()
        if not existing_price:
            price_record = SecurityPrice(
                security_id=security.id,
                date=current_date,
                price=price
            )
            db.session.add(price_record)
    db.session.commit()
    print("Initial security prices added successfully")

print("All portfolios with allocations have been set up successfully!")
