from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

from . import db


class Customer(db.Model, UserMixin):
    """Customer model for users investing in portfolios"""

    __tablename__ = 'customers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255))
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define relationship - use backref
    investments = db.relationship('Investment', backref='customer', lazy='dynamic',
                                  cascade="all, delete-orphan")
    # Virtual attribute for the password
    @property
    def password(self):
        raise AttributeError('Password is not a readable attribute')

    @password.setter
    def password(self, password):
        self.password_hash = generate_password_hash(password)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def get_total_investment(self):
        return sum(investment.amount for investment in self.investments)

    def get_current_value(self):
        return sum(investment.get_current_value() for investment in self.investments)

    def get_total_return(self):
        total = self.get_total_investment()
        current = self.get_current_value()
        if total:
            return (current - total) / total * 100
        return 0
    #Add daily/monthly P&L methods? #TODO

    def __repr__(self):
        return f'<Customer {self.name}>'