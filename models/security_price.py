from datetime import datetime
from . import db

class SecurityPrice(db.Model):
    """Model representing a security's price record."""
    __tablename__ = 'security_prices'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    security_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('securities.id'), nullable=False)
    date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    price = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<SecurityPrice {self.security_id} on {self.date.strftime('%Y-%m-%d')}: ${self.price:.2f}>"
