from datetime import datetime, timedelta

from . import db, Investment
from .security_price import SecurityPrice


class Portfolio(db.Model):
    __tablename__ = 'portfolios'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    risk_level = db.Column(db.String(20))  # Low, Medium, High
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    investments = db.relationship('Investment', backref='portfolio', lazy='dynamic')
    allocations = db.relationship('PortfolioAllocation', backref='portfolio', lazy='dynamic')


    def get_subscription_nav(self, subscription_date):
        """
        Returns the official subscription NAV for investments on 'subscription_date'.
        All investments submitted on that day will be batched together at this price.
        If no previously funded investments exist, we return a default value of 1000.
        """
        from models.performance import PortfolioSnapshot
        start_day = datetime(subscription_date.year, subscription_date.month, subscription_date.day)
        end_day = start_day + timedelta(days=1)
        # Get the final snapshot on that day.
        snapshot = PortfolioSnapshot.query.filter(
            PortfolioSnapshot.portfolio_id == self.id,
            PortfolioSnapshot.date >= start_day,
            PortfolioSnapshot.date < end_day
        ).order_by(PortfolioSnapshot.date.desc()).first()
        # Fallback: use the latest snapshot before the subscription date.
        if not snapshot:
            snapshot = PortfolioSnapshot.query.filter(
                PortfolioSnapshot.portfolio_id == self.id,
                PortfolioSnapshot.date <= subscription_date
            ).order_by(PortfolioSnapshot.date.desc()).first()
        if not snapshot:
            return 1000  # Default if no snapshot is available.
        # Check for existing funded investments from previous days.
        previous_units = db.session.query(db.func.sum(Investment.units)).filter(
            Investment.portfolio_id == self.id,
            Investment.start_date < start_day,
            Investment.is_active == True,
            Investment.is_funded == True
        ).scalar()
        if not previous_units or previous_units <= 0:
            return 1000  # Use default if no prior data.
        return snapshot.total_value / previous_units


    def get_nav_per_unit(self, as_of_date=None):
        """
        Returns the NAV per unit for this portfolio.
        For investments created on the same day, the final (latest) snapshot of that day is used.
        """
        from models.performance import PortfolioSnapshot  # Import snapshot model

        if as_of_date is None:
            as_of_date = datetime.utcnow()

        # Lock the date to the calendar day (midnight)
        start_day = datetime(as_of_date.year, as_of_date.month, as_of_date.day)
        end_day = start_day + timedelta(days=1)

        # Use the final snapshot recorded on that day.
        snapshot = PortfolioSnapshot.query.filter(
            PortfolioSnapshot.portfolio_id == self.id,
            PortfolioSnapshot.date >= start_day,
            PortfolioSnapshot.date < end_day
        ).order_by(PortfolioSnapshot.date.desc()).first()

        # Fall back to the latest snapshot before as_of_date if none found for the day.
        if not snapshot:
            snapshot = PortfolioSnapshot.query.filter(
                PortfolioSnapshot.portfolio_id == self.id,
                PortfolioSnapshot.date <= as_of_date
            ).order_by(PortfolioSnapshot.date.desc()).first()

        # If no snapshot exists at all, return a default NAV.
        if not snapshot:
            return 1000

        # Calculate the total units from all active investments (if needed).
        total_units = db.session.query(db.func.sum(Investment.units)).filter(
            Investment.portfolio_id == self.id,
            Investment.start_date <= as_of_date,
            Investment.is_active == True
        ).scalar()

        if not total_units or total_units <= 0:
            single_inv = Investment.query.filter_by(portfolio_id=self.id, is_active=True).first()
            if single_inv and single_inv.units:
                total_units = single_inv.units
            else:
                return 1000

        return snapshot.total_value / total_units

    def get_total_value(self, as_of_date=None):
        """Get total portfolio value as of a specific date"""
        if as_of_date is None:
            as_of_date = datetime.utcnow()

        # Sum up all securities in the portfolio
        total_value = 0
        for allocation in self.allocations:
            security_value = allocation.security.get_value(as_of_date)
            total_value += security_value * (allocation.percentage / 100)

        return total_value

    def get_performance(self, start_date, end_date=None):
        """Calculate portfolio performance between dates"""
        if end_date is None:
            end_date = datetime.utcnow()

        start_value = self.get_total_value(start_date)
        end_value = self.get_total_value(end_date)

        if start_value == 0:
            return 0

        return (end_value - start_value) / start_value * 100

    def __repr__(self):
        return f'<Portfolio {self.name}>'


class Security(db.Model):
    """Security model representing individual securities (stocks, ETFs, etc.)"""

    __tablename__ = 'securities'

    id = db.Column(db.Integer, primary_key=True)
    symbol = db.Column(db.String(20), nullable=False, unique=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20))  # Stock, ETF, Bond, etc.
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define relationship - REMOVE back_populates
    allocations = db.relationship('PortfolioAllocation', backref='security', lazy='dynamic')

    def get_price(self, as_of_date=None):
        """Get security price as of a specific date"""

        if as_of_date is None:
            as_of_date = datetime.utcnow()

        # Get the price closest to the requested date
        price = SecurityPrice.query.filter(
            SecurityPrice.security_id == self.id,
            SecurityPrice.date <= as_of_date
        ).order_by(SecurityPrice.date.desc()).first()

        if price:
            return price.price
        return 0

    def get_value(self, as_of_date=None):
        """Get security value as of a specific date"""
        # Value is calculated in the context of a portfolio allocation
        return self.get_price(as_of_date)

    def __repr__(self):
        return f'<Security {self.symbol}>'



class PortfolioAllocation(db.Model):
    """Model representing the allocation of securities within a portfolio"""

    __tablename__ = 'portfolio_allocations'

    id = db.Column(db.Integer, primary_key=True)
    portfolio_id = db.Column(db.Integer, db.ForeignKey('portfolios.id'), nullable=False)
    security_id = db.Column(db.Integer, db.ForeignKey('securities.id'), nullable=False)
    percentage = db.Column(db.Float, nullable=False)  # Allocation percentage (0-100)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (
        db.UniqueConstraint('portfolio_id', 'security_id', name='uix_portfolio_security'),
    )

    # Relationships already defined by backref in Portfolio and Security

    def __repr__(self):
        return f'<PortfolioAllocation {self.portfolio_id}:{self.security_id}:{self.percentage}%>'