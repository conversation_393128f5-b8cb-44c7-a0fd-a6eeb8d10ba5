from datetime import datetime
from . import db

class Performance(db.Model):
    __tablename__ = 'performances'

    id = db.Column(db.Integer, primary_key=True)
    investment_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON>ey('investments.id'), nullable=False)
    date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    value = db.Column(db.Float, nullable=False)
    return_pct = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Use a string reference for Investment
 #   investment = db.relationship("Investment", back_populates="performances")

    def __repr__(self):
        return f'<Performance {self.investment_id} - Date: {self.date}, Value: ${self.value:.2f}, Return: {self.return_pct:.2f}%>'



class PortfolioSnapshot(db.Model):
    """Model for tracking portfolio composition and value over time"""

    __tablename__ = 'portfolio_snapshots'

    id = db.Column(db.Integer, primary_key=True)
    portfolio_id = db.Column(db.Integer, db.<PERSON>ey('portfolios.id'), nullable=False)
    date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    total_value = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    portfolio = db.relationship('Portfolio', backref=db.backref('snapshots', lazy='dynamic'))

    __table_args__ = (
        db.UniqueConstraint('portfolio_id', 'date', name='uix_portfolio_date'),
    )

    def __repr__(self):
        return f'<PortfolioSnapshot {self.portfolio_id} - Date: {self.date}, Value: ${self.total_value:.2f}>'
