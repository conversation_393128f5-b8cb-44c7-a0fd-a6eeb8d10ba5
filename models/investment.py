from datetime import datetime
from . import db

class Investment(db.Model):
    """Model representing a customer's investment in a specific portfolio"""
    __tablename__ = 'investments'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('customers.id'), nullable=False)
    portfolio_id = db.Column(db.Integer, db.ForeignKey('portfolios.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # Initial investment amount
    start_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    end_date = db.Column(db.DateTime)  # If investment is closed
    is_active = db.Column(db.Bo<PERSON>, default=True)
    is_funded = db.Column(db.Bo<PERSON>an, default=False)  # Indicates if the investment is funded/activated
    is_paper = db.Column(db.<PERSON>, default=False)   # Indicates paper trading investment
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Use backref instead of back_populates:
    performances = db.relationship('Performance', backref='investment', lazy='dynamic')

    # Additional fields for unit-based accounting:
    units = db.Column(db.Float, nullable=True)         # Number of units purchased
    entry_nav = db.Column(db.Float, nullable=True)       # Locked subscription NAV
    high_water_mark = db.Column(db.Float, nullable=True, default=0.0)
    entry_fee_applied = db.Column(db.Boolean, default=False)
    exit_fee_applied = db.Column(db.Boolean, default=False)

    def redeem(self, redeem_amount):
        """
        Redeem part of this investment. This reduces the number of units accordingly.
        """
        # Get current NAV per unit (you might create a helper method for this; here we use the portfolio's method)
        current_nav = self.portfolio.get_nav_per_unit(datetime.utcnow())
        if current_nav <= 0:
            raise ValueError("Current NAV is not valid for redemption.")

        redeemed_units = redeem_amount / current_nav
        if redeemed_units > self.units:
            raise ValueError("Redemption amount exceeds your current investment value.")

        self.units -= redeemed_units
        self.amount -= redeem_amount
        # Optionally log or record the redemption event

    def get_current_value(self):
        from models.portfolio import Portfolio
        # Current value is calculated based on fixed units and the current subscription NAV.
        # Here we use the portfolio's get_subscription_nav; adjust as needed.
        current_nav = self.portfolio.get_subscription_nav(datetime.utcnow())
        return self.units * current_nav if self.units else 0

    def get_return(self):
        cv = self.get_current_value()
        return (cv - self.amount) / self.amount * 100 if self.amount else 0

    def get_return_from_snapshots(self):
        from models.performance import PortfolioSnapshot
        from models import db
        from flask import current_app

        current_app.logger.info(f"[DEBUG] get_return_from_snapshots() called for Investment ID={self.id}, "
                                  f"Portfolio ID={self.portfolio_id}, amount={self.amount}, start_date={self.start_date}")

        # Use the earliest available snapshot for the portfolio as the start snapshot,
        # regardless of whether it is before the investment start date.
        start_snapshot = PortfolioSnapshot.query.filter(
            PortfolioSnapshot.portfolio_id == self.portfolio_id
        ).order_by(PortfolioSnapshot.date.asc()).first()

        latest_snapshot = PortfolioSnapshot.query.filter_by(
            portfolio_id=self.portfolio_id
        ).order_by(PortfolioSnapshot.date.desc()).first()

        if not start_snapshot:
            current_app.logger.info(f"[DEBUG] No snapshot available for portfolio {self.portfolio_id}")
            return 0
        if not latest_snapshot:
            current_app.logger.info(f"[DEBUG] No latest snapshot available for portfolio {self.portfolio_id}")
            return 0

        current_app.logger.info(f"[DEBUG] start_snapshot date={start_snapshot.date}, total_value={start_snapshot.total_value}")
        current_app.logger.info(f"[DEBUG] latest_snapshot date={latest_snapshot.date}, total_value={latest_snapshot.total_value}")

        total_at_start = db.session.query(db.func.sum(Investment.amount)).filter(
            Investment.portfolio_id == self.portfolio_id,
            Investment.is_active == True,
            Investment.start_date <= self.start_date
        ).scalar() or 0

        total_latest = db.session.query(db.func.sum(Investment.amount)).filter(
            Investment.portfolio_id == self.portfolio_id,
            Investment.is_active == True
        ).scalar() or 0

        current_app.logger.info(f"[DEBUG] total_at_start={total_at_start}, total_latest={total_latest}")

        if total_at_start == 0 or total_latest == 0:
            current_app.logger.info("[DEBUG] Invalid totals; returning 0")
            return 0

        ownership_start = self.amount / total_at_start
        ownership_latest = self.amount / total_latest

        value_at_start = start_snapshot.total_value * ownership_start
        value_latest = latest_snapshot.total_value * ownership_latest

        current_app.logger.info(f"[DEBUG] value_at_start={value_at_start}, value_latest={value_latest}")

        try:
            return_percentage = ((value_latest - value_at_start) / value_at_start) * 100
            current_app.logger.info(f"[DEBUG] Calculated return_percentage={return_percentage}")
            return return_percentage
        except Exception as e:
            current_app.logger.error(f"[DEBUG] Exception in get_return_from_snapshots() for Investment ID={self.id}: {str(e)}")
            return 0

    def get_performance_history(self):
        return self.performances.order_by('date').all()

    def close_investment(self):
        self.is_active = False
        self.end_date = datetime.utcnow()

    def __repr__(self):
        return f'<Investment {self.id} - Customer: {self.customer_id}, Portfolio: {self.portfolio_id}, Amount: ${self.amount:.2f}>'
