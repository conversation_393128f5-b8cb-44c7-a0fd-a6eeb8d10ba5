from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

# Ensure that the Performance model is imported first so that
# the Investment model’s relationship (back_populates="performances") is defined.
from .performance import Performance
from .investment import Investment
from .portfolio import Portfolio, Security, PortfolioAllocation
from .models_customer import Customer

# Configure all mappers after all models have been imported.
db.configure_mappers()
