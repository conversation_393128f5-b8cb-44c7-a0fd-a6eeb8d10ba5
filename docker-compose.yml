version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_APP=app.py
      - FLASK_CONFIG=production
      - DATABASE_URI=sqlite:///portfolio_dashboard.db
      - SECRET_KEY=${SECRET_KEY:-your-secure-secret-key}
      - ADMIN_EMAIL=${ADMIN_EMAIL:-<EMAIL>}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin}
    volumes:
      - ./uploads:/app/uploads
      - ./data:/app/data
      - ./instance:/app/instance
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s 