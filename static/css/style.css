:root {
    --teal-grean: #055A57;
    --tropical-teal: #00917C;
    --tropical-green2: #49B0A1;
    --peach-yellow: #FFCE7B;
    --charchol-black: #141414;
    --aqua-glow: #00FFDA;    
    --blue-dark: #0a2463;
    --blue-medium: #1e88e5;
    --blue-light: #64b5f6;
    --green-main: #51B662;
    --orange-main: #f28e44;
    --red-main: #C83540;
    --gray-dark: #2c3e50;
    --gray-medium: #64748b;
    --gray-light: #e0e0e0;
    --background-light: #f8f9fa;
    --background-dark: #0f172a;
    --card-bg: #ffffff;
    --bs-info-text-emphasis: #055A57;
    --bs-info-bg-subtle: #ffe7bd;
}

.btn-outline-danger {
    --bs-btn-color: var(--red-main) ;
    --bs-btn-border-color: var(--red-main) ;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: var(--red-main) ;
    --bs-btn-hover-border-color: var(--red-main) ;
    --bs-btn-focus-shadow-rgb: 220, 53, 69;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: var(--red-main) ;
    --bs-btn-active-border-color: var(--red-main) ;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: var(--red-main) ;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color:var(--red-main) ;
    --bs-gradient: none;
}

.btn-outline-primary {
    --bs-btn-color: var(--tropical-teal);
    --bs-btn-border-color: var(--tropical-teal);
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: var(--tropical-teal);
    --bs-btn-hover-border-color: var(--tropical-teal);
    --bs-btn-focus-shadow-rgb: 13, 110, 253;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: var(--tropical-teal);
    --bs-btn-active-border-color: var(--tropical-teal);
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: var(--tropical-teal);
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: var(--tropical-teal);
    --bs-gradient: none;
}

btn-check:checked+.btn, .btn.active, .btn.show, .btn:first-child:active, :not(.btn-check)+.btn:active {
    color: var(--dark-bg);
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}


body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--gray-dark);
    background-color: #f5f7fa;
    line-height: 1.6;
}

.card {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

.card-header.bg-dark {
    background: linear-gradient(to right, var(--teal-grean), var(--tropical-green2)) !important;
    color: white;
}

.card-body {
    padding: 1.5rem;
}

.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.table {
    --bs-table-bg: transparent;
    --bs-table-striped-color: #212529;
    --bs-table-striped-bg: rgba(0, 0, 0, 0.02);
    --bs-table-active-color: #212529;
    --bs-table-active-bg: rgba(0, 0, 0, 0.1);
    --bs-table-hover-color: #212529;
    --bs-table-hover-bg: rgba(0, 0, 0, 0.04);
    border-color: var(--gray-light);
}

.table thead th {
    font-weight: 600;
    border-top: none;
    border-bottom: 2px solid var(--gray-light);
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
    --bs-table-accent-bg: rgba(0, 0, 0, 0.02);
}

.table-hover > tbody > tr:hover > * {
    --bs-table-accent-bg: rgba(0, 0, 0, 0.04);
}

.btn {
    font-weight: 500;
    padding: 0.6rem 1.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    background-color: var(--green-main);
    border-color: var(--green-main);
}

.btn-success:hover {
    background-color: #2e7d32;
    border-color: #2e7d32;
}

.btn-danger {
    background-color: var(--red-main);
    border-color: var(--red-main);
}

.btn-danger:hover {
    background-color: var(--red-main);
    border-color: var(--red-main);
}

.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.375rem;
}

.badge.bg-success {
    background-color: var(--tropical-teal) !important;
}

.badge.bg-danger {
    background-color: var(--red-main) !important;
}

.badge.bg-warning {
    background-color: var(--orange-main) !important;
}

.form-control, .form-select {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    border: 1px solid var(--gray-light);
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--blue-medium);
    box-shadow: 0 0 0 0.25rem rgba(30, 136, 229, 0.25);
}

/* Stats Cards */
.stat-card {
    padding: 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    height: 100%;
}

.stat-card .stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 1rem;
    color: var(--gray-medium);
}

.bg-gradient-primary {
    background: linear-gradient(to right, var(--blue-dark), var(--blue-medium));
    color: white;
}

.bg-gradient-success {
    background: linear-gradient(45deg, #055A57, #49B0A1);
    color: white;
}

.bg-gradient-danger {
    background: linear-gradient(to right, #c62828, var(--red-main));
    color: white;
}

.bg-gradient-info {
    background: linear-gradient(to right, #0277bd, #29b6f6);
    color: white;
}

/* Performance indicators */
.performance-indicator {
    display: flex;
    align-items: center;
}

.performance-indicator .indicator-arrow {
    margin-right: 0.5rem;
}

.performance-indicator.positive {
    color: var(--green-main);
}

.performance-indicator.negative {
    color: var(--red-main);
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }

/* For dark theme compatibility */
.dark-theme {
    --card-bg: #1e293b;
    --background-light: #0f172a;
    --gray-dark: #e2e8f0;
    --gray-medium: #94a3b8;
    --gray-light: #334155;
    color: var(--gray-dark);
    background-color: #0f172a;
}

.dark-theme .card {
    background-color: var(--card-bg);
}

.dark-theme .card-header {
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--gray-light);
}

.dark-theme .table {
    color: var(--gray-dark);
}

.dark-theme .table thead th {
    border-bottom: 2px solid var(--gray-light);
}

.dark-theme .table-striped > tbody > tr:nth-of-type(odd) > * {
    --bs-table-accent-bg: rgba(255, 255, 255, 0.05);
}

.dark-theme .table-hover > tbody > tr:hover > * {
    --bs-table-accent-bg: rgba(255, 255, 255, 0.1);
}

.dark-theme .form-control,
.dark-theme .form-select {
    background-color: var(--card-bg);
    border-color: var(--gray-light);
    color: var(--gray-dark);
}
