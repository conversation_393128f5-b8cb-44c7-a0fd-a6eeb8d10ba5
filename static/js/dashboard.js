// Enhanced dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Format numbers with commas
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    // Apply number formatting to elements with class 'format-number'
    document.querySelectorAll('.format-number').forEach(function(el) {
        const value = parseFloat(el.textContent);
        if (!isNaN(value)) {
            el.textContent = formatNumber(value.toFixed(2));
        }
    });

    // Add animation to elements with class 'animate-fade-in'
    document.querySelectorAll('.animate-fade-in').forEach(function(el, index) {
        el.style.opacity = '0';
        el.style.animation = 'none';
        setTimeout(function() {
            el.style.animation = '';
        }, 100 + (index * 100));
    });

    // Enhanced Chart.js defaults for better visuals
    if (typeof Chart !== 'undefined') {
        Chart.defaults.font.family = "'Inter', sans-serif";
        Chart.defaults.color = document.body.classList.contains('dark-theme') ? '#94a3b8' : '#64748b';
        Chart.defaults.borderColor = document.body.classList.contains('dark-theme') ? '#334155' : '#e0e0e0';

        // Define gradient for line charts
        Chart.defaults.elements.line.borderWidth = 2;
        Chart.defaults.elements.line.tension = 0.4;
        Chart.defaults.elements.point.radius = 3;
        Chart.defaults.elements.point.hoverRadius = 5;

        // Use the specified colors for the theme
        Chart.defaults.plugins.tooltip.backgroundColor = document.body.classList.contains('dark-theme') ? '#1e293b' : 'rgba(255, 255, 255, 0.9)';
        Chart.defaults.plugins.tooltip.titleColor = document.body.classList.contains('dark-theme') ? '#e2e8f0' : '#2c3e50';
        Chart.defaults.plugins.tooltip.bodyColor = document.body.classList.contains('dark-theme') ? '#e2e8f0' : '#2c3e50';
        Chart.defaults.plugins.tooltip.borderColor = document.body.classList.contains('dark-theme') ? '#334155' : '#e0e0e0';
        Chart.defaults.plugins.tooltip.borderWidth = 1;
        Chart.defaults.plugins.tooltip.padding = 10;
        Chart.defaults.plugins.tooltip.displayColors = true;
        Chart.defaults.plugins.tooltip.boxPadding = 5;
        Chart.defaults.plugins.tooltip.usePointStyle = true;
        Chart.defaults.plugins.tooltip.callbacks = {
            label: function(context) {
                let label = context.dataset.label || '';
                if (label) {
                    label += ': ';
                }
                if (context.parsed.y !== null) {
                    if (context.dataset.isCurrency) {
                        label += '$' + context.parsed.y.toLocaleString();
                    } else if (context.dataset.isPercentage) {
                        label += context.parsed.y.toFixed(2) + '%';
                    } else {
                        label += context.parsed.y.toLocaleString();
                    }
                }
                return label;
            }
        };
    }
});

// Function to create gradient backgrounds for charts
function createGradient(ctx, startColor, endColor) {
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, startColor);
    gradient.addColorStop(1, endColor);
    return gradient;
}

// Function to create advanced charts with ApexCharts if available
function createAdvancedChart(elementId, type, options) {
    if (typeof ApexCharts !== 'undefined' && document.getElementById(elementId)) {
        const chart = new ApexCharts(document.getElementById(elementId), {
            chart: {
                type: type,
                fontFamily: 'Inter, sans-serif',
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    },
                    autoSelected: 'zoom'
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800
                }
            },
            ...options
        });
        chart.render();
        return chart;
    }
    return null;
}