/**
 * Portfolio JavaScript for Portfolio Dashboard
 * Handles portfolio-specific functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Portfolio JS loaded');

    // Initialize portfolio allocation chart
    initializeAllocationChart();

    // Setup portfolio allocation form
    setupAllocationForm();

    // Handle investment simulation
    setupInvestmentSimulation();
});

/**
 * Initialize the portfolio allocation chart
 */
function initializeAllocationChart() {
    const allocationCanvas = document.getElementById('portfolioAllocationChart');

    if (!allocationCanvas) {
        return;
    }

    const portfolioId = allocationCanvas.dataset.portfolioId;

    if (!portfolioId) {
        console.error('Portfolio ID not specified for allocation chart');
        return;
    }

    // Fetch allocation data from API
    fetch(`/api/portfolio/${portfolioId}/allocations`)
        .then(response => response.json())
        .then(data => {
            // Create the chart
            const ctx = allocationCanvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.labels,
                    datasets: [{
                        data: data.values,
                        backgroundColor: data.colors || [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(201, 203, 207, 0.7)',
                            'rgba(255, 99, 71, 0.7)',
                            'rgba(50, 205, 50, 0.7)',
                            'rgba(138, 43, 226, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed || 0;
                                    return label + ': ' + value + '%';
                                }
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error loading allocation data:', error);
            allocationCanvas.parentNode.innerHTML = '<div class="alert alert-danger">Failed to load allocation data</div>';
        });
}

/**
 * Setup the portfolio allocation form
 */
function setupAllocationForm() {
    const allocationForm = document.getElementById('portfolioAllocationForm');

    if (!allocationForm) {
        return;
    }

    // Track total allocation
    const allocations = document.querySelectorAll('.allocation-input');
    let totalDisplay = document.getElementById('totalAllocation');

    // Update total when an allocation changes
    allocations.forEach(input => {
        input.addEventListener('input', updateTotal);
    });

    // Function to update total allocation
    function updateTotal() {
        let total = 0;

        allocations.forEach(input => {
            const value = parseFloat(input.value) || 0;
            total += value;
        });

        if (totalDisplay) {
            totalDisplay.textContent = total.toFixed(2) + '%';

            // Highlight if not 100%
            if (Math.abs(total - 100) > 0.01) {
                totalDisplay.classList.add('text-danger');
                totalDisplay.classList.add('fw-bold');

                // Disable submit button if total is not 100%
                const submitButton = allocationForm.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.disabled = true;
                }
            } else {
                totalDisplay.classList.remove('text-danger');
                totalDisplay.classList.remove('fw-bold');

                // Enable submit button if total is 100%
                const submitButton = allocationForm.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.disabled = false;
                }
            }
        }
    }

    // Run once at start to set initial state
    updateTotal();
}

/**
 * Setup investment simulation functionality
 */
function setupInvestmentSimulation() {
    const simulationForm = document.getElementById('investmentSimulationForm');

    if (!simulationForm) {
        return;
    }

    simulationForm.addEventListener('submit', function(event) {
        event.preventDefault();

        const amount = document.getElementById('simulationAmount').value;
        const portfolioId = document.getElementById('simulationPortfolioId').value;
        const duration = document.getElementById('simulationDuration').value;

        // Validate inputs
        if (!amount || isNaN(amount) || amount <= 0) {
            alert('Please enter a valid investment amount');
            return;
        }

        // Prepare data for API request
        const data = {
            portfolio_id: portfolioId,
            amount: amount,
            duration: duration
        };

        // Show loading indicator
        const resultsContainer = document.getElementById('simulationResults');
        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">Running simulation...</p></div>';

        // Send API request to simulate investment
        fetch('/api/simulate-investment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            displaySimulationResults(result);
        })
        .catch(error => {
            console.error('Error simulating investment:', error);
            resultsContainer.innerHTML = '<div class="alert alert-danger">Failed to simulate investment. Please try again.</div>';
        });
    });
}

/**
 * Display investment simulation results
 * @param {object} result - The simulation results from the API
 */
function displaySimulationResults(result) {
    const resultsContainer = document.getElementById('simulationResults');

    if (!resultsContainer) {
        console.error('Results container not found');
        return;
    }

    if (result.status === 'success') {
        // Create HTML to display the results
        let html = `
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Investment Simulation Results</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Initial Investment</h6>
                                    <h4 class="mb-0">$${parseFloat(result.initial_amount).toLocaleString()}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Projected Value</h6>
                                    <h4 class="mb-0">$${parseFloat(result.projected_value).toLocaleString()}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Projected Return</h6>
                                    <h4 class="mb-0 ${result.projected_return >= 0 ? 'text-success' : 'text-danger'}">${parseFloat(result.projected_return).toFixed(2)}%</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container" style="height: 300px;">
                        <canvas id="simulationChart"></canvas>
                    </div>
                </div>
                <div class="card-footer">
                    <p class="mb-0 small text-muted">This simulation is based on historical performance and is not a guarantee of future returns.</p>
                </div>
            </div>
        `;

        resultsContainer.innerHTML = html;

        // Create chart
        const ctx = document.getElementById('simulationChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: result.timeline.map(item => item.date),
                datasets: [{
                    label: 'Projected Value',
                    data: result.timeline.map(item => item.value),
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += '$' + context.parsed.y.toLocaleString();
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    } else {
        // Display error message
        resultsContainer.innerHTML = `
            <div class="alert alert-danger">
                <h5>Simulation Error</h5>
                <p>${result.message || 'An error occurred during simulation.'}</p>
            </div>
        `;
    }
}