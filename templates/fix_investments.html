{% extends "base.html" %}

{% block title %}Fix Investments - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>Fix Customer Investments</h1>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Link Customers to Portfolio</h5>
    </div>
    <div class="card-body">
        {% if portfolios %}
            <form method="POST" action="{{ url_for('admin.fix_investments') }}">
                <div class="mb-3">
                    <label for="portfolio_id" class="form-label">Select Portfolio</label>
                    <select class="form-select" id="portfolio_id" name="portfolio_id">
                        {% for id, name in portfolios %}
                            <option value="{{ id }}">{{ name }} (ID: {{ id }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label for="amount" class="form-label">Investment Amount</label>
                    <input type="number" class="form-control" id="amount" name="amount" value="100000">
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This will create or update investment records for all customers in the selected portfolio.
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">Fix Investments</button>
                </div>
            </form>
        {% else %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                No portfolios with snapshots found. You need to import at least one IBKR report first.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}