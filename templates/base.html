<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Portfolio Dashboard{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- ApexCharts for advanced charts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <!-- Fav icon -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.png') }}">


    <style>
        :root {
            /* --teal-grean: #055A57;
            --tropical-teal: #00917C;
            --peach-yellow: #FFCE7B;
            --charchol-black: #141414;
            --aqua-glow: #00FFDA; */

            /* --primary-color: #0a2463;
            --secondary-color: #247ba0;
            --accent-color: #1e88e5; */
            --primary-color: #055A57;
            --secondary-color: #00917C;
            --accent-color: #FFCE7B;
            --orange-main: #f28e44;
            --success-color: #24A18F;
            --warning-color: #ff9800;
            --danger-color: var(--red-main) ;
            --dark-bg: #141414;
            --card-bg: #FFCE7B;
            --light-bg: #ffffff;
            --text-primary: #141414;
            --text-secondary: #FFCE7B;
            --border-color: #e0e0e0;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.04);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
        }

        body {
            font-family: 'Montserrat', sans-serif;
            color: var(--text-primary);
            background-color: #f5f7fa;
            line-height: 1.6;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
        }
        

        .navbar {
            padding: 1rem 0;
            box-shadow: var(--shadow-sm);
            background-color: var(--primary-color);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            position: relative;
        }

        .navbar-brand::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-color), transparent);
        }

        .nav-link {
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 0.25rem;
        }

        .nav-link:hover {
            color: white;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 50%;
            background-color: var(--accent-color);
            transition: all 0.3s ease;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 80%;
            left: 10%;
        }

        .dropdown-menu {
            border: none;
            box-shadow: var(--shadow-md);
            border-radius: 0.5rem;
            padding: 0.5rem;
        }

        .dropdown-item {
            padding: 0.75rem 1.25rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background-color: rgba(0,0,0,0.05);
        }

        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: var(--shadow-md);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            font-weight: 600;
            border-bottom: 1px solid var(--border-color);
            background-color: white;
            padding: 1.25rem 1.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            font-weight: 500;
            padding: 0.6rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .bg-warning {
            --bs-bg-opacity: 1;
            background-color: var(--orange-main) !important;
        }

        .alert {
            border: none;
            border-radius: 0.5rem;
            padding: 1rem 1.5rem;
            box-shadow: var(--shadow-sm);
        }

        footer {
            background-color: var(--primary-color);
            padding: 2.5rem 0;
            margin-top: 5rem;
            font-family: 'Montserrat', sans-serif;
        }
        footer h5 {
            color: white;  /* Changed from var(--accent-color) to white */
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 1.25rem;
        }
        footer p {
            color: white;
            font-size: 0.95rem;
            opacity: 0.9;
        }
        footer .list-inline-item {
            margin-right: 1.5rem;
        }
        footer .list-inline-item:last-child {
            margin-right: 0;
        }
        footer .list-inline-item a {
            color: var(--secondary-color);
            transition: color 0.3s ease;
        }
        footer .list-inline-item a:hover {
            color: var(--accent-color);
        }
        footer .text-muted {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        .theme-toggle {
            cursor: pointer;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            background-color: rgba(255,255,255,0.1);
            margin-right: 1rem;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background-color: rgba(255,255,255,0.2);
        }

        /* Table Styles */
        .table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
        }

        .table th {
            font-weight: 600;
            padding: 1rem;
            background-color: rgba(0,0,0,0.02);
            border-bottom: 2px solid var(--border-color);
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:hover {
            background-color: rgba(0,0,0,0.01);
        }

        /* Badge Styles */
        .badge {
            padding: 0.35em 0.65em;
            font-weight: 500;
            border-radius: 0.375rem;
        }

        /* Form Controls */
        .form-control, .form-select {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.25rem rgba(30, 136, 229, 0.25);
        }

        /* Dark theme classes - can be toggled with JS */
        .dark-theme {
            --card-bg: #1e293b;
            --light-bg: #0f172a;
            --text-primary: #e2e8f0;
            --text-secondary: #94a3b8;
            --border-color: #334155;
            color: var(--text-primary);
            background-color: var(--light-bg);
        }
        .dark-theme h1, .dark-theme h2, .dark-theme h3, .dark-theme h4, .dark-theme h5, .dark-theme h6, .dark-theme .navbar-brand, .dark-theme .nav-link, .dark-theme .card-header, .dark-theme .action-title, .dark-theme .card-value, .dark-theme .card-label, .dark-theme .market-name, .dark-theme .market-value, .dark-theme .welcome-header, .dark-theme .main-title, .dark-theme .main-text {
            color: #fff !important;
        }
        .dark-theme .navbar {
            background-color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
        }

        .dark-theme .card,
        .dark-theme .dropdown-menu,
        .dark-theme .action-tile {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
        }
        .dark-theme .card-header {
            background-color: var(--card-bg);
            border-bottom: 1px solid var(--border-color);
        }
        .dark-theme .list-group-item {
            background-color: var(--card-bg);
            border-color: var(--border-color);
            color: var(--secondary-color) !important;
        }
        .dark-theme .dropdown-item {
            color: var(--text-primary);
        }
        .dark-theme .dropdown-item:hover {
            background-color: rgba(255,255,255,0.05);
        }
        .dark-theme .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }
        .dark-theme .btn-outline-secondary:hover,
        .dark-theme .btn-outline-secondary.active {
            background-color: var(--border-color);
            color: var(--text-primary);
            border-color: var(--border-color);
        }
        .dark-theme .text-muted {
            color: #9ca3af !important;
        }
        .dark-theme .welcome-header {
            background: linear-gradient(to right, rgba(26, 31, 46, 0.95), rgba(17, 24, 39, 0.95));
            color: var(--text-primary);
        }
        .dark-theme .table th {
            background-color: rgba(255,255,255,0.02);
        }
        .dark-theme .table tr:hover {
            background-color: rgba(255,255,255,0.03);
        }
        .dark-theme .form-control,
        .dark-theme .form-select {
            background-color: var(--light-bg);
            border-color: var(--border-color);
            color: var(--text-primary);
        }
        .dark-theme .form-control:focus,
        .dark-theme .form-select:focus {
            background-color: var(--light-bg);
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.25rem rgba(255, 206, 123, 0.15);
        }
        .dark-theme .alert-info {
            background-color: rgb(80 182 98);
            color: #0f172a;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .dark-theme .fas.fa-info-circle {
            color: #055A57;
        }
        /* Improve dark mode scrollbar */
        .dark-theme ::-webkit-scrollbar {
            width: 14px;
        }
        .dark-theme ::-webkit-scrollbar-track {
            background-color: var(--light-bg);
        }
        .dark-theme ::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border: 3px solid var(--light-bg);
            border-radius: 7px;
        }
        .dark-theme ::-webkit-scrollbar-thumb:hover {
            background-color: #4b5563;
        }

        .dark-theme .table th {
            background-color: rgba(255,255,255,0.05);
            border-bottom: 2px solid var(--border-color);
        }

        .dark-theme .table td {
            border-bottom: 1px solid var(--border-color);
        }

        .dark-theme .table tr:hover {
            background-color: rgba(255,255,255,0.02);
        }

        .fa-chart-pie.icon-green-main {
        color: var(--secondary-color) !important;
    }
    
        .dark-theme h1, .dark-theme h2, .dark-theme h3, .dark-theme h4, .dark-theme h5, .dark-theme h6, .dark-theme .navbar-brand, .dark-theme .nav-link, .dark-theme .card-header, .dark-theme .action-title, .dark-theme .card-value, .dark-theme .card-label, .dark-theme .market-name, .dark-theme .market-value, .dark-theme .welcome-header, .dark-theme .main-title, .dark-theme .main-text {
        color: #fff !important;
        }
        .dark-theme li {
        color: #fff !important;
        }
        .dark-theme .form-check-label {
            color: #000 !important;
        }
        .dark-theme p {
        color: #fff !important;
         }


        .dark-theme .fa-chart-pie.icon-green-main {
            color: var(--secondary-color) !important;
        }

        .fa-file-alt.text-info {
            color: var(--secondary-color) !important;
        }

        .dark-theme .fa-file-alt.text-info {
            color: var(--secondary-color) !important;
        }

        .dark-theme .form-control,
        .dark-theme .form-select {
            background-color: var(--card-bg);
            border-color: var(--border-color);
            color: var(--text-primary);
        }
    </style>

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{% if current_user.is_authenticated %}
                {% if current_user.is_admin %}
                    {{ url_for('admin.dashboard') }}
                {% else %}
                    {{ url_for('customer.dashboard') }}
                {% endif %}
            {% else %}
                {{ url_for('index') }}
            {% endif %}">
        </a>
        <img src="{{url_for('static', filename='img/KAM-logo.png')}}" width="210" align="middle" />
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto align-items-center">
                    <li class="nav-item me-2">
                        <div class="theme-toggle" id="themeToggle">
                            <i class="fas fa-moon"></i>
                        </div>
                    </li>

                    {% if current_user.is_authenticated %}
                        {% if current_user.is_admin %}
                            <!-- Admin Navigation -->
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}" href="{{ url_for('admin.dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'admin.customers' %}active{% endif %}" href="{{ url_for('admin.customers') }}">
                                    <i class="fas fa-users me-1"></i> Clients
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'admin.portfolios' %}active{% endif %}" href="{{ url_for('admin.portfolios') }}">
                                    <i class="fas fa-chart-pie me-1"></i> Portfolios
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i> Management
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
<!--                                    <li><a class="dropdown-item" href="{{ url_for('admin.securities') }}"><i class="fas fa-chart-line me-2"></i> Securities</a></li>-->
                                    <li><a class="dropdown-item" href="{{ url_for('admin.all_investments') }}"><i class="fas fa-money-bill-wave me-2"></i> All Investments</a></li>
<!--                                    <li><a class="dropdown-item" href="{{ url_for('admin.import_report') }}"><i class="fas fa-file-import me-2"></i> Import Reports</a></li>-->
                                </ul>
                            </li>
                        {% else %}
                            <!-- Customer Navigation -->
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'customer.dashboard' %}active{% endif %}" href="{{ url_for('customer.dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'customer.investments' %}active{% endif %}" href="{{ url_for('customer.investments') }}">
                                    <i class="fas fa-money-bill-wave me-1"></i> Investments
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'customer.portfolios' %}active{% endif %}" href="{{ url_for('customer.portfolios') }}">
                                    <i class="fas fa-chart-pie me-1"></i> Portfolios
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.endpoint == 'customer.reports' %}active{% endif %}" href="{{ url_for('customer.reports') }}">
                                    <i class="fas fa-file-alt me-1"></i> Reports
                                </a>
                            </li>
                        {% endif %}

                        <!-- User Menu -->
                        <li class="nav-item dropdown ms-2">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="avatar-circle me-2">
                                    <span class="avatar-initials">{{ current_user.name[:1] }}</span>
                                </div>
                                <span>
                                    {% if current_user.is_admin %}
                                        <span class="badge bg-danger me-1">Admin</span>
                                    {% endif %}
                                    {{ current_user.name }}
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
<!--                                <li><a class="dropdown-item" href="#"><i class="fas fa-user-circle me-2"></i> Profile</a></li>-->
<!--                                <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i> Preferences</a></li>-->
<!--                                <li><a class="dropdown-item" href="#"><i class="fas fa-bell me-2"></i> Notifications</a></li>-->
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'auth.login' %}active{% endif %}" href="{{ url_for('auth.login') }}">
                                <i class="fas fa-sign-in-alt me-1"></i> Sign In
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'auth.register' %}active{% endif %}" href="{{ url_for('auth.register') }}">
                                <i class="fas fa-user-plus me-1"></i> Register
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {% if category == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                        {% elif category == 'danger' %}
                            <i class="fas fa-exclamation-circle me-2"></i>
                        {% elif category == 'warning' %}
                            <i class="fas fa-exclamation-triangle me-2"></i>
                        {% elif category == 'info' %}
                            <i class="fas fa-info-circle me-2"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <h5 class="mb-3"><img src="{{url_for('static', filename='img/KAM-logo.png')}}" width="200" align="middle" /></h5>
                    <p class="mb-0 text-muted">Advanced portfolio management and analytics platform for institutional investors.</p>
                </div>
                <div class="col-md-4 text-center">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#" class="text-decoration-none text-muted"><i class="fab fa-linkedin fa-lg"></i></a></li>
                        <li class="list-inline-item"><a href="#" class="text-decoration-none text-muted"><i class="fab fa-twitter fa-lg"></i></a></li>
                        <li class="list-inline-item"><a href="#" class="text-decoration-none text-muted"><i class="fab fa-github fa-lg"></i></a></li>
                    </ul>
                </div>
                <div class="col-md-4 text-md-end">
                    <p class="mb-0 text-muted">&copy; {{ now.year }} Keheilan. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>

    <script>
        // Setup theme toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');
            const body = document.body;

            // Check for saved theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                body.classList.add('dark-theme');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            }

            // Toggle theme when clicked
            themeToggle.addEventListener('click', function() {
                if (body.classList.contains('dark-theme')) {
                    body.classList.remove('dark-theme');
                    themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                    localStorage.setItem('theme', 'light');
                } else {
                    body.classList.add('dark-theme');
                    themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                    localStorage.setItem('theme', 'dark');
                }
            });

            // Add avatar circle styles dynamically
            const style = document.createElement('style');
            style.textContent = `
                .avatar-circle {
                    width: 32px;
                    height: 32px;
                    background-color: var(--accent-color);
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .avatar-initials {
                    color: white;
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 1;
                }
            `;
            document.head.appendChild(style);
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>

