{% extends "base.html" %}

{% block title %}{{ portfolio.name }} - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ portfolio.name }}</h1>
            <div>
                <a href="{{ url_for('customer.portfolios') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left"></i> Back to Portfolios
                </a>
<!--                {% if not investment %}-->
<!--                    <a href="{{ url_for('customer.new_investment') }}?portfolio_id={{ portfolio.id }}" class="btn btn-primary">-->
<!--                        <i class="fas fa-plus"></i> Invest Now-->
<!--                    </a>-->
<!--                {% else %}-->
                    <a href="{{ url_for('customer.investment_detail', investment_id=investment.id) }}" class="btn btn-success">
                        <i class="fas fa-eye"></i> View Your Investment
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Portfolio Overview -->
<div class="row mb-4">
    <div class="col-md-7">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Portfolio Overview</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th style="width: 40%">Risk Level:</th>
                                    <td>
                                        <span class="badge {{ 'bg-danger' if portfolio.risk_level == 'High' else 'bg-warning' if portfolio.risk_level == 'Medium' else 'bg-success' }}">
                                            {{ portfolio.risk_level }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Expected Return:</th>
                                    <td>
                                        {% if portfolio.risk_level == 'High' %}
                                            10-15%+ annually
                                        {% elif portfolio.risk_level == 'Medium' %}
                                            6-10% annually
                                        {% else %}
                                            3-6% annually
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Recommended For:</th>
                                    <td>
                                        {% if portfolio.risk_level == 'High' %}
                                            Long-term investors (7+ years)
                                        {% elif portfolio.risk_level == 'Medium' %}
                                            Medium-term investors (3-7 years)
                                        {% else %}
                                            Short-term investors (1-3 years)
                                        {% endif %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th style="width: 40%">Created:</th>
                                    <td>{{ portfolio.created_at.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>Last Updated:</th>
                                    <td>{{ portfolio.updated_at.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>Securities:</th>
                                    <td>{{ allocations|length }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                {% if portfolio.description %}
                    <h6 class="fw-bold mb-2">Portfolio Description</h6>
                    <p>{{ portfolio.description }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-5">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Portfolio Allocation</h5>
            </div>
            <div class="card-body">
                {% if allocations %}
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="portfolioAllocationChart" data-portfolio-id="{{ portfolio.id }}"></canvas>
                    </div>
                {% else %}
                    <div class="alert alert-info mb-0">
                        No allocation data available for this portfolio.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Portfolio Allocation -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Security Allocation</h5>
    </div>
    <div class="card-body">
        {% if allocations %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Security</th>
                            <th>Symbol</th>
                            <th>Type</th>
                            <th>Allocation (%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for allocation in allocations %}
                            <tr>
                                <td>{{ allocation.security.name }}</td>
                                <td>{{ allocation.security.symbol }}</td>
                                <td>{{ allocation.security.type }}</td>
                                <td>{{ allocation.percentage }}%</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info mb-0">
                No allocation data available for this portfolio.
            </div>
        {% endif %}
    </div>
</div>

<!-- Historical Performance -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Historical Performance</h5>
    </div>
    <div class="card-body">
        <div class="chart-container" style="height: 400px;">
            <canvas id="performanceChart"></canvas>
        </div>
        <div class="mt-3">
            <p class="text-muted small">
                <i class="fas fa-info-circle me-1"></i>
                Historical performance is not a guarantee of future results. Past performance may not be indicative of future returns.
            </p>
        </div>
    </div>
</div>

<!-- Portfolio Characteristics -->
<div class="card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Portfolio Characteristics</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="card bg-light mb-3">
                    <div class="card-body">
                        <h6 class="card-title fw-bold">Investment Objective</h6>
                        <p class="card-text">
                            {% if portfolio.risk_level == 'High' %}
                                Growth-oriented portfolio seeking capital appreciation with higher volatility tolerance. Suitable for long-term investors.
                            {% elif portfolio.risk_level == 'Medium' %}
                                Balanced approach seeking moderate growth and income with manageable volatility. Suitable for medium-term investors.
                            {% else %}
                                Income-focused portfolio seeking stability and preservation of capital with low volatility. Suitable for short-term investors.
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light mb-3">
                    <div class="card-body">
                        <h6 class="card-title fw-bold">Asset Mix</h6>
                        <p class="card-text">
                            {% if portfolio.risk_level == 'High' %}
                                Predominantly equity-focused with higher allocation to growth stocks, emerging markets, and alternative investments.
                            {% elif portfolio.risk_level == 'Medium' %}
                                Diversified mix of equities and fixed income with balanced exposure to various market sectors and regions.
                            {% else %}
                                Conservative mix with higher allocation to fixed income, stable dividend stocks, and cash equivalents.
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light mb-3">
                    <div class="card-body">
                        <h6 class="card-title fw-bold">Rebalancing Strategy</h6>
                        <p class="card-text">
                            This portfolio is regularly monitored and rebalanced quarterly to maintain target allocations and respond to market conditions. Strategic adjustments are made to optimize risk-adjusted returns.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize portfolio allocation chart
    const allocationCanvas = document.getElementById('portfolioAllocationChart');
    if (allocationCanvas) {
        const portfolioId = allocationCanvas.dataset.portfolioId;

        // Fetch allocation data from API
        fetch(`/api/portfolio/${portfolioId}/allocations`)
            .then(response => response.json())
            .then(data => {
                // Create the chart
                const ctx = allocationCanvas.getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            data: data.values,
                            backgroundColor: data.colors || [
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)',
                                'rgba(255, 159, 64, 0.7)',
                                'rgba(201, 203, 207, 0.7)',
                                'rgba(255, 99, 71, 0.7)',
                                'rgba(50, 205, 50, 0.7)',
                                'rgba(138, 43, 226, 0.7)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed || 0;
                                        return label + ': ' + value + '%';
                                    }
                                }
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading allocation data:', error);
                allocationCanvas.parentNode.innerHTML = '<div class="alert alert-danger">Failed to load allocation data</div>';
            });
    }

    // Sample historical performance data
    const performanceData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
            label: '{{ portfolio.name }}',
            data: [
                {% if portfolio.risk_level == 'High' %}
                    2.3, 3.1, -1.2, 4.5, 2.8, 1.9, 5.2, 3.7, -0.8, 3.2, 4.1, 2.5
                {% elif portfolio.risk_level == 'Medium' %}
                    1.8, 2.5, -0.9, 3.2, 2.1, 1.5, 3.8, 2.7, -0.5, 2.3, 3.0, 2.0
                {% else %}
                    1.2, 1.5, -0.5, 2.1, 1.4, 1.1, 2.3, 1.8, -0.3, 1.6, 1.9, 1.3
                {% endif %}
            ],
            borderColor: 'rgba(54, 162, 235, 1)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            fill: true,
            tension: 0.1
        }, {
            label: 'Benchmark',
            data: [1.5, 2.2, -1.0, 3.5, 2.0, 1.4, 3.6, 2.5, -0.7, 2.1, 2.8, 1.8],
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0)',
            fill: false,
            borderDash: [5, 5]
        }]
    };

    // Create performance chart
    const performanceCtx = document.getElementById('performanceChart');
    if (performanceCtx) {
        new Chart(performanceCtx, {
            type: 'line',
            data: performanceData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Monthly Return (%)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}