{% extends "base.html" %}

{% block title %}My Investments - Portfolio Dashboard{% endblock %}

{% block content %}
<!--<div class="row mb-4">-->
<!--    <div class="col-12">-->
<!--        <div class="d-flex justify-content-between align-items-center">-->
<!--            <h1>My Investments</h1>-->
<!--            <a href="{{ url_for('customer.new_investment') }}" class="btn btn-primary">-->
<!--                <i class="fas fa-plus"></i> New Investment-->
<!--            </a>-->
<!--        </div>-->
<!--    </div>-->
<!--</div>-->

<!-- Active Investments -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Active Investments</h5>
    </div>
    <div class="card-body">
        {% set active_investments = investments|selectattr('is_active', 'equalto', true)|list %}
        {% if active_investments %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Portfolio</th>
                            <th>Risk Level</th>
                            <th>Amount</th>
                            <th>Current Value</th>
                            <th>Return</th>
                            <th>Start Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for investment in active_investments %}
                            <tr>
                                <td>{{ investment.portfolio.name }}</td>
                                <td>
                                    <span class="badge {{ 'bg-danger' if investment.portfolio.risk_level == 'High' else 'bg-warning' if investment.portfolio.risk_level == 'Medium' else 'bg-success' }}">
                                        {{ investment.portfolio.risk_level }}
                                    </span>
                                </td>
                                <td>${{ investment.amount|round(2)|format_number }}</td>

                                <!-- Show N/A if not funded -->
                                <td>
                                    {% if investment.is_funded %}
                                        ${{ investment.get_current_value()|round(2)|format_number }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if investment.is_funded %}
                                        <span class="{{ 'text-success' if investment.get_return() >= 0 else 'text-danger' }}">
                                            {{ investment.get_return()|round(2) }}%
                                        </span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>

                                <td>{{ investment.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if not investment.is_funded %}
                                        <span class="badge bg-warning">Pending Funding</span>
                                    {% else %}
                                        <span class="badge bg-success">Active</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('customer.investment_detail', investment_id=investment.id) }}"
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        {% if investment.is_funded %}
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal"
                                                    data-bs-target="#closeInvestmentModal-{{ investment.id }}">
                                                <i class="fas fa-times"></i> Close
                                            </button>
                                        {% endif %}
                                    </div>

                                    {% if investment.is_funded %}
                                    <!-- Close Investment Modal -->
                                    <div class="modal fade" id="closeInvestmentModal-{{ investment.id }}" tabindex="-1"
                                         aria-labelledby="closeInvestmentModalLabel-{{ investment.id }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="closeInvestmentModalLabel-{{ investment.id }}">
                                                        Close Investment
                                                    </h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                            aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>Are you sure you want to close this investment in
                                                       <strong>{{ investment.portfolio.name }}</strong>?</p>
                                                    <div class="alert alert-info">
                                                        <p class="mb-1">
                                                            <strong>Current Value:</strong>
                                                            ${{ investment.get_current_value()|round(2)|format_number }}
                                                        </p>
                                                        <p class="mb-0">
                                                            <strong>Return:</strong>
                                                            {{ investment.get_return()|round(2) }}%
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                        Cancel
                                                    </button>
                                                    <form action="{{ url_for('customer.close_investment', investment_id=investment.id, next=request.path) }}"
                                                          method="POST">
                                                        <button type="submit" class="btn btn-danger">Close Investment</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info mb-0">
                You don't have any active investments. Click the "New Investment" button to create one.
            </div>
        {% endif %}
    </div>
</div>

<!-- Closed Investments -->
<div class="card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Closed Investments</h5>
    </div>
    <div class="card-body">
        {% set closed_investments = investments|selectattr('is_active', 'equalto', false)|list %}
        {% if closed_investments %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Portfolio</th>
                            <th>Amount</th>
                            <th>Final Value</th>
                            <th>Return</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for investment in closed_investments %}
                            <tr>
                                <td>{{ investment.portfolio.name }}</td>
                                <td>${{ investment.amount|round(2)|format_number }}</td>
                                <td>${{ investment.get_current_value()|round(2)|format_number }}</td>
                                <td class="{{ 'text-success' if investment.get_return() >= 0 else 'text-danger' }}">
                                    {{ investment.get_return()|round(2) }}%
                                </td>
                                <td>{{ investment.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ investment.end_date.strftime('%Y-%m-%d') if investment.end_date else 'N/A' }}</td>
                                <td>
                                    <a href="{{ url_for('customer.investment_detail', investment_id=investment.id) }}"
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info mb-0">
                You have no closed investments yet.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
