{% extends "base.html" %}

{% block title %}Dashboard - Capital Insight{% endblock %}

{% block extra_head %}
<style>
    .summary-card {
        border-radius: 0.75rem;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        height: 100%;
        transition: all 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .gradient-primary {
        background: linear-gradient(45deg, #055A57, #00917C);
    }

    .gradient-success {
        background: linear-gradient(45deg, #055A57, #49B0A1);
    }

    .gradient-danger {
        background: linear-gradient(135deg, #c62828, #e53935);
    }

    .card-icon {
        font-size: 2rem;
        opacity: 0.8;
        margin-bottom: 1rem;
    }

    .card-value {
        font-size: 2.25rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .card-label {
        font-size: 1rem;
        opacity: 0.9;
        font-weight: 500;
    }

    .performance-chart-container {
        min-height: 400px;
    }

    .allocation-chart-container {
        min-height: 320px;
    }

    .investment-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 500;
    }

    .badge-active {
        background-color: var(--aqua-glow);
        color: var(--teal-grean);
    }

    .badge-pending {
        background-color: rgba(255, 152, 0, 0.15);
        color: #ef6c00;
    }

    .badge-closed {
        background-color: rgba(97, 97, 97, 0.15);
        color: #424242;
    }

    .return-positive {
        color: var(--tropical-teal);
        font-weight: 500;
    }

    .return-negative {
        color: var(--red-main);
        font-weight: 500;
    }

    .action-button {
        border-radius: 50px;
        padding: 0.5rem 1.25rem;
    }

    .investment-row:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .welcome-header {
        border-radius: 0.75rem;
        padding: 2rem;
        background: linear-gradient(to right, rgba(10, 36, 99, 0.8), rgba(30, 136, 229, 0.8));
        margin-bottom: 2rem;
        color: #212529; /* Changed from white to dark color */
        background-image: url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .market-summary {
        padding: 1.5rem;
        border-radius: 0.75rem;
        background-color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
    }

    .market-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .market-name {
        flex: 1;
        font-weight: 500;
    }

    .market-value {
        font-weight: 600;
    }

    .market-change {
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        margin-left: 0.5rem;
        text-align: right;
        min-width: 80px;
    }

    .quick-actions {
        margin-bottom: 2rem;
    }

    .action-tile {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1.5rem;
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        height: 100%;
    }

    .action-tile:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .action-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }

    .action-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-align: center;
    }

    .animate-fade-in {
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .delay-1 { animation-delay: 0.1s; }
    .delay-2 { animation-delay: 0.2s; }
    .delay-3 { animation-delay: 0.3s; }
    .delay-4 { animation-delay: 0.4s; }
</style>
{% endblock %}

{% block content %}
<!-- Welcome Header -->
<div class="welcome-header animate-fade-in">
    <div class="row align-items-center">
        <div class="col-md-12">
            <h1 class="display-5 mb-1">Welcome, {{ current_user.name }}</h1>
            <p class="fs-5 mb-0">Your investment summary and portfolio performance</p>
        </div>
    </div>
</div>

<div class="row">
    <!-- Left Column - Dashboard Content -->
    <div class="col-lg-9">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-4 animate-fade-in delay-1">
                <div class="summary-card text-white gradient-success">
                    <div class="card-body p-4">
                        <div class="card-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="card-value">${{ total_invested|round(2)|format_number }}</div>
                        <div class="card-label">Total Invested</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 animate-fade-in delay-2">
                <div class="summary-card text-white gradient-success">
                    <div class="card-body p-4">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-value">${{ total_current_value|round(2)|format_number }}</div>
                        <div class="card-label">Current Value</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 animate-fade-in delay-3">
                <div class="summary-card text-white {{ 'gradient-success' if total_return >= 0 else 'gradient-danger' }}">
                    <div class="card-body p-4">
                        <div class="card-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="card-value">{{ total_return|round(2) }}%</div>
                        <div class="card-label">Total Return</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Chart -->
        <div class="card mb-4 animate-fade-in delay-1">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Performance History</h5>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-secondary" id="view30d">30D</button>
                    <button class="btn btn-sm btn-outline-secondary" id="view90d">90D</button>
                    <button class="btn btn-sm btn-outline-secondary active" id="viewYTD">YTD</button>
                    <button class="btn btn-sm btn-outline-secondary" id="view1y">1Y</button>
                    <button class="btn btn-sm btn-outline-secondary" id="viewAll">All</button>
                </div>
            </div>
            <div class="card-body">
                <div class="performance-chart-container">
                    {% if report and report.get('performance_chart') %}
                        <img src="{{ report.performance_chart }}" class="img-fluid" alt="Performance Chart">
                    {% else %}
                        <div class="alert alert-info d-flex align-items-center" role="alert">
                            <i class="fas fa-info-circle me-2 fs-4"></i>
                            <div>No performance data available yet. Contact your administrator to create an investment for you.</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Investment Table -->
        <div class="card animate-fade-in delay-2">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-folder-open me-2"></i>Your Investments</h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                        <li><a class="dropdown-item" href="#">All Investments</a></li>
                        <li><a class="dropdown-item" href="#">Active Only</a></li>
                        <li><a class="dropdown-item" href="#">Pending Only</a></li>
                        <li><a class="dropdown-item" href="#">Closed Only</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body p-0">
                {% if investments %}
                <div class="table-responsive">
                    <table class="table mb-0">
                        <thead>
                            <tr>
                                <th>Portfolio</th>
                                <th>Amount</th>
                                <th>Current Value</th>
                                <th>Return</th>
                                <th>Start Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for investment in investments %}
                            <tr class="investment-row">
                                <td class="fw-medium">{{ investment.portfolio.name }}</td>
                                <td>${{ investment.amount|round(2)|format_number }}</td>
                                <td>
                                    {% if investment.is_funded %}
                                        ${{ investment.get_current_value()|round(2)|format_number }}
                                    {% else %}
                                        <span class="text-muted">Pending</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if investment.is_funded %}
                                        <span class="{{ 'return-positive' if investment.get_return() >= 0 else 'return-negative' }}">
                                            {% if investment.get_return() >= 0 %}+{% endif %}{{ investment.get_return()|round(2) }}%
                                        </span>
                                    {% else %}
                                        <span class="text-muted">—</span>
                                    {% endif %}
                                </td>
                                <td>{{ investment.start_date.strftime('%b %d, %Y') }}</td>
                                <td>
                                    {% if not investment.is_active %}
                                        <span class="investment-badge badge-closed">Closed</span>
                                    {% elif not investment.is_funded %}
                                        <span class="investment-badge badge-pending">Pending</span>
                                    {% else %}
                                        <span class="investment-badge badge-active">Active</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="{{ url_for('customer.investment_detail', investment_id=investment.id) }}" class="btn btn-sm btn-outline-primary me-2">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if investment.is_active and investment.is_funded %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#closeInvestmentModal-{{ investment.id }}">
                                            <i class="fas fa-times"></i>
                                        </button>

                                        <!-- Close Investment Modal -->
                                        <div class="modal fade" id="closeInvestmentModal-{{ investment.id }}" tabindex="-1" aria-labelledby="closeInvestmentModalLabel-{{ investment.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="closeInvestmentModalLabel-{{ investment.id }}">Close Investment</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="text-center mb-4">
                                                            <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                                                            <h4>Are you sure?</h4>
                                                        </div>
                                                        <p>You are about to close your investment in <strong>{{ investment.portfolio.name }}</strong>. This action cannot be undone.</p>

                                                        <div class="card bg-light my-3">
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-6">
                                                                        <p class="mb-1 text-muted">Initial Investment</p>
                                                                        <p class="fw-bold">${{ investment.amount|round(2)|format_number }}</p>
                                                                    </div>
                                                                    <div class="col-6">
                                                                        <p class="mb-1 text-muted">Current Value</p>
                                                                        <p class="fw-bold">${{ investment.get_current_value()|round(2)|format_number }}</p>
                                                                    </div>
                                                                    <div class="col-6">
                                                                        <p class="mb-1 text-muted">Start Date</p>
                                                                        <p class="fw-bold">{{ investment.start_date.strftime('%b %d, %Y') }}</p>
                                                                    </div>
                                                                    <div class="col-6">
                                                                        <p class="mb-1 text-muted">Total Return</p>
                                                                        <p class="fw-bold {{ 'text-success' if investment.get_return() >= 0 else 'text-danger' }}">{{ investment.get_return()|round(2) }}%</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form action="{{ url_for('customer.close_investment', investment_id=investment.id, next=request.path) }}" method="POST">
                                                            <button type="submit" class="btn btn-danger">Close Investment</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="p-4 text-center">
                    <div class="mb-3">
                        <i class="fas fa-folder-open text-muted fa-3x"></i>
                    </div>
                    <h5>No Investments Yet</h5>
                    <p class="text-muted mb-4">You don't have any investments yet. Please contact your administrator to create an investment for you.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Right Column - Sidebar Content -->
    <div class="col-lg-3">
        <!-- Portfolio Allocation -->
        <div class="card mb-4 animate-fade-in delay-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Portfolio Allocation</h5>
            </div>
            <div class="card-body">
                <div class="allocation-chart-container">
                    {% if report and report.status == 'success' %}
                        <img src="{{ report.allocation_chart }}" class="img-fluid" alt="Allocation Chart">
                    {% else %}
                        <div class="text-center p-4">
                            <i class="fas fa-chart-pie text-muted fa-3x mb-3"></i>
                            <p class="text-muted">No allocation data available.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>


        <!-- Quick Actions -->
        <div class="card animate-fade-in delay-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('customer.portfolios') }}" class="list-group-item list-group-item-action d-flex align-items-center">
                        <i class="fas fa-chart-pie icon-green-main me-3 fa-lg"></i>
                        <div>
                            <div class="fw-medium">Browse Portfolios</div>
                            <small class="text-muted">Explore available investment options</small>
                        </div>
                    </a>
                    <a href="{{ url_for('customer.reports') }}" class="list-group-item list-group-item-action d-flex align-items-center">
                        <i class="fas fa-file-alt text-info me-3 fa-lg"></i>
                        <div>
                            <div class="fw-medium">Generate Report</div>
                            <small class="text-muted">Create detailed performance reports</small>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize period buttons for performance chart
    const periodButtons = document.querySelectorAll('#view30d, #view90d, #viewYTD, #view1y, #viewAll');

    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            periodButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // In a real implementation, this would trigger an AJAX call to update the chart
            // For demonstration, we just show an effect
            const chartContainer = document.querySelector('.performance-chart-container');
            if (chartContainer) {
                chartContainer.style.opacity = '0.5';
                setTimeout(() => {
                    chartContainer.style.opacity = '1';
                }, 500);
            }
        });
    });

    // Auto-refresh market data every minute (simulated)
    setInterval(function() {
        const marketItems = document.querySelectorAll('.market-item');
        if (marketItems.length > 0) {
            marketItems.forEach(item => {
                // Simulate value changes
                const changeEl = item.querySelector('.market-change');
                if (changeEl) {
                    const randomChange = (Math.random() * 0.4 - 0.2).toFixed(2);
                    const isNegative = randomChange < 0;

                    changeEl.textContent = `${isNegative ? '' : '+'}${randomChange}%`;
                    changeEl.className = `market-change ${isNegative ? 'bg-danger-subtle text-danger' : 'bg-success-subtle text-success'}`;
                }
            });
        }
    }, 60000);
});
</script>
{% endblock %}
