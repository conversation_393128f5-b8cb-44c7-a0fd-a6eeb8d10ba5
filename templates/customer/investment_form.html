{% extends "base.html" %}

{% block title %}{{ title }} - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ title }}</h1>
            <!-- Back button now sends you back to the customer detail page for the target customer -->
            <a href="{{ url_for('admin.customer_detail', customer_id=customer.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Customer Details
            </a>
        </div>
    </div>
</div>

<!-- Display customer information for clarity -->
{% if customer %}
<div class="alert alert-info">
    <strong>Creating Investment For:</strong> {{ customer.name }} ({{ customer.email }})
</div>
{% endif %}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Investment Details</h5>
            </div>
            <div class="card-body">
                <!-- Change the form action to point to the admin route which includes the customer_id -->
                <form method="POST" action="{{ url_for('admin.new_investment_for_customer', customer_id=customer.id) }}">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.portfolio_id.label(class="form-label") }}
                        {{ form.portfolio_id(class="form-select" ~ (" is-invalid" if form.portfolio_id.errors else "")) }}
                        {% if form.portfolio_id.errors %}
                            {% for error in form.portfolio_id.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">Select the portfolio you want to invest in.</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.amount.label(class="form-label") }}
                        {{ form.amount(class="form-control" ~ (" is-invalid" if form.amount.errors else ""), placeholder="Enter investment amount") }}
                        {% if form.amount.errors %}
                            {% for error in form.amount.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">Enter the amount you want to invest.</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.start_date.label(class="form-label") }}
                        {{ form.start_date(class="form-control" ~ (" is-invalid" if form.start_date.errors else ""), type="date") }}
                        {% if form.start_date.errors %}
                            {% for error in form.start_date.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">Select the start date for this investment.</div>
                        {% endif %}
                    </div>

                    <!-- Paper Trading Checkbox -->
                    <div class="mb-3 form-check">
                        {{ form.paper_investment(class="form-check-input" ~ (" is-invalid" if form.paper_investment.errors else "")) }}
                        {{ form.paper_investment.label(class="form-check-label") }}
                        {% if form.paper_investment.errors %}
                            {% for error in form.paper_investment.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">Check if this investment is for paper trading.</div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        <!-- Optional: Calculation Results Section -->
        <div id="calculation-results" class="mt-4"></div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Investment Guide</h5>
            </div>
            <div class="card-body">
                <h5>Portfolio Selection</h5>
                <p>Choose a portfolio that matches your customer's investment goals and risk tolerance.</p>
                <ul>
                    <li><strong>Low Risk:</strong> Conservative, stable returns.</li>
                    <li><strong>Medium Risk:</strong> Balanced growth and income.</li>
                    <li><strong>High Risk:</strong> Aggressive, high-growth potential.</li>
                </ul>

                <h5>Investment Amount</h5>
                <p>Ensure the investment amount fits within the customer's financial plan.</p>

                <h5>Start Date</h5>
                <p>This sets when the investment begins tracking performance.</p>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i> Subscriptions are processed daily, so all new investments from a given day are subscribed together.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
