{% extends "base.html" %}

{% block title %}Performance Report - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>Performance Analysis Report</h1>
            <a href="{{ url_for('customer.reports') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Reports
            </a>
        </div>
    </div>
</div>

<!-- Report Period -->
<div class="alert alert-info mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <i class="fas fa-calendar-alt me-2"></i>
            <strong>Report Period:</strong> {{ start_date.strftime('%B %d, %Y') }} - {{ end_date.strftime('%B %d, %Y') }}
        </div>
        <div>
            <strong>Generated:</strong> {{ now.strftime('%B %d, %Y %H:%M') }}
        </div>
    </div>
</div>

<!-- Performance Overview Chart -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Overall Performance</h5>
    </div>
    <div class="card-body">
        <div class="chart-container" style="height: 400px;">
            <canvas id="overallPerformanceChart"></canvas>
        </div>
    </div>
</div>

<!-- Individual Investments Performance -->
{% if performance_data %}
    {% for data in performance_data %}
        <div class="card mb-4">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">{{ data.investment.portfolio.name }} Performance</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th>Start Date:</th>
                                    <td>{{ data.investment.start_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>Initial Investment:</th>
                                    <td>${{ data.investment.amount|round(2)|format_number }}</td>
                                </tr>
                                <tr>
                                    <th>Current Value:</th>
                                    <td>${{ data.investment.get_current_value()|round(2)|format_number }}</td>
                                </tr>
                                <tr>
                                    <th>Total Return:</th>
                                    <td class="{{ 'text-success' if data.investment.get_return() >= 0 else 'text-danger' }}">
                                        {{ data.investment.get_return()|round(2) }}%
                                    </td>
                                </tr>
                                <tr>
                                    <th>Risk Level:</th>
                                    <td>
                                        <span class="badge {{ 'bg-danger' if data.investment.portfolio.risk_level == 'High' else 'bg-warning' if data.investment.portfolio.risk_level == 'Medium' else 'bg-success' }}">
                                            {{ data.investment.portfolio.risk_level }}
                                        </span>
                                    </td>
                                </tr>
<tr>
  <th>Status:</th>
  <td>
{% if not data.investment.is_active %}
  <span class="badge bg-secondary">Closed</span>
{% elif not data.investment.is_funded %}
  <span class="badge bg-warning">Pending Funding</span>
{% else %}
  <span class="badge bg-success">Active</span>
{% endif %}
  </td>
</tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-8">
                        <div class="chart-container" style="height: 300px;">
                            <canvas id="investmentChart-{{ data.investment.id }}"></canvas>
                        </div>
                    </div>
                </div>

                <h6 class="mb-3">Monthly Performance</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Value</th>
                                <th>Return</th>
                                <th>Monthly Change</th>
                                <th>YTD Return</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in data.history %}
                                <tr>
                                    <td>{{ entry.date }}</td>
                                    <td>${{ entry.value|round(2)|format_number }}</td>
                                    <td class="{{ 'text-success' if entry.return_pct >= 0 else 'text-danger' }}">
                                        {{ entry.return_pct|round(2) }}%
                                    </td>
                                    <td class="{{ 'text-success' if entry.monthly_change >= 0 else 'text-danger' }}">
                                        {{ '+' if entry.monthly_change >= 0 else '' }}{{ entry.monthly_change|round(2) }}%
                                    </td>
                                    <td class="{{ 'text-success' if entry.ytd_return >= 0 else 'text-danger' }}">
                                        {{ entry.ytd_return|round(2) }}%
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    <h6 class="mb-3">Performance Metrics</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-subtitle mb-2 text-muted">Annualized Return</h6>
                                    <h4 class="card-title {{ 'text-success' if data.annualized_return >= 0 else 'text-danger' }}">
                                        {{ data.annualized_return|round(2) }}%
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-subtitle mb-2 text-muted">Best Month</h6>
                                    <h4 class="card-title text-success">{{ data.best_month.return|round(2) }}%</h4>
                                    <p class="card-text small">{{ data.best_month.date }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-subtitle mb-2 text-muted">Worst Month</h6>
                                    <h4 class="card-title text-danger">{{ data.worst_month.return|round(2) }}%</h4>
                                    <p class="card-text small">{{ data.worst_month.date }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-subtitle mb-2 text-muted">Volatility</h6>
                                    <h4 class="card-title">{{ data.volatility|round(2) }}%</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i> No performance data available for the selected period. Please adjust your report parameters or create an investment.
    </div>
{% endif %}

<!-- Comparison with Market Benchmarks -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Benchmark Comparison</h5>
    </div>
    <div class="card-body">
        <div class="chart-container" style="height: 400px;">
            <canvas id="benchmarkChart"></canvas>
        </div>
        <div class="mt-4">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Benchmark</th>
                            <th>Return ({{ start_date.strftime('%Y-%m-%d') }} to {{ end_date.strftime('%Y-%m-%d') }})</th>
                            <th>Your Performance</th>
                            <th>Difference</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>S&P 500</td>
                            <td>15.23%</td>
                            <td class="{{ 'text-success' if performance_data|length > 0 and performance_data[0].investment.get_return() >= 0 else 'text-danger' }}">
                                {{ performance_data[0].investment.get_return()|round(2) if performance_data|length > 0 else 0 }}%
                            </td>
                            <td class="{{ 'text-success' if performance_data|length > 0 and (performance_data[0].investment.get_return() - 15.23) >= 0 else 'text-danger' }}">
                                {{ '+' if performance_data|length > 0 and (performance_data[0].investment.get_return() - 15.23) >= 0 else '' }}
                                {{ (performance_data[0].investment.get_return() - 15.23)|round(2) if performance_data|length > 0 else -15.23 }}%
                            </td>
                        </tr>
                        <tr>
                            <td>Dow Jones</td>
                            <td>12.45%</td>
                            <td class="{{ 'text-success' if performance_data|length > 0 and performance_data[0].investment.get_return() >= 0 else 'text-danger' }}">
                                {{ performance_data[0].investment.get_return()|round(2) if performance_data|length > 0 else 0 }}%
                            </td>
                            <td class="{{ 'text-success' if performance_data|length > 0 and (performance_data[0].investment.get_return() - 12.45) >= 0 else 'text-danger' }}">
                                {{ '+' if performance_data|length > 0 and (performance_data[0].investment.get_return() - 12.45) >= 0 else '' }}
                                {{ (performance_data[0].investment.get_return() - 12.45)|round(2) if performance_data|length > 0 else -12.45 }}%
                            </td>
                        </tr>
                        <tr>
                            <td>NASDAQ</td>
                            <td>18.76%</td>
                            <td class="{{ 'text-success' if performance_data|length > 0 and performance_data[0].investment.get_return() >= 0 else 'text-danger' }}">
                                {{ performance_data[0].investment.get_return()|round(2) if performance_data|length > 0 else 0 }}%
                            </td>
                            <td class="{{ 'text-success' if performance_data|length > 0 and (performance_data[0].investment.get_return() - 18.76) >= 0 else 'text-danger' }}">
                                {{ '+' if performance_data|length > 0 and (performance_data[0].investment.get_return() - 18.76) >= 0 else '' }}
                                {{ (performance_data[0].investment.get_return() - 18.76)|round(2) if performance_data|length > 0 else -18.76 }}%
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sample data for overall performance chart
    const performanceLabels = [
        {% if performance_data|length > 0 %}
            {% for entry in performance_data[0].history %}
                "{{ entry.date }}",
            {% endfor %}
        {% endif %}
    ];

    const performanceDatasets = [
        {% if performance_data|length > 0 %}
            {% for data in performance_data %}
                {
                    label: "{{ data.investment.portfolio.name }}",
                    data: [{% for entry in data.history %} {{ entry.return_pct }}, {% endfor %}],
                    borderColor: getRandomColor({{ loop.index0 }}),
                    backgroundColor: getRandomColor({{ loop.index0 }}, 0.2),
                    fill: false,
                    tension: 0.1
                },
            {% endfor %}
        {% endif %}
    ];

    // Create overall performance chart
    const overallCtx = document.getElementById('overallPerformanceChart');
    if (overallCtx) {
        new Chart(overallCtx, {
            type: 'line',
            data: {
                labels: performanceLabels,
                datasets: performanceDatasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Return (%)'
                        }
                    }
                }
            }
        });
    }

    // Create individual investment charts
    {% if performance_data %}
        {% for data in performance_data %}
            const investmentCtx{{ data.investment.id }} = document.getElementById('investmentChart-{{ data.investment.id }}');
            if (investmentCtx{{ data.investment.id }}) {
                new Chart(investmentCtx{{ data.investment.id }}, {
                    type: 'line',
                    data: {
                        labels: [{% for entry in data.history %} "{{ entry.date }}", {% endfor %}],
                        datasets: [{
                            label: 'Value ($)',
                            data: [{% for entry in data.history %} {{ entry.value }}, {% endfor %}],
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            yAxisID: 'y',
                            fill: true
                        }, {
                            label: 'Return (%)',
                            data: [{% for entry in data.history %} {{ entry.return_pct }}, {% endfor %}],
                            borderColor: 'rgba(255, 99, 132, 1)',
                            backgroundColor: 'rgba(255, 99, 132, 0)',
                            yAxisID: 'y1',
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: 'Value ($)'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: 'Return (%)'
                                },
                                grid: {
                                    drawOnChartArea: false
                                },
                                ticks: {
                                    callback: function(value) {
                                        return value + '%';
                                    }
                                }
                            }
                        }
                    }
                });
            }
        {% endfor %}
    {% endif %}

    // Create benchmark comparison chart
    const benchmarkCtx = document.getElementById('benchmarkChart');
    if (benchmarkCtx) {
        new Chart(benchmarkCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Your Portfolio',
                    data: [2.3, 3.1, -1.2, 4.5, 2.8, 1.9, 5.2, 3.7, -0.8, 3.2, 4.1, 2.5],
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    fill: false
                }, {
                    label: 'S&P 500',
                    data: [1.8, 2.7, -0.5, 3.9, 2.3, 1.5, 4.7, 3.2, -1.2, 2.8, 3.5, 2.1],
                    borderColor: 'rgba(255, 99, 132, 1)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    fill: false
                }, {
                    label: 'Dow Jones',
                    data: [1.5, 2.5, -0.8, 3.5, 2.0, 1.2, 4.2, 2.8, -1.5, 2.5, 3.2, 1.8],
                    borderColor: 'rgba(255, 206, 86, 1)',
                    backgroundColor: 'rgba(255, 206, 86, 0.2)',
                    fill: false
                }, {
                    label: 'NASDAQ',
                    data: [2.2, 3.3, -0.3, 4.8, 3.0, 2.1, 5.5, 4.0, -0.5, 3.5, 4.5, 2.8],
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Monthly Return (%)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    // Utility function to generate colors
    function getRandomColor(index, alpha = 1) {
        const colors = [
            `rgba(54, 162, 235, ${alpha})`,
            `rgba(255, 99, 132, ${alpha})`,
            `rgba(255, 206, 86, ${alpha})`,
            `rgba(75, 192, 192, ${alpha})`,
            `rgba(153, 102, 255, ${alpha})`,
            `rgba(255, 159, 64, ${alpha})`,
            `rgba(201, 203, 207, ${alpha})`,
            `rgba(255, 99, 71, ${alpha})`
        ];

        return colors[index % colors.length];
    }
});
</script>
{% endblock %}