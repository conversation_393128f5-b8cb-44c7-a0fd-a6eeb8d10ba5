{% extends "base.html" %}

{% block title %}Summary Report - Capital Insight{% endblock %}

{% block extra_head %}
<style>
    /* Custom styles for a modern, fancy report summary */
    .report-header {
        background: linear-gradient(to right, var(--teal-grean), var(--tropical-green2));
        border-radius: 0.5rem;
        padding: 1.5rem;
        color: #fff;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .report-header h1 {
        margin-bottom: 0.5rem;
        font-size: 1.75rem;
        font-weight: 600;
    }
    .report-header p {
        margin-bottom: 0;
        font-weight: 400;
    }
    .summary-card {
        border-radius: 0.5rem;
        overflow: hidden;
        color: #fff;
        margin-bottom: 1rem;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background-color: var(--accent-color);
        color: var(--teal-grean);
    }
    .summary-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 12px rgba(0, 0, 0, 0.1);
    }
    .summary-card h6 {
        margin: 0;
        font-size: 0.95rem;
        font-weight: 500;
        opacity: 0.9;
    }
    .summary-card p {
        margin: 0;
        font-size: 1.6rem;
        font-weight: 700;
    }
    .card-container {
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
        border-radius: 0.5rem;
    }
    .chart-placeholder {
        min-height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9f9f9;
        border: 1px dashed #ccc;
        color: #888;
    }
    .table thead {
        background-color: #f1f1f1;
    }
    .badge-risik-high {
        background-color: rgba(198, 40, 40, 0.1);
        color: #c62828;
        font-weight: 500;
    }
    .badge-risik-medium {
        background-color: rgba(255, 152, 0, 0.15);
        color: #ef6c00;
        font-weight: 500;
    }
    .badge-risik-low {
        background-color: rgba(67, 160, 71, 0.15);
        color: #2e7d32;
        font-weight: 500;
    }

    .investment-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 500;
    }

    .badge-active {
        background-color: var(--aqua-glow);
        color: var(--teal-grean);
    }


</style>
{% endblock %}

{% block content %}
<!-- Report Header -->
<div class="report-header">
    <h1 class="mb-2">Summary Report</h1>
    <p>
        <strong>Report Period:</strong> {{ start_date.strftime('%B %d, %Y') }} - {{ end_date.strftime('%B %d, %Y') }} &middot;
        <strong>Generated:</strong> {{ now.strftime('%B %d, %Y %H:%M') }}
    </p>
</div>

<!-- Summary Cards -->
<div class="row g-3 mb-3">
    <div class="col-sm-6 col-md-3">
        <div class="summary-card p-3">
            <h6>Total Invested</h6>
            <p>${{ report.get('total_invested', 0)|round(2)|format_number }}</p>
        </div>
    </div>
    <div class="col-sm-6 col-md-3">
        <div class="summary-card  p-3">
            <h6>Current Value</h6>
            <p>${{ report.get('total_current_value', 0)|round(2)|format_number }}</p>
        </div>
    </div>
    <div class="col-sm-6 col-md-3">
        {% set ret = report.get('total_return_percentage', 0)|float %}
        {# set ret_bg = 'bg-success' if ret >= 0 else 'bg-danger' #}
        <!-- <div class="summary-card {{ ret_bg }} p-3"> -->
        <div class="summary-card p-3">
            <h6>Total Return</h6>
            <p>{{ ret|round(2) }}%</p>
        </div>
    </div>
    <div class="col-sm-6 col-md-3">
        <div class="summary-card p-3">
            <h6>Active Investments</h6>
            <p>{{ report.get('active_investments_count', 0) }}</p>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row g-3 mb-3">
    <div class="col-md-8">
        <div class="card-container card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Performance History</h5>
            </div>
            <div class="card-body">
                {% if report.get('performance_chart') %}
                    <img src="{{ report.get('performance_chart') }}" class="img-fluid" alt="Performance Chart">
                {% else %}
                    <div class="chart-placeholder">No performance data available</div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card-container card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Portfolio Allocation</h5>
            </div>
            <div class="card-body">
                {% if report.get('allocation_chart') %}
                    <img src="{{ report.get('allocation_chart') }}" class="img-fluid" alt="Allocation Chart">
                {% else %}
                    <div class="chart-placeholder">No allocation data available</div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!--&lt;!&ndash; Portfolio Summary Table &ndash;&gt;-->
<!--<div class="card-container card mb-3">-->
<!--    <div class="card-header bg-dark text-white">-->
<!--        <h5 class="card-title mb-0">Portfolio Summary</h5>-->
<!--    </div>-->
<!--    <div class="card-body">-->
<!--        {% if report.get('portfolios') %}-->
<!--        <div class="table-responsive">-->
<!--            <table class="table align-middle table-striped">-->
<!--                <thead>-->
<!--                    <tr>-->
<!--                        <th>Portfolio</th>-->
<!--                        <th>Risk Level</th>-->
<!--                        <th>Amount Invested</th>-->
<!--                        <th>Current Value</th>-->
<!--                        <th>Return</th>-->
<!--                    </tr>-->
<!--                </thead>-->
<!--<tbody>-->
<!--    {% for portfolio in report.get('portfolios', []) %}-->
<!--        <tr>-->
<!--            <td>{{ portfolio.name }}</td>-->
<!--            <td>-->
<!--                {% if portfolio.risk_level == 'High' %}-->
<!--                    <span class="badge badge-risik-high">High</span>-->
<!--                {% elif portfolio.risk_level == 'Medium' %}-->
<!--                    <span class="badge badge-risik-medium">Medium</span>-->
<!--                {% else %}-->
<!--                    <span class="badge badge-risik-low">Low</span>-->
<!--                {% endif %}-->
<!--            </td>-->
<!--            <td>${{ portfolio.amount_invested|round(2)|format_number }}</td>-->
<!--            <td>${{ portfolio.current_value|round(2)|format_number }}</td>-->
<!--            <td class="{% if portfolio.return >= 0 %}text-success{% else %}text-danger{% endif %}">-->
<!--                {% if portfolio.return >= 0 %}+{% endif %}{{ portfolio.return|round(2) }}%-->
<!--            </td>-->
<!--        </tr>-->
<!--    {% endfor %}-->
<!--</tbody>-->

<!--            </table>-->
<!--        </div>-->
<!--        {% else %}-->
<!--            <div class="alert alert-info">No portfolio data available for the selected period.</div>-->
<!--        {% endif %}-->
<!--    </div>-->
<!--</div>-->

<!-- Investment Summary Table -->
<div class="card-container card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Investment Summary</h5>
    </div>
    <div class="card-body">
        {% if report.get('investments') %}
        <div class="table-responsive">
            <table class="table align-middle table-striped">
                <thead class="table-light">
                    <tr>
                        <th>Portfolio</th>
                        <th>Start Date</th>
                        <th>Amount</th>
                        <th>Current Value</th>
                        <th>Return</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inv in report.get('investments', []) %}
                        {% set portfolio_name = inv.get('portfolio_name','') if inv is mapping else inv.portfolio.name %}
                        {% set start_d = inv.get('start_date') if inv is mapping else inv.start_date %}
                        {% set amt = inv.get('amount',0) if inv is mapping else inv.amount %}
                        {% set currv = inv.get('current_value',0) if inv is mapping else inv.get_current_value() %}
                        {% set ret = inv.get('return',0) if inv is mapping else inv.get_return() %}
                        {% set is_active = inv.get('is_active',True) if inv is mapping else inv.is_active %}
                        {% set is_funded = inv.get('is_funded',False) if inv is mapping else inv.is_funded %}
                    <tr>
                        <td>{{ portfolio_name }}</td>
                        <td>
                            {% if start_d %}
                                {{ start_d.strftime('%Y-%m-%d') if start_d is not string else start_d }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>${{ amt|round(2)|format_number }}</td>
                        <td>${{ currv|round(2)|format_number }}</td>
                        <td class="{% if ret >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {% if ret >= 0 %}+{% endif %}{{ ret|round(2) }}%
                        </td>
                        <td>
                            {% if not is_active %}
                                <span class="badge bg-secondary">Closed</span>
                            {% elif not is_funded %}
                                <span class="badge bg-warning">Pending Funding</span>
                            {% else %}
                                <span class="badge investment-badge badge-active">Active</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
            <div class="alert alert-info">No investment data available for the selected period.</div>
        {% endif %}
    </div>
</div>
{% endblock %}
