{% extends "base.html" %}

{% block title %}Detailed Report - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>Detailed Investment Report</h1>
            <a href="{{ url_for('customer.reports') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Reports
            </a>
        </div>
    </div>
</div>

<!-- Report Period -->
<div class="alert alert-info mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <i class="fas fa-calendar-alt me-2"></i>
            <strong>Report Period:</strong> {{ start_date.strftime('%B %d, %Y') }} - {{ end_date.strftime('%B %d, %Y') }}
        </div>
        <div>
            <strong>Generated:</strong> {{ now.strftime('%B %d, %Y %H:%M') }}
        </div>
    </div>
</div>

<!-- Investment Reports -->
{% if investment_reports %}
    {% for report in investment_reports %}
        <div class="card mb-4">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">
                    {{ report.investment.portfolio.name }} -
                    <span class="badge {{ 'bg-success' if report.investment.is_active else 'bg-secondary' }}">
                        {{ 'Active' if report.investment.is_active else 'Closed' }}
                    </span>
                </h5>
            </div>
            <div class="card-body">
                <!-- Investment Summary -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th>Start Date:</th>
                                    <td>{{ report.investment.start_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                {% if not report.investment.is_active %}
                                <tr>
                                    <th>End Date:</th>
                                    <td>{{ report.investment.end_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>Amount Invested:</th>
                                    <td>${{ report.investment.amount|round(2)|format_number }}</td>
                                </tr>
                                <tr>
                                    <th>Current Value:</th>
                                    <td>${{ report.current_value|round(2)|format_number }}</td>
                                </tr>
                                <tr>
                                    <th>Total Return:</th>
                                    <td class="{{ 'text-success' if report.return_pct >= 0 else 'text-danger' }}">
                                        {{ report.return_pct|round(2) }}%
                                    </td>
                                </tr>
                                <tr>
                                    <th>Return ($):</th>
                                    <td class="{{ 'text-success' if report.return_amount >= 0 else 'text-danger' }}">
                                        ${{ report.return_amount|round(2)|format_number }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Portfolio Risk Level:</th>
                                    <td>
                                        <span class="badge {{ 'bg-danger' if report.investment.portfolio.risk_level == 'High' else 'bg-warning' if report.investment.portfolio.risk_level == 'Medium' else 'bg-success' }}">
                                            {{ report.investment.portfolio.risk_level }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <!-- Performance Chart -->
                        {% if report.performance_chart %}
                            <img src="{{ report.performance_chart }}" class="img-fluid" alt="Performance Chart">
                        {% else %}
                            <div class="alert alert-info">
                                No performance chart available for this investment.
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Portfolio Allocation -->
                <h5 class="mb-3">Portfolio Allocation</h5>
                <div class="row mb-4">
                    <div class="col-md-6">
                        {% if report.allocation_chart %}
                            <img src="{{ report.allocation_chart }}" class="img-fluid" alt="Portfolio Allocation">
                        {% else %}
                            <div class="alert alert-info">
                                No allocation chart available for this portfolio.
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if report.allocations %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Security</th>
                                            <th>Type</th>
                                            <th>Allocation</th>
                                            <th>Current Value</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for allocation in report.allocations %}
                                            <tr>
                                                <td>{{ allocation.security_name }}</td>
                                                <td>{{ allocation.security_type }}</td>
                                                <td>{{ allocation.percentage }}%</td>
                                                <td>${{ (report.current_value * allocation.percentage / 100)|round(2)|format_number }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                No allocation data available for this portfolio.
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Performance History -->
                <h5 class="mb-3">Performance History</h5>
                {% if report.performances %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Value</th>
                                    <th>Return</th>
                                    <th>Change from Previous</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for performance in report.performances %}
                                    <tr>
                                        <td>{{ performance.date.strftime('%Y-%m-%d') }}</td>
                                        <td>${{ performance.value|round(2)|format_number }}</td>
                                        <td class="{{ 'text-success' if performance.return_pct >= 0 else 'text-danger' }}">
                                            {{ performance.return_pct|round(2) }}%
                                        </td>
                                        <td>
                                            {% if loop.index > 1 %}
                                                {% set prev_value = report.performances[loop.index0 - 1].value %}
                                                {% set change = performance.value - prev_value %}
                                                {% set change_pct = (change / prev_value * 100) if prev_value else 0 %}
                                                <span class="{{ 'text-success' if change >= 0 else 'text-danger' }}">
                                                    {{ '+' if change >= 0 else '' }}${{ change|round(2)|format_number }}
                                                    ({{ '+' if change_pct >= 0 else '' }}{{ change_pct|round(2) }}%)
                                                </span>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        No performance history available for this investment.
                    </div>
                {% endif %}
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i> No investment data available for the selected period. Please adjust your report parameters or create an investment.
    </div>
{% endif %}
{% endblock %}