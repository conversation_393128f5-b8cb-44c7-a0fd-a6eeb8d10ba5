{% extends "base.html" %}

{% block title %}Export Performance Report - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>Export Performance Report</h1>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Import Form -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Download Report</h5>
            </div>
            <div class="card-body">
<form method="POST" action="{{ url_for('customer.generate_report') }}">
                    {{ form.hidden_tag() }}


                   <div class="mb-3">
    {{ form.report_type.label(class="form-label") }}
    {{ form.report_type(class="form-select" + (" is-invalid" if form.report_type.errors else "")) }}
    {% if form.report_type.errors %}
        {% for error in form.report_type.errors %}
            <div class="invalid-feedback">{{ error }}</div>
        {% endfor %}
    {% endif %}
</div>

<div class="mb-3">
    {{ form.start_date.label(class="form-label") }}
    {{ form.start_date(class="form-control" + (" is-invalid" if form.start_date.errors else ""), type="date") }}
    {% if form.start_date.errors %}
        {% for error in form.start_date.errors %}
            <div class="invalid-feedback">{{ error }}</div>
        {% endfor %}
    {% endif %}
</div>

<div class="mb-3">
    {{ form.end_date.label(class="form-label") }}
    {{ form.end_date(class="form-control" + (" is-invalid" if form.end_date.errors else ""), type="date") }}
    {% if form.end_date.errors %}
        {% for error in form.end_date.errors %}
            <div class="invalid-feedback">{{ error }}</div>
        {% endfor %}
    {% endif %}
</div>

<div class="mb-3">
    {{ form.format.label(class="form-label") }}
    {{ form.format(class="form-select" + (" is-invalid" if form.format.errors else "")) }}
    {% if form.format.errors %}
        {% for error in form.format.errors %}
            <div class="invalid-feedback">{{ error }}</div>
        {% endfor %}
    {% endif %}
</div>



<!-- Creating Portfolio from Report? #TODO
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="createPortfolioSwitch" name="create_portfolio" value="true">
                                <label class="form-check-label" for="createPortfolioSwitch">Create Portfolio from Report</label>
                            </div>
                        </div>
                        <div class="card-body" id="portfolioOptions" style="display: none;">
                            <div class="mb-3">
                                <label for="portfolio_name" class="form-label">Portfolio Name</label>
                                <input type="text" class="form-control" id="portfolio_name" name="portfolio_name" placeholder="Leave blank to use account name from report">
                                <div class="form-text">
                                    Enter a name for the new portfolio, or leave blank to use the account name from the report.
                                </div>
                            </div>
                        </div>
                    </div>
-->
       
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
<!-- Info on Creating Portfolio from Report? #TODO

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Import Instructions</h5>
            </div>
            <div class="card-body">
                <h5>IBKR Report Format</h5>
                <p>The system supports importing IBKR reports in both JSON and CSV formats. The following data will be extracted from the reports:</p>

                <ul>
                    <li><strong>Securities:</strong> Stocks, ETFs, and other instruments in the portfolio</li>
                    <li><strong>Prices:</strong> Current prices of securities</li>
                    <li><strong>Portfolio Composition:</strong> How the portfolio is allocated</li>
                    <li><strong>Performance Data:</strong> Historical performance metrics</li>
                </ul>

                <h5>How to Export Reports from IBKR</h5>
                <ol>
                    <li>Log in to your IBKR account</li>
                    <li>Go to Performance & Reports > Flex Queries</li>
                    <li>Create a new query with the following sections:</li>
                    <ul>
                        <li>Account Information</li>
                        <li>Net Asset Value</li>
                        <li>Open Positions</li>
                        <li>Trades</li>
                        <li>Cash Transactions</li>
                    </ul>
                    <li>Run the query and download in your preferred format (JSON or CSV)</li>
                </ol>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> You can automatically create a portfolio from the imported report by checking the "Create Portfolio from Report" option.
                </div>
            </div>
        </div>
    </div>
</div>
-->
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle portfolio options when checkbox is clicked
    const createPortfolioSwitch = document.getElementById('createPortfolioSwitch');
    const portfolioOptions = document.getElementById('portfolioOptions');

    createPortfolioSwitch.addEventListener('change', function() {
        if (this.checked) {
            portfolioOptions.style.display = 'block';
        } else {
            portfolioOptions.style.display = 'none';
        }
    });
});
</script>

<style>
    h5, h6 {
        color: var(--teal-grean);
    }
</style>
{% endblock %}