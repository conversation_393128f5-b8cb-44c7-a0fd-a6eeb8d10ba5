{% extends "base.html" %}

{% block title %}Available Portfolios - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>Available Portfolios</h1>
            <a href="{{ url_for('customer.new_investment') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Investment
            </a>
        </div>
    </div>
</div>

<!-- Portfolio Cards -->
<div class="row">
    {% if portfolio_data %}
        {% for item in portfolio_data %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ item.portfolio.name }}</h5>
                        <span class="badge {{ 'bg-danger' if item.portfolio.risk_level == 'High' else 'bg-warning' if item.portfolio.risk_level == 'Medium' else 'bg-success' }}">
                            {{ item.portfolio.risk_level }} Risk
                        </span>
                    </div>
                    <div class="card-body">
                        {% if item.portfolio.description %}
                            <p class="card-text">{{ item.portfolio.description|truncate(150) }}</p>
                        {% else %}
                            <p class="card-text text-muted">No description available for this portfolio.</p>
                        {% endif %}

                        <h6 class="mt-4">Portfolio Allocation</h6>
                        {% if item.portfolio.allocations.count() > 0 %}
                            <div class="chart-container" style="height: 200px;">
                                <canvas id="allocationChart-{{ item.portfolio.id }}" data-portfolio-id="{{ item.portfolio.id }}"></canvas>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                No allocation data available for this portfolio.
                            </div>
                        {% endif %}

{% if item.invested > 0 %}
    {% set net_return = ((item.current_value - item.invested) / item.invested * 100) %}
    <div class="alert alert-success mt-3">
        <i class="fas fa-check-circle me-2"></i> You are currently invested in this portfolio.
        <p class="mb-0 mt-1 small">
            <strong>Amount Invested:</strong> ${{ item.invested|round(2)|format_number }}<br>
            <strong>Current Value:</strong> ${{ item.current_value|round(2)|format_number }}<br>
            <strong>Return:</strong>
            <span class="{{ 'text-success' if net_return >= 0 else 'text-danger' }}">
                {{ net_return|round(2) }}%
            </span>
        </p>
    </div>
{% endif %}
                    </div>
                    <div class="card-footer">
                        <div class="d-grid gap-2">
{% if item.invested > 0 and item.investments|length > 0 %}
    <a href="{{ url_for('customer.investment_detail', investment_id=item.investments[0].id) }}" class="btn btn-primary">
        <i class="fas fa-eye"></i> View Investment
    </a>
{% else %}
    <a href="{{ url_for('customer.new_investment') }}?portfolio_id={{ item.portfolio.id }}" class="btn btn-success">
        <i class="fas fa-plus-circle"></i> Invest Now
    </a>
{% endif %}
                            <a href="{{ url_for('customer.portfolio_detail', portfolio_id=item.portfolio.id) }}" class="btn btn-secondary">
                                <i class="fas fa-info-circle"></i> Portfolio Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No portfolios are available for investment at this time. Please check back later or contact the administrator.
            </div>
        </div>
    {% endif %}
</div>

<!-- Risk Level Guide -->
<div class="card mt-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Investment Risk Guide</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="card bg-success text-white mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Low Risk</h5>
                        <p class="card-text">Conservative investments with stable returns and lower volatility. Ideal for short-term goals or risk-averse investors.</p>
                    </div>
                </div>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Expected Return
                        <span class="badge bg-success">3-6%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Volatility
                        <span class="badge bg-success">Low</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Time Horizon
                        <span class="badge bg-success">1-3 Years</span>
                    </li>
                </ul>
            </div>
            <div class="col-md-4">
                <div class="card bg-warning text-white mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Medium Risk</h5>
                        <p class="card-text">Balanced approach with moderate growth potential and medium volatility. Suitable for mid-term investment goals.</p>
                    </div>
                </div>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Expected Return
                        <span class="badge bg-warning">6-10%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Volatility
                        <span class="badge bg-warning">Medium</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Time Horizon
                        <span class="badge bg-warning">3-7 Years</span>
                    </li>
                </ul>
            </div>
            <div class="col-md-4">
                <div class="card bg-danger text-white mb-3">
                    <div class="card-body">
                        <h5 class="card-title">High Risk</h5>
                        <p class="card-text">Aggressive strategy with higher potential returns and increased volatility. Best for long-term investment goals.</p>
                    </div>
                </div>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Expected Return
                        <span class="badge bg-danger">10-15%+</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Volatility
                        <span class="badge bg-danger">High</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Time Horizon
                        <span class="badge bg-danger">7+ Years</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize allocation charts for each portfolio
    const portfolioCanvases = document.querySelectorAll('[data-portfolio-id]');

    portfolioCanvases.forEach(canvas => {
        const portfolioId = canvas.dataset.portfolioId;

        // Fetch allocation data from API
        fetch(`/api/portfolio/${portfolioId}/allocations`)
            .then(response => response.json())
            .then(data => {
                // Create the chart
                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            data: data.values,
                            backgroundColor: data.colors || [
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    font: {
                                        size: 10
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed || 0;
                                        return label + ': ' + value + '%';
                                    }
                                }
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading allocation data:', error);
                canvas.parentNode.innerHTML = '<div class="alert alert-danger">Failed to load allocation data</div>';
            });
    });
});
</script>
{% endblock %}