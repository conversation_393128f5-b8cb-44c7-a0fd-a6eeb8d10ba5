{% extends "base.html" %}

{% block title %}Advanced Dashboard - Portfolio Dashboard{% endblock %}

{% block extra_head %}
<style>
    .metric-card {
        border-radius: 10px;
        transition: transform 0.3s, box-shadow 0.3s;
    }
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 30px;
    }
    .small-chart {
        height: 250px;
    }
    .dashboard-section {
        margin-bottom: 40px;
    }
    .section-title {
        border-left: 4px solid #3b82f6;
        padding-left: 10px;
    }
    .risk-badge {
        font-size: 0.85rem;
        padding: 5px 10px;
        border-radius: 20px;
        margin-left: 10px;
    }
    .tooltip-custom {
        position: relative;
        display: inline-block;
        cursor: help;
    }
    .tooltip-custom .tooltip-text {
        visibility: hidden;
        width: 200px;
        background-color: #555;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        margin-left: -100px;
        opacity: 0;
        transition: opacity 0.3s;
    }
    .tooltip-custom:hover .tooltip-text {
        visibility: visible;
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Investment Dashboard</h1>
        <div>
            <a href="{{ url_for('customer.new_investment') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Investment
            </a>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="text-center py-5">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Loading your investment data...</p>
    </div>

    <!-- Dashboard Content (hidden until loaded) -->
    <div id="dashboardContent" style="display: none;">
        <!-- Key Metrics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">Total Invested</h5>
                        <h2 class="card-text" id="totalInvestedValue">$0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title">Current Value</h5>
                        <h2 class="card-text" id="currentValueValue">$0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card text-white" id="returnCard">
                    <div class="card-body">
                        <h5 class="card-title">Total Return</h5>
                        <h2 class="card-text" id="totalReturnValue">0%</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card bg-info text-white">
                    <div class="card-body">
                        <h5 class="card-title">Annualized Return</h5>
                        <h2 class="card-text" id="annualizedReturnValue">0%</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Investment Value Chart Section -->
        <div class="dashboard-section">
            <h3 class="section-title mb-4">Portfolio Performance</h3>
            <div class="card">
                <div class="card-body">
                    <ul class="nav nav-tabs" id="performanceTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="value-tab" data-bs-toggle="tab" data-bs-target="#value-content" type="button" role="tab">Value History</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="return-tab" data-bs-toggle="tab" data-bs-target="#return-content" type="button" role="tab">Return History</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="monthly-tab" data-bs-toggle="tab" data-bs-target="#monthly-content" type="button" role="tab">Monthly Performance</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3">
                        <div class="tab-pane fade show active" id="value-content" role="tabpanel">
                            <div class="chart-container">
                                <canvas id="valueChart"></canvas>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="return-content" role="tabpanel">
                            <div class="chart-container">
                                <canvas id="returnChart"></canvas>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="monthly-content" role="tabpanel">
                            <div class="chart-container">
                                <canvas id="monthlyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Portfolio Allocation and Risk Analysis -->
        <div class="row dashboard-section">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-dark text-white">
                        <h5 class="card-title mb-0">Portfolio Allocation</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container small-chart">
                            <canvas id="allocationChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-dark text-white">
                        <h5 class="card-title mb-0">Risk & Return Analysis</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-subtitle mb-2 text-muted">
                                            Volatility
                                            <span class="tooltip-custom">
                                                <i class="fas fa-info-circle text-primary"></i>
                                                <span class="tooltip-text">Measures the fluctuation in returns. Lower values indicate more stable returns.</span>
                                            </span>
                                        </h6>
                                        <h4 class="card-title" id="volatilityValue">-</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-subtitle mb-2 text-muted">
                                            Sharpe Ratio
                                            <span class="tooltip-custom">
                                                <i class="fas fa-info-circle text-primary"></i>
                                                <span class="tooltip-text">Measures return per unit of risk. Higher values indicate better risk-adjusted returns.</span>
                                            </span>
                                        </h6>
                                        <h4 class="card-title" id="sharpeValue">-</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-subtitle mb-2 text-muted">
                                            Sortino Ratio
                                            <span class="tooltip-custom">
                                                <i class="fas fa-info-circle text-primary"></i>
                                                <span class="tooltip-text">Similar to Sharpe ratio but only considers downside risk. Higher values are better.</span>
                                            </span>
                                        </h6>
                                        <h4 class="card-title" id="sortinoValue">-</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-subtitle mb-2 text-muted">
                                            Max Drawdown
                                            <span class="tooltip-custom">
                                                <i class="fas fa-info-circle text-primary"></i>
                                                <span class="tooltip-text">Largest peak-to-trough decline. Lower values indicate lower downside risk.</span>
                                            </span>
                                        </h6>
                                        <h4 class="card-title" id="drawdownValue">-</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Comparison -->
        <div class="dashboard-section">
            <h3 class="section-title mb-4">Market Comparison</h3>
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">Performance vs Benchmarks</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="benchmarkChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Investment Table -->
        <div class="dashboard-section">
            <h3 class="section-title mb-4">Your Investments</h3>
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">Investment Details</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="investmentsTable">
                            <thead>
                                <tr>
                                    <th>Portfolio</th>
                                    <th>Risk Level</th>
                                    <th>Amount</th>
                                    <th>Current Value</th>
                                    <th>Return</th>
                                    <th>Start Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Investment rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to format currency
    function formatCurrency(value) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value);
    }

    // Function to format percentage
    function formatPercentage(value) {
        return value.toFixed(2) + '%';
    }

    // Get dashboard data from API
    fetch('/dashboard/data')
        .then(response => response.json())
        .then(data => {
            // Hide loading spinner
            document.getElementById('loadingSpinner').style.display = 'none';
            // Show dashboard content
            document.getElementById('dashboardContent').style.display = 'block';

            // Load investment data from API endpoint
            return Promise.all([
                data,
                fetch('/api/customer/dashboard-data').then(res => res.json())
            ]);
        })
        .then(([chartData, apiData]) => {
            // Update summary metrics
            document.getElementById('totalInvestedValue').textContent = formatCurrency(apiData.summary.total_invested);
            document.getElementById('currentValueValue').textContent = formatCurrency(apiData.summary.total_current_value);

            const totalReturn = apiData.summary.total_return;
            document.getElementById('totalReturnValue').textContent = formatPercentage(totalReturn);

            // Calculate annualized return (simple approximation)
            const investments = apiData.investments;
            if (investments.length > 0) {
                // Get the oldest investment date
                const oldestDate = new Date(Math.min(...investments.map(inv => new Date(inv.start_date))));
                const now = new Date();
                const yearsElapsed = (now - oldestDate) / (1000 * 60 * 60 * 24 * 365);

                if (yearsElapsed > 0) {
                    const annualizedReturn = (Math.pow(1 + totalReturn / 100, 1 / yearsElapsed) - 1) * 100;
                    document.getElementById('annualizedReturnValue').textContent = formatPercentage(annualizedReturn);
                }
            }

            // Set return card color based on performance
            const returnCard = document.getElementById('returnCard');
            if (totalReturn >= 0) {
                returnCard.classList.add('bg-success');
            } else {
                returnCard.classList.add('bg-danger');
            }

            // Update risk metrics
            const riskMetrics = chartData.risk_metrics;
            if (riskMetrics.volatility !== null) {
                document.getElementById('volatilityValue').textContent = formatPercentage(riskMetrics.volatility);
                document.getElementById('sharpeValue').textContent = riskMetrics.sharpe_ratio.toFixed(2);
                document.getElementById('sortinoValue').textContent = riskMetrics.sortino_ratio.toFixed(2);
                document.getElementById('drawdownValue').textContent = formatPercentage(riskMetrics.max_drawdown);
            }

            // Create value chart
            const valueCtx = document.getElementById('valueChart').getContext('2d');
            new Chart(valueCtx, {
                type: 'line',
                data: chartData.value_chart.data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += '$' + context.parsed.y.toLocaleString();
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Create return chart
            const returnCtx = document.getElementById('returnChart').getContext('2d');
            new Chart(returnCtx, {
                type: 'line',
                data: chartData.return_chart.data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        y: {
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y.toFixed(2) + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Create monthly performance chart
            const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
            new Chart(monthlyCtx, {
                type: 'bar',
                data: {
                    labels: chartData.monthly_performance.labels,
                    datasets: [{
                        label: 'Monthly Return (%)',
                        data: chartData.monthly_performance.data,
                        backgroundColor: chartData.monthly_performance.data.map(value =>
                            value >= 0 ? 'rgba(75, 192, 192, 0.7)' : 'rgba(255, 99, 132, 0.7)'
                        )
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y.toFixed(2) + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Create allocation chart
            const allocationCtx = document.getElementById('allocationChart').getContext('2d');
            new Chart(allocationCtx, {
                type: 'pie',
                data: chartData.allocation_chart.data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += context.parsed.toFixed(1) + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Create benchmark comparison chart
            const benchmarkCtx = document.getElementById('benchmarkChart').getContext('2d');
            new Chart(benchmarkCtx, {
                type: 'line',
                data: chartData.benchmark_comparison,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        y: {
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y.toFixed(2) + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Populate investments table
            const tableBody = document.getElementById('investmentsTable').getElementsByTagName('tbody')[0];
            tableBody.innerHTML = ''; // Clear existing rows

            apiData.investments.forEach(inv => {
                const row = tableBody.insertRow();

                // Portfolio name
                const portfolioCell = row.insertCell();
                portfolioCell.textContent = inv.portfolio_name;

                // Risk level - determine from portfolio name (simplified for demo)
                const riskCell = row.insertCell();
                let riskBadge = document.createElement('span');
                riskBadge.className = 'badge';

                // Set risk level based on portfolio name (just for demonstration purposes)
                if (inv.portfolio_name.includes('Conservative') || inv.portfolio_name.includes('Income')) {
                    riskBadge.className += ' bg-success';
                    riskBadge.textContent = 'Low';
                } else if (inv.portfolio_name.includes('Growth') || inv.portfolio_name.includes('Balanced')) {
                    riskBadge.className += ' bg-warning';
                    riskBadge.textContent = 'Medium';
                } else if (inv.portfolio_name.includes('Aggressive') || inv.portfolio_name.includes('High')) {
                    riskBadge.className += ' bg-danger';
                    riskBadge.textContent = 'High';
                } else {
                    riskBadge.className += ' bg-info';
                    riskBadge.textContent = 'Medium';
                }

                riskCell.appendChild(riskBadge);

                // Amount
                const amountCell = row.insertCell();
                amountCell.textContent = formatCurrency(inv.amount);

                // Current value
                const valueCell = row.insertCell();
                valueCell.textContent = formatCurrency(inv.current_value);

                // Return
                const returnCell = row.insertCell();
                returnCell.textContent = formatPercentage(inv.return);
                returnCell.className = inv.return >= 0 ? 'text-success' : 'text-danger';

                // Start date
                const dateCell = row.insertCell();
                dateCell.textContent = inv.start_date;

                // Status
                const statusCell = row.insertCell();
                let statusBadge = document.createElement('span');
                statusBadge.className = 'badge bg-success';
                statusBadge.textContent = 'Active';
                statusCell.appendChild(statusBadge);

                // Actions
                const actionsCell = row.insertCell();
                const viewBtn = document.createElement('a');
                viewBtn.href = `/dashboard/investment/${inv.id}`;
                viewBtn.className = 'btn btn-sm btn-primary me-2';
                viewBtn.innerHTML = '<i class="fas fa-eye"></i> View';
                actionsCell.appendChild(viewBtn);

                // Add close button for active investments
                const closeBtn = document.createElement('button');
                closeBtn.type = 'button';
                closeBtn.className = 'btn btn-sm btn-danger';
                closeBtn.innerHTML = '<i class="fas fa-times"></i> Close';
                closeBtn.dataset.bsToggle = 'modal';
                closeBtn.dataset.bsTarget = `#closeInvestmentModal-${inv.id}`;
                actionsCell.appendChild(closeBtn);
            });
        })
        .catch(error => {
            console.error('Error fetching dashboard data:', error);
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('dashboardContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    There was an error loading your dashboard. Please try again later.
                </div>
            `;
            document.getElementById('dashboardContent').style.display = 'block';
        });
});
</script>
{% endblock %}