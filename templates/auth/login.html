{% extends "base.html" %}

{% block title %}Sign In - Keheilan Fund{% endblock %}

{% block extra_head %}
<style>
    .auth-container {
        max-width: 450px;
        margin: 0 auto;
    }

    .auth-card {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .auth-header {
        background: linear-gradient(to right, var(--teal-grean), var(--tropical-green2));
        color: white;
        padding: 2.5rem 2rem;
        text-align: center;
    }

    .auth-logo {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .auth-title {
        font-weight: 400;
        margin-bottom: 0.5rem;
    }

    .auth-subtitle {
        opacity: 0.8;
        font-weight: 300;
    }

    .auth-body {
        padding: 2.5rem;
    }

    .form-floating > label {
        color: #64748b;
    }

    .form-floating .form-control {
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        padding: 1.5rem 1rem;
        height: calc(3.5rem + 2px);
        font-size: 1rem;
    }

    .form-floating .form-control:focus {
        box-shadow: 0 0 0 0.25rem rgba(30, 136, 229, 0.25);
        border-color: #1e88e5;
    }

    .auth-button {
        background: linear-gradient(to right, var(--teal-grean), var(--tropical-green2));
        border: none;
        border-radius: 0.5rem;
        padding: 0.8rem 0;
        font-weight: 600;
        font-size: 1rem;
        margin-top: 1rem;
        transition: all 0.3s ease;
    }

    .auth-button:hover {
        background: linear-gradient(to right, var(--teal-grean), var(--tropical-green2));
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .remember-me {
        margin-top: 1rem;
    }

    .form-check-input:checked {
        background-color: #1e88e5;
        border-color: #1e88e5;
    }

    .auth-footer {
        text-align: center;
        padding: 1.5rem;
        border-top: 1px solid #e0e0e0;
        color: #64748b;
    }

    .auth-link {
        color: var(--tropical-green2);
        font-weight: 600;
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .auth-link:hover {
        color: var(--teal-grean);
        text-decoration: underline;
    }

    .auth-divider {
        display: flex;
        align-items: center;
        margin: 1.5rem 0;
        color: #64748b;
    }

    .auth-divider::before,
    .auth-divider::after {
        content: "";
        flex: 1;
        border-bottom: 1px solid #e0e0e0;
    }

    .auth-divider::before {
        margin-right: 1rem;
    }

    .auth-divider::after {
        margin-left: 1rem;
    }

    .social-login {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .social-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: #f5f7fa;
        color: #64748b;
        transition: all 0.3s ease;
        border: 1px solid #e0e0e0;
    }

    .social-button:hover {
        background-color: #e0e0e0;
        transform: translateY(-2px);
    }

    .auth-help {
        text-align: center;
        margin-top: 1rem;
    }

    .password-toggle {
        cursor: pointer;
        position: absolute;
        top: 1.25rem;
        right: 1rem;
        color: #64748b;
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container mt-5 mb-5">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                <img src="{{url_for('static', filename='img/KAM-logo.png')}}" width="300" align="middle" />
            </div>
            <h1 class="auth-title">Welcome Back</h1>
            <p class="auth-subtitle">Sign in to access your investment dashboard</p>
        </div>
        <div class="auth-body">
            <form method="POST" action="{{ url_for('auth.login') }}">
                {{ form.hidden_tag() }}

                <div class="form-floating mb-3">
                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), placeholder="Email", id="floatingEmail") }}
                    <label for="floatingEmail">Email address</label>
                    {% if form.email.errors %}
                        {% for error in form.email.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="form-floating mb-3 position-relative">
                    {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="Password", id="floatingPassword") }}
                    <label for="floatingPassword">Password</label>
                    <span class="password-toggle" id="passwordToggle">
                        <i class="fas fa-eye"></i>
                    </span>
                    {% if form.password.errors %}
                        {% for error in form.password.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="remember-me d-flex justify-content-between">
                    <div class="form-check">
                        {{ form.remember(class="form-check-input", id="rememberCheck") }}
                        <label class="form-check-label" for="rememberCheck">Remember me</label>
                    </div>
                    <a href="#" class="auth-link">Forgot password?</a>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary auth-button">Sign In</button>
                </div>
            </form>

            <div class="auth-divider">or continue with</div>

            <div class="social-login">
                <a href="#" class="social-button">
                    <i class="fab fa-google"></i>
                </a>
                <a href="#" class="social-button">
                    <i class="fab fa-apple"></i>
                </a>
                <a href="#" class="social-button">
                    <i class="fab fa-microsoft"></i>
                </a>
            </div>

            <div class="auth-help">
                <span>Need help? <a href="#" class="auth-link">Contact support</a></span>
            </div>
        </div>
        <div class="auth-footer">
            <p class="mb-0">Don't have an account? <a href="{{ url_for('auth.register') }}" class="auth-link">Create account</a></p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    const passwordToggle = document.getElementById('passwordToggle');
    const passwordField = document.getElementById('floatingPassword');

    if (passwordToggle && passwordField) {
        passwordToggle.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);

            // Toggle eye icon
            const icon = passwordToggle.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    }
});
</script>
{% endblock %}