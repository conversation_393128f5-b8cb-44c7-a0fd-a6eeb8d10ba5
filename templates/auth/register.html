<style>
    .auth-link {
        color: var(--tropical-green2);
        font-weight: 600;
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .auth-link:hover {
        color: var(--teal-grean);
        text-decoration: underline;
    }
</style>

{% extends "base.html" %}

{% block title %}Register - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h4 class="mb-0">Register</h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.register') }}">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else ""), placeholder="Enter your name") }}
                        {% if form.name.errors %}
                            {% for error in form.name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), placeholder="Enter your email") }}
                        {% if form.email.errors %}
                            {% for error in form.email.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="Enter your password") }}
                        {% if form.password.errors %}
                            {% for error in form.password.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.password_confirm.label(class="form-label") }}
                        {{ form.password_confirm(class="form-control" + (" is-invalid" if form.password_confirm.errors else ""), placeholder="Confirm your password") }}
                        {% if form.password_confirm.errors %}
                            {% for error in form.password_confirm.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p >Already have an account? <a href="{{ url_for('auth.login') }}" class="auth-link">Login</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}