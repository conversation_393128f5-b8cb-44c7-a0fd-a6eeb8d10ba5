{% extends "base.html" %}

{% block title %}{{ security.name }} Prices - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ security.name }} ({{ security.symbol }}) Prices</h1>
            <div>
                <a href="{{ url_for('admin.securities') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Securities
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Security Info -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Security Information</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tbody>
                        <tr>
                            <th style="width: 40%">Symbol:</th>
                            <td>{{ security.symbol }}</td>
                        </tr>
                        <tr>
                            <th>Name:</th>
                            <td>{{ security.name }}</td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td>{{ security.type }}</td>
                        </tr>
                        <tr>
                            <th>Current Price:</th>
                            <td>${{ security.get_price()|round(2)|format_number }}</td>
                        </tr>
                        <tr>
                            <th>Added on:</th>
                            <td>{{ security.created_at.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        <tr>
                            <th>Last Updated:</th>
                            <td>{{ security.updated_at.strftime('%Y-%m-%d') }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add Price Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Add Price</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.security_prices', security_id=security.id) }}">
                    {{ form.hidden_tag() }}
                    {{ form.security_id }}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.date.label(class="form-label") }}
                                {{ form.date(class="form-control" + (" is-invalid" if form.date.errors else ""), type="date") }}
                                {% if form.date.errors %}
                                    {% for error in form.date.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% else %}
                                    <div class="form-text">Select the date for this price.</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.price.label(class="form-label") }}
                                {{ form.price(class="form-control" + (" is-invalid" if form.price.errors else ""), placeholder="Enter price") }}
                                {% if form.price.errors %}
                                    {% for error in form.price.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% else %}
                                    <div class="form-text">Enter the price for this date.</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Price Chart -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Price History</h5>
    </div>
    <div class="card-body">
        <div class="chart-container" style="height: 400px;">
            <canvas id="priceHistoryChart"></canvas>
        </div>
    </div>
</div>

<!-- Price History Table -->
<div class="card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Price History</h5>
    </div>
    <div class="card-body">
        {% if prices %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Price</th>
                            <th>Change</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for price in prices %}
                            <tr>
                                <td>{{ price.date.strftime('%Y-%m-%d') }}</td>
                                <td>${{ price.price|round(2)|format_number }}</td>
                                <td>
                                    {% if loop.index < prices|length %}
                                        {% set next_price = prices[loop.index] %}
                                        {% set change = price.price - next_price.price %}
                                        {% set change_pct = (change / next_price.price * 100) if next_price.price > 0 else 0 %}
                                        <span class="{{ 'text-success' if change >= 0 else 'text-danger' }}">
                                            {{ '+' if change >= 0 else '' }}{{ change|round(2) }}
                                            ({{ '+' if change_pct >= 0 else '' }}{{ change_pct|round(2) }}%)
                                        </span>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-warning edit-price-btn"
                                                data-price-id="{{ price.id }}"
                                                data-date="{{ price.date.strftime('%Y-%m-%d') }}"
                                                data-price="{{ price.price }}">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal-{{ price.id }}">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal-{{ price.id }}" tabindex="-1" aria-labelledby="deleteModalLabel-{{ price.id }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel-{{ price.id }}">Confirm Deletion</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>Are you sure you want to delete the price entry for <strong>{{ price.date.strftime('%Y-%m-%d') }}</strong>?</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form action="{{ url_for('admin.delete_price', security_id=security.id, price_id=price.id) }}" method="POST">
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info mb-0">
                No price history available for this security. Use the form above to add prices.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Price History Chart
    const priceChartCanvas = document.getElementById('priceHistoryChart');

    {% if prices %}
        const priceData = {
            labels: [
                {% for price in prices|reverse %}
                    "{{ price.date.strftime('%Y-%m-%d') }}",
                {% endfor %}
            ],
            datasets: [{
                label: '{{ security.symbol }} Price',
                data: [
                    {% for price in prices|reverse %}
                        {{ price.price }},
                    {% endfor %}
                ],
                borderColor: 'rgba(54, 162, 235, 1)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                fill: true
            }]
        };

        new Chart(priceChartCanvas, {
            type: 'line',
            data: priceData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += '$' + context.parsed.y.toLocaleString();
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    {% endif %}

    // Edit price button handlers
    const editButtons = document.querySelectorAll('.edit-price-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const priceId = this.dataset.priceId;
            const date = this.dataset.date;
            const price = this.dataset.price;

            // Update form fields
            document.getElementById('date').value = date;
            document.getElementById('price').value = price;

            // Update form action to edit endpoint
            const form = document.querySelector('form');
            form.action = "{{ url_for('admin.security_prices', security_id=security.id) }}?edit=" + priceId;

            // Update submit button text
            document.querySelector('button[type="submit"]').textContent = 'Update Price';

            // Scroll to form
            document.querySelector('.card-header:contains("Add Price")').scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
});
</script>
{% endblock %}