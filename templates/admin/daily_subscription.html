{% extends "base.html" %}

{% block title %}Daily Subscription for {{ sub_date.strftime('%Y-%m-%d') }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>Daily Subscription Process for {{ sub_date.strftime('%Y-%m-%d') }}</h1>
        <p>
            The following investments were submitted on {{ sub_date.strftime('%Y-%m-%d') }} and are pending subscription.
            When you click the button below, all pending investments will be processed together using the same subscription NAV.
        </p>

        {% if pending_investments %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Investment ID</th>
                        <th>Customer ID</th>
                        <th>Portfolio ID</th>
                        <th>Amount Invested</th>
                        <th>Created At</th>
                    </tr>
                </thead>
                <tbody>
                    {% for investment in pending_investments %}
                    <tr>
                        <td>{{ investment.id }}</td>
                        <td>{{ investment.customer_id }}</td>
                        <td>{{ investment.portfolio_id }}</td>
                        <td>${{ investment.amount|round(2)|format_number }}</td>
                        <td>{{ investment.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
            <div class="alert alert-info">
                There are no pending investments for this day.
            </div>
        {% endif %}

        <hr>
        <form method="POST" action="{{ url_for('admin.daily_subscription', sub_date=sub_date.strftime('%Y-%m-%d')) }}">
            <button type="submit" class="btn btn-primary">
                Subscribe All Pending Investments
            </button>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</div>
{% endblock %}
