{% extends "base.html" %}
{% block title %}Manage Portfolios - Admin{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1>Manage Portfolios</h1>
    </div>
</div>

{% if portfolio_data and portfolio_data|length > 0 %}
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Portfolio Name</th>
                <th>Risk Level</th>
                <th>Total Invested</th>
                <th>Current Value</th>
                <th>Number of Investments</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for entry in portfolio_data %}
            <tr>
                <td>{{ entry.portfolio.name }}</td>
                <td>
                    <span class="badge {% if entry.portfolio.risk_level == 'High' %}bg-danger{% elif entry.portfolio.risk_level == 'Medium' %}bg-warning{% else %}bg-success{% endif %}">
                        {{ entry.portfolio.risk_level }}
                    </span>
                </td>
                <td>${{ entry.invested|round(2)|format_number }}</td>
                <td>${{ entry.current_value|round(2)|format_number }}</td>
                <td>{{ entry.investments|length }}</td>
                <td>
                    <a href="{{ url_for('admin.portfolio_detail', portfolio_id=entry.portfolio.id) }}" class="btn btn-sm btn-primary">
                        View Details
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
    <div class="alert alert-info">
        No portfolios found.
    </div>
{% endif %}

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin portfolios page loaded.');
});
</script>
{% endblock %}
