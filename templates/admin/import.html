{% extends "base.html" %}

{% block title %}Import IBKR Report - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>Import IBKR Report</h1>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Import Form -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Upload Report</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.import_report') }}" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.report_file.label(class="form-label") }}
                        {{ form.report_file(class="form-control" + (" is-invalid" if form.report_file.errors else "")) }}
                        {% if form.report_file.errors %}
                            {% for error in form.report_file.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">Upload your IBKR broker report in JSON or CSV format.</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.file_type.label(class="form-label") }}
                        {{ form.file_type(class="form-select") }}
                        <div class="form-text">Select the format of the report you are uploading.</div>
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Import Instructions</h5>
            </div>
            <div class="card-body">
                <h5>IBKR Report Format</h5>
                <p>The system supports importing IBKR reports in both JSON and CSV formats. The following data will be extracted from the reports:</p>

                <ul>
                    <li><strong>Securities:</strong> Stocks, ETFs, and other instruments in the portfolio</li>
                    <li><strong>Prices:</strong> Current prices of securities</li>
                    <li><strong>Portfolio Composition:</strong> How the portfolio is allocated</li>
                    <li><strong>Performance Data:</strong> Historical performance metrics</li>
                </ul>

                <h5>How to Export Reports from IBKR</h5>
                <ol>
                    <li>Log in to your IBKR account</li>
                    <li>Go to Performance & Reports > Flex Queries</li>
                    <li>Create a new query with the following sections:</li>
                    <ul>
                        <li>Account Information</li>
                        <li>Net Asset Value</li>
                        <li>Open Positions</li>
                        <li>Trades</li>
                        <li>Cash Transactions</li>
                    </ul>
                    <li>Run the query and download in your preferred format (JSON or CSV)</li>
                </ol>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> After importing the report, you may need to update portfolio allocations and create relationships between securities and portfolios.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}