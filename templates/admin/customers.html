{% extends "base.html" %}

{% block title %}Manage Customers - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>Manage Customers</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newCustomerModal">
                <i class="fas fa-user-plus"></i> New Customer
            </button>
        </div>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Customers</h5>
    </div>
    <div class="card-body">
        {% if customers_data %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Created</th>
                            <th>Total Invested</th>
                            <th>Current Value</th>
                            <th>Return</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for cust in customers_data %}
                            <tr>
                                <td>{{ cust.name }}</td>
                                <td>{{ cust.email }}</td>
                                <td>{{ cust.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>${{ cust.total_invested|round(2)|format_number }}</td>
                                <td>${{ cust.current_value|round(2)|format_number }}</td>
                                <td class="{{ 'text-success' if cust.return_pct >= 0 else 'text-danger' }}">
                                    {{ cust.return_pct|round(2) }}%
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('admin.customer_detail', customer_id=cust.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
<!--                                        <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editModal-{{ cust.id }}">-->
<!--                                            <i class="fas fa-edit"></i> Edit-->
<!--                                        </button>-->
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal-{{ cust.id }}">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                    <!-- Edit and Delete modals go here if needed -->
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                No customers found. Click the "New Customer" button to add a customer.
            </div>
        {% endif %}
    </div>
</div>

<!-- New Customer Modal -->
<div class="modal fade" id="newCustomerModal" tabindex="-1" aria-labelledby="newCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newCustomerModalLabel">New Customer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin.new_customer') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin">
                        <label class="form-check-label" for="is_admin">Admin User</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Customer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // You can add additional JavaScript for dashboard interactions here
});
</script>
{% endblock %}
