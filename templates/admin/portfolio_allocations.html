{% extends "base.html" %}

{% block title %}Portfolio Allocations - {{ portfolio.name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ portfolio.name }} - Allocations</h1>
            <a href="{{ url_for('admin.portfolio_detail', portfolio_id=portfolio.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Portfolio
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Current Allocations -->
    <div class="col-md-7">
        <div class="card">
            <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Current Allocations</h5>
                <span id="totalAllocationDisplay" class="badge {{ 'bg-success' if allocations|sum(attribute='percentage') == 100 else 'bg-danger' }}">
                    Total: {{ allocations|sum(attribute='percentage')|round(2) }}%
                </span>
            </div>
            <div class="card-body">
                {% if allocations %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Security</th>
                                    <th>Symbol</th>
                                    <th>Type</th>
                                    <th>Allocation</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for allocation in allocations %}
                                    <tr>
                                        <td>{{ allocation.security.name }}</td>
                                        <td>{{ allocation.security.symbol }}</td>
                                        <td>{{ allocation.security.type }}</td>
                                        <td>{{ allocation.percentage }}%</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-warning edit-allocation-btn"
                                                    data-security-id="{{ allocation.security_id }}"
                                                    data-security-name="{{ allocation.security.name }}"
                                                    data-percentage="{{ allocation.percentage }}">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal-{{ allocation.id }}">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>

                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal-{{ allocation.id }}" tabindex="-1" aria-labelledby="deleteModalLabel-{{ allocation.id }}" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel-{{ allocation.id }}">Confirm Deletion</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>Are you sure you want to remove <strong>{{ allocation.security.name }} ({{ allocation.security.symbol }})</strong> from this portfolio?</p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <!--
                                                            <form action="{# url_for('admin.delete_allocation', portfolio_id=portfolio.id, allocation_id=allocation.id) #}" method="POST">
                                                                <button type="submit" class="btn btn-danger">Delete</button>
                                                            </form> 
                                                            -->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <div class="chart-container" style="height: 300px;">
                            <canvas id="portfolioAllocationChart" data-portfolio-id="{{ portfolio.id }}"></canvas>
                        </div>
                    </div>
                {% else %}
                    <div class="alert alert-info mb-0">
                        No allocations have been defined for this portfolio yet. Use the form to add securities to the portfolio.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Add/Edit Allocation Form -->
    <div class="col-md-5">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0" id="allocationFormTitle">Add Allocation</h5>
            </div>
            <div class="card-body">
                <form id="portfolioAllocationForm" method="POST" action="{{ url_for('admin.portfolio_allocations', portfolio_id=portfolio.id) }}">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.security_id.label(class="form-label") }}
                        {{ form.security_id(class="form-select" + (" is-invalid" if form.security_id.errors else "")) }}
                        {% if form.security_id.errors %}
                            {% for error in form.security_id.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.percentage.label(class="form-label") }}
                        {{ form.percentage(class="form-control allocation-input" + (" is-invalid" if form.percentage.errors else ""), placeholder="Enter allocation percentage") }}
                        {% if form.percentage.errors %}
                            {% for error in form.percentage.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">
                                Enter a percentage between 0 and 100. The total allocation across all securities should equal 100%.
                                <br><strong>Current total allocation: </strong><span id="totalAllocation">{{ allocations|sum(attribute='percentage')|round(2) }}%</span>
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <button type="button" id="cancelEditBtn" class="btn btn-secondary" style="display: none;">Cancel Edit</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Allocation Guidelines</h5>
            </div>
            <div class="card-body">
                <p>When creating portfolio allocations, consider the following guidelines:</p>
                <ul>
                    <li>The total allocation must equal 100%</li>
                    <li>Diversify across multiple sectors for better risk management</li>
                    <li>Consider the risk level of the portfolio when choosing securities</li>
                    <li>For higher risk portfolios, allocate more to growth-oriented securities</li>
                    <li>For lower risk portfolios, focus on stable, income-generating securities</li>
                </ul>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i> Changes to portfolio allocations will affect all customer investments in this portfolio.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/portfolio.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit allocation button clicks
    document.querySelectorAll('.edit-allocation-btn').forEach(button => {
        button.addEventListener('click', function() {
            const securityId = this.dataset.securityId;
            const securityName = this.dataset.securityName;
            const percentage = this.dataset.percentage;

            // Update form
            document.getElementById('security_id').value = securityId;
            document.getElementById('percentage').value = percentage;

            // Update form title
            document.getElementById('allocationFormTitle').textContent = `Edit Allocation: ${securityName}`;

            // Show cancel button
            document.getElementById('cancelEditBtn').style.display = 'block';

            // Focus on percentage input
            document.getElementById('percentage').focus();
        });
    });

    // Handle cancel edit button
    document.getElementById('cancelEditBtn').addEventListener('click', function() {
        // Reset form
        document.getElementById('portfolioAllocationForm').reset();

        // Reset form title
        document.getElementById('allocationFormTitle').textContent = 'Add Allocation';

        // Hide cancel button
        this.style.display = 'none';
    });
});
</script>
{% endblock %}