{% extends "base.html" %}

{% block title %}Admin Dashboard - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1>Admin Dashboard</h1>
    </div>
</div>

<!-- Summary Cards Row -->
<div class="row mb-4">
    <!-- Card 1: Customers -->
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5 class="card-title">Customers</h5>
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="card-text">{{ customer_count }}</h2>
                    <i class="fas fa-users fa-3x"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <small>Total Customers</small>
                <a href="{{ url_for('admin.customers') }}" class="text-white">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Card 2: Portfolios -->
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">Portfolios</h5>
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="card-text">{{ portfolio_count }}</h2>
                    <i class="fas fa-chart-pie fa-3x"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <small>Total Portfolios</small>
                <a href="{{ url_for('admin.portfolios') }}" class="text-white">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Card 3: Investments -->
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5 class="card-title">Investments</h5>
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="card-text">{{ investment_count }}</h2>
                    <i class="fas fa-money-bill-wave fa-3x"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <small>Total Investments</small>
                <a href="{{ url_for('admin.all_investments') }}" class="text-white">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!--&lt;!&ndash; Portfolios Table Section &ndash;&gt;-->
<!--<div class="row mb-4">-->
<!--    <div class="col-12">-->
<!--        <h2>Portfolios</h2>-->
<!--        {% if portfolio_stats and portfolio_stats|length > 0 %}-->
<!--        <div class="table-responsive">-->
<!--            <table class="table table-striped">-->
<!--                <thead>-->
<!--                    <tr>-->
<!--                        <th>Name</th>-->
<!--                        <th>Risk Level</th>-->
<!--                        <th>Total Invested</th>-->
<!--                        <th>Investors</th>-->
<!--                        <th>Actions</th>-->
<!--                    </tr>-->
<!--                </thead>-->
<!--                <tbody>-->
<!--                    {% for portfolio in portfolio_stats %}-->
<!--                    <tr>-->
<!--                        <td>{{ portfolio.name }}</td>-->
<!--                        <td>-->
<!--                            <span class="badge-->
<!--                                {% if portfolio.risk_level == 'High' %}bg-danger-->
<!--                                {% elif portfolio.risk_level == 'Medium' %}bg-warning-->
<!--                                {% else %}bg-success{% endif %}">-->
<!--                                {{ portfolio.risk_level }}-->
<!--                            </span>-->
<!--                        </td>-->
<!--                        <td>${{ portfolio.total_invested|round(2)|format_number }}</td>-->
<!--                        <td>{{ portfolio.investor_count }}</td>-->
<!--                        <td>-->
<!--                            <a href="{{ url_for('admin.portfolio_detail', portfolio_id=portfolio.id) }}" class="btn btn-sm btn-primary">-->
<!--                                View-->
<!--                            </a>-->
<!--                        </td>-->
<!--                    </tr>-->
<!--                    {% endfor %}-->
<!--                </tbody>-->
<!--            </table>-->
<!--        </div>-->
<!--        {% else %}-->
<!--            <div class="alert alert-info">-->
<!--                No portfolios found.-->
<!--            </div>-->
<!--        {% endif %}-->
<!--    </div>-->
<!--</div>-->

<!-- Pending Subscription Dates Section -->
{% if pending_dates and pending_dates|length > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-header">
                <h5 class="card-title mb-0">Pending Subscription Dates</h5>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    {% for date_str in pending_dates %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Investments pending for {{ date_str }}
                        <a href="{{ url_for('admin.daily_subscription', sub_date=date_str) }}" class="btn btn-sm btn-info">
                            Subscribe
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endif %}



<!-- Something like this in your summary card -->

<!--&lt;!&ndash; IBKR Report Summary Section &ndash;&gt;-->
<!--{% if snapshot and report_data %}-->
<!--<div class="card mb-4">-->
<!--    <div class="card-header bg-info text-white">-->
<!--        <h5 class="card-title mb-0">IBKR Report Summary</h5>-->
<!--    </div>-->
<!--    <div class="card-body">-->
<!--<p><strong>Report Date:</strong> {{ snapshot.date.strftime("%Y-%m-%d") }}</p>-->
<!--{% set acct_rows = report_data["sections"]["Account Information"]["tables"][0]["rows"] %}-->
<!--{% for row in acct_rows %}-->
<!--  {% if row["Field Name"]|lower == "name" %}-->
<!--    <p><strong>Account Name:</strong> {{ row["Field Value"] }}</p>-->
<!--  {% elif row["Field Name"]|lower == "account" %}-->
<!--    <p><strong>Account Number:</strong> {{ row["Field Value"] }}</p>-->
<!--  {% elif row["Field Name"]|lower == "base currency" %}-->
<!--    <p><strong>Base Currency:</strong> {{ row["Field Value"] }}</p>-->
<!--  {% endif %}-->
<!--{% endfor %}-->
<!--{% if report_data.get("key_statistics") and report_data["key_statistics"].get("deposits_withdrawals") %}-->
<!--<p><strong>Deposits/Withdrawals:</strong> {{ report_data["key_statistics"]["deposits_withdrawals"] }}</p>-->
<!--{% endif %}-->
<!--<p><strong>Ending NAV:</strong> ${{ snapshot.total_value|round(2)|format_number }}</p>-->
<!--    </div>-->
<!--</div>-->
<!--&lt;!&ndash;{% else %}&ndash;&gt;-->
<!--&lt;!&ndash;<div class="alert alert-warning">&ndash;&gt;-->
<!--&lt;!&ndash;    IBKR report data is not available.&ndash;&gt;-->
<!--&lt;!&ndash;</div>&ndash;&gt;-->
<!--{% endif %}-->

<!-- Portfolio Statistics -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Portfolio Statistics</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Portfolio</th>
                        <th>Risk Level</th>
                        <th>Total Invested</th>
                        <th>Investors</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for portfolio in portfolio_stats %}
                        <tr>
                            <td>{{ portfolio.name }}</td>
                            <td>
                                <span class="badge {{ 'bg-danger' if portfolio.risk_level == 'High' else 'bg-warning' if portfolio.risk_level == 'Medium' else 'bg-success' }}">
                                    {{ portfolio.risk_level }}
                                </span>
                            </td>
                            <td>${{ portfolio.total_invested|round(2)|format_number }}</td>
                            <td>{{ portfolio.investor_count }}</td>
                            <td>
                                <a href="{{ url_for('admin.portfolio_detail', portfolio_id=portfolio.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Recent Investments Table in Admin Dashboard -->
<div class="card">
  <div class="card-header bg-dark text-white">
    <h5 class="card-title mb-0">Recent Investments</h5>
  </div>
  <div class="card-body">
    {% if recent_investments %}
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead>
            <tr>
              <th>Customer</th>
              <th>Portfolio</th>
              <th>Amount</th>
              <th>Start Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for investment in recent_investments %}
              <tr>
                <td>{{ investment.customer.name }}</td>
                <td>{{ investment.portfolio.name }}</td>
                <td>${{ investment.amount|round(2)|format_number }}</td>
                <td>{{ investment.start_date.strftime('%Y-%m-%d') }}</td>
                <td>
                  {% if not investment.is_active %}
                    <span class="badge bg-secondary">Closed</span>
                  {% elif not investment.is_funded %}
                    <span class="badge bg-warning">Pending Funding</span>
                  {% else %}
                    <span class="badge bg-success">Active</span>
                  {% endif %}
                </td>
                <td>
                  <!-- FIX: Use the customer.investment_detail endpoint -->
<!-- In admin_dashboard.html, within a loop over recent_investments -->
<a href="{{ url_for('admin.investment_detail', investment_id=investment.id) }}" class="btn btn-sm btn-primary">
    <i class="fas fa-eye"></i> View Details
</a>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    {% else %}
      <div class="alert alert-info">
        No recent investments found.
      </div>
    {% endif %}
  </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any dashboard-specific scripts here
    console.log('Admin dashboard loaded');

    // Example: Auto-refresh dashboard data every 5 minutes
    /*
    setInterval(function() {
        fetch('/api/admin/dashboard-data')
            .then(response => response.json())
            .then(data => {
                // Update dashboard with new data
                console.log('Dashboard data updated');
            })
            .catch(error => {
                console.error('Error updating dashboard:', error);
            });
    }, 300000); // 5 minutes
    */
});
</script>
{% endblock %}