{# templates/admin/admin_all_investments.html #}
{% extends "base.html" %}
{% block title %}All Investments - Admin{% endblock %}

{% block content %}
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <h1>All Investments</h1>
      <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
      </a>
    </div>
  </div>
</div>

<div class="card">
  <div class="card-header bg-dark text-white">
    <h5 class="card-title mb-0">Investments List</h5>
  </div>
  <div class="card-body">
    {% if investments %}
      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>ID</th>
              <th>Customer</th>
              <th>Portfolio</th>
              <th>Amount</th>
              <th>Start Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for inv in investments %}
              <tr>
                <td>{{ inv.id }}</td>
                <td>{{ inv.customer.name }}</td>
                <td>{{ inv.portfolio.name }}</td>
                <td>${{ inv.amount|round(2)|format_number }}</td>
                <td>{{ inv.start_date.strftime('%Y-%m-%d') }}</td>
                <td>
                  {% if not inv.is_active %}
                    <span class="badge bg-secondary">Closed</span>
                  {% elif not inv.is_funded %}
                    <span class="badge bg-warning">Pending Subscription</span>
                  {% else %}
                    <span class="badge bg-success">Active</span>
                  {% endif %}
                </td>
                <td>
                  <a href="{{ url_for('admin.investment_detail', investment_id=inv.id) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> Details
                  </a>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    {% else %}
      <div class="alert alert-info">No investments found.</div>
    {% endif %}
  </div>
</div>
{% endblock %}
