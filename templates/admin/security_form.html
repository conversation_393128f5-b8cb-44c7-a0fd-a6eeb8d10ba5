{% extends "base.html" %}

{% block title %}{{ title }} - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ title }}</h1>
            <a href="{{ url_for('admin.securities') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Securities
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Security Details</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.new_security') if not security else url_for('admin.edit_security', security_id=security.id) }}">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.symbol.label(class="form-label") }}
                        {{ form.symbol(class="form-control" + (" is-invalid" if form.symbol.errors else ""), placeholder="Enter security symbol") }}
                        {% if form.symbol.errors %}
                            {% for error in form.symbol.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">Enter the ticker symbol for this security (e.g., AAPL, SPY).</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else ""), placeholder="Enter security name") }}
                        {% if form.name.errors %}
                            {% for error in form.name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">Enter the full name of the security (e.g., Apple Inc., SPDR S&P 500 ETF).</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.type.label(class="form-label") }}
                        {{ form.type(class="form-select" + (" is-invalid" if form.type.errors else "")) }}
                        {% if form.type.errors %}
                            {% for error in form.type.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% else %}
                            <div class="form-text">Select the type of security.</div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Security Information</h5>
            </div>
            <div class="card-body">
                <h6 class="mb-3">Security Types</h6>
                <ul>
                    <li><strong>Stock</strong>: Represents ownership in a company (e.g., Apple, Microsoft).</li>
                    <li><strong>ETF</strong>: Exchange Traded Fund that holds a basket of securities (e.g., SPY, QQQ).</li>
                    <li><strong>Bond</strong>: Fixed income security representing a loan to a corporation or government.</li>
                    <li><strong>Mutual Fund</strong>: Professionally managed investment fund that pools money from investors.</li>
                    <li><strong>Cash</strong>: Cash or cash equivalents like money market funds.</li>
                    <li><strong>Other</strong>: Any other security type not listed above.</li>
                </ul>

                <h6 class="mb-3 mt-4">Next Steps</h6>
                <p>After creating a security, you'll need to:</p>
                <ol>
                    <li>Add price information for the security</li>
                    <li>Add the security to one or more portfolios</li>
                    <li>Set allocation percentages in each portfolio</li>
                </ol>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i> You can also import securities and price data from your broker using the Import tool.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}