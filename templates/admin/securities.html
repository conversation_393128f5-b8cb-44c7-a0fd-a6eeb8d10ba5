{% extends "base.html" %}

{% block title %}Manage Securities - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>Manage Securities</h1>
            <a href="{{ url_for('admin.new_security') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Security
            </a>
        </div>
    </div>
</div>

<!-- Securities Table -->
<div class="card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Securities</h5>
    </div>
    <div class="card-body">
        {% if securities %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Current Price</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for security in securities %}
                            <tr>
                                <td>{{ security.symbol }}</td>
                                <td>{{ security.name }}</td>
                                <td>{{ security.type }}</td>
                                <td>${{ security.get_price()|round(2)|format_number }}</td>
                                <td>{{ security.updated_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('admin.security_prices', security_id=security.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-dollar-sign"></i> Prices
                                        </a>
                                        <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editModal-{{ security.id }}">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal-{{ security.id }}">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>

                                    <!-- Edit Modal -->
                                    <div class="modal fade" id="editModal-{{ security.id }}" tabindex="-1" aria-labelledby="editModalLabel-{{ security.id }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="editModalLabel-{{ security.id }}">Edit Security</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <form action="{# url_for('admin.edit_security', security_id=security.id) #}" method="POST">
                                                    <div class="modal-body">
                                                        <div class="mb-3">
                                                            <label for="symbol-{{ security.id }}" class="form-label">Symbol</label>
                                                            <input type="text" class="form-control" id="symbol-{{ security.id }}" name="symbol" value="{{ security.symbol }}" required>
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="name-{{ security.id }}" class="form-label">Name</label>
                                                            <input type="text" class="form-control" id="name-{{ security.id }}" name="name" value="{{ security.name }}" required>
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="type-{{ security.id }}" class="form-label">Type</label>
                                                            <select class="form-select" id="type-{{ security.id }}" name="type" required>
                                                                <option value="Stock" {% if security.type == 'Stock' %}selected{% endif %}>Stock</option>
                                                                <option value="ETF" {% if security.type == 'ETF' %}selected{% endif %}>ETF</option>
                                                                <option value="Bond" {% if security.type == 'Bond' %}selected{% endif %}>Bond</option>
                                                                <option value="Mutual Fund" {% if security.type == 'Mutual Fund' %}selected{% endif %}>Mutual Fund</option>
                                                                <option value="Cash" {% if security.type == 'Cash' %}selected{% endif %}>Cash</option>
                                                                <option value="Other" {% if security.type == 'Other' %}selected{% endif %}>Other</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <button type="submit" class="btn btn-primary">Save Changes</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal-{{ security.id }}" tabindex="-1" aria-labelledby="deleteModalLabel-{{ security.id }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel-{{ security.id }}">Confirm Deletion</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>Are you sure you want to delete <strong>{{ security.name }} ({{ security.symbol }})</strong>?</p>
                                                    <div class="alert alert-warning">
                                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                                        This will remove the security from all portfolios and delete all price history. This action cannot be undone.
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form action="{# url_for('admin.delete_security', security_id=security.id) #}" method="POST">
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                No securities have been added yet. Click the "New Security" button to add your first security.
            </div>
        {% endif %}
    </div>
</div>

<!-- Securities Chart -->
<div class="card mt-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Securities by Type</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="chart-container" style="height: 400px;">
                    <canvas id="securitiesChart"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">Summary</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Count</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set security_types = {} %}
                                {% for security in securities %}
                                    {% if security.type in security_types %}
                                        {% if security_types.update({security.type: security_types[security.type] + 1}) %}{% endif %}
                                    {% else %}
                                        {% if security_types.update({security.type: 1}) %}{% endif %}
                                    {% endif %}
                                {% endfor %}

                                {% for type, count in security_types.items() %}
                                    <tr>
                                        <td>{{ type }}</td>
                                        <td>{{ count }}</td>
                                        <td>{{ (count / securities|length * 100)|round(1) }}%</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Securities Section -->
<div class="card mt-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Import Securities</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <form action="{# url_for('admin.import_securities') #}" method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="securityFile" class="form-label">Securities CSV File</label>
                        <input class="form-control" type="file" id="securityFile" name="securities_file" accept=".csv">
                        <div class="form-text">Upload a CSV file with securities data.</div>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Import Securities
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-md-6">
                <div class="alert alert-info">
                    <h6 class="fw-bold">CSV Format Requirements</h6>
                    <p>Your CSV file should include the following columns:</p>
                    <ul class="mb-0">
                        <li>symbol (required): Security ticker symbol</li>
                        <li>name (required): Full name of the security</li>
                        <li>type (required): Type of security (Stock, ETF, Bond, etc.)</li>
                        <li>price (optional): Current price of the security</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Create the securities chart
    const securitiesCtx = document.getElementById('securitiesChart');

    if (securitiesCtx && {{ securities|length }} > 0) {
        // Count securities by type
        const securityTypes = {};
        {% for security in securities %}
            if ('{{ security.type }}' in securityTypes) {
                securityTypes['{{ security.type }}']++;
            } else {
                securityTypes['{{ security.type }}'] = 1;
            }
        {% endfor %}

        // Convert to arrays for Chart.js
        const types = Object.keys(securityTypes);
        const counts = types.map(type => securityTypes[type]);

        // Colors
        const colors = [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(201, 203, 207, 0.7)'
        ];

        // Create chart
        new Chart(securitiesCtx, {
            type: 'pie',
            data: {
                labels: types,
                datasets: [{
                    data: counts,
                    backgroundColor: colors.slice(0, types.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const percentage = (value / counts.reduce((a, b) => a + b, 0) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}