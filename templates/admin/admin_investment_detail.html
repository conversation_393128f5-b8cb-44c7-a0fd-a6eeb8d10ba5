{# templates/admin/admin_investment_detail.html #}
{% extends "base.html" %}
{% block title %}Investment Details - Admin{% endblock %}

{% block content %}
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <h1>Investment Details (Admin View)</h1>
      <a href="{{ url_for('admin.all_investments') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to All Investments
      </a>
    </div>
  </div>
</div>

<div class="card mb-4">
  <div class="card-header bg-dark text-white">
    <h5 class="card-title mb-0">Investment Summary</h5>
  </div>
  <div class="card-body">
    <table class="table">
      <tbody>
        <tr>
          <th>ID:</th>
          <td>{{ investment.id }}</td>
        </tr>
        <tr>
          <th>Customer:</th>
          <td>{{ investment.customer.name }}</td>
        </tr>
        <tr>
          <th>Portfolio:</th>
          <td>{{ investment.portfolio.name }}</td>
        </tr>
        <tr>
          <th>Amount Invested:</th>
          <td>${{ investment.amount|round(2)|format_number }}</td>
        </tr>
        <tr>
          <th>Start Date:</th>
          <td>{{ investment.start_date.strftime('%Y-%m-%d') }}</td>
        </tr>
        <tr>
          <th>Units:</th>
          <td>{{ investment.units|round(4)|format_number }}</td>
        </tr>
        <tr>
          <th>Entry NAV:</th>
          <td>${{ investment.entry_nav|round(2)|format_number }}</td>
        </tr>
        <tr>
          <th>Status:</th>
          <td>
            {% if not investment.is_active %}
                <span class="badge bg-secondary">Closed</span>
            {% elif not investment.is_funded %}
                <span class="badge bg-warning">Pending Subscription</span>
            {% else %}
                <span class="badge bg-success">Active</span>
            {% endif %}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div class="card">
  <div class="card-header bg-dark text-white">
    <h5 class="card-title mb-0">Performance History</h5>
  </div>
  <div class="card-body">
    {% if performances %}
      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Date</th>
              <th>Value</th>
              <th>Return (%)</th>
            </tr>
          </thead>
          <tbody>
            {% for performance in performances %}
            <tr>
              <td>{{ performance.date.strftime('%Y-%m-%d') }}</td>
              <td>${{ performance.value|round(2)|format_number }}</td>
              <td>{{ performance.return_pct|round(2) }}%</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    {% else %}
      <div class="alert alert-info">No performance data available.</div>
    {% endif %}
  </div>
</div>
{% endblock %}
