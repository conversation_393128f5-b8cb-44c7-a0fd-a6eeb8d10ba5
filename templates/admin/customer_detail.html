{% extends "base.html" %}

{% block title %}{{ customer.name }} - Portfolio Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ customer.name }}</h1>
            <div>
                <a href="{{ url_for('admin.customers') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left"></i> Back to Customers
                </a>
                <button type="button" class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#editCustomerModal">
                    <i class="fas fa-edit"></i> Edit Customer
                </button>
                <a href="{{ url_for('admin.new_investment_for_customer', customer_id=customer.id) }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New Investment
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Customer Details -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Customer Information</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tbody>
                        <tr>
                            <th style="width: 40%">Name:</th>
                            <td>{{ customer.name }}</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>{{ customer.email }}</td>
                        </tr>
                        <tr>
                            <th>Joined:</th>
                            <td>{{ customer.created_at.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        <tr>
                            <th>Last Updated:</th>
                            <td>{{ customer.updated_at.strftime('%Y-%m-%d') }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="row">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">Total Invested</h5>
                        <h2 class="card-text">${{ total_invested|round(2)|format_number }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title">Current Value</h5>
                        <h2 class="card-text">${{ total_current_value|round(2)|format_number }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card {{ 'bg-success' if total_return >= 0 else 'bg-danger' }} text-white">
                    <div class="card-body">
                        <h5 class="card-title">Total Return</h5>
                        <h2 class="card-text">{{ total_return|round(2) }}%</h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">Portfolio Allocation</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 250px;">
                    <canvas id="portfolioAllocationChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Investments Table -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Investments</h5>
    </div>
    <div class="card-body">
        {% if investments %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Portfolio</th>
                            <th>Risk Level</th>
                            <th>Amount</th>
                            <th>Current Value</th>
                            <th>Return</th>
                            <th>Start Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for investment in investments %}
                            <tr>
                                <td>{{ investment.portfolio.name }}</td>
                                <td>
                                    <span class="badge {{ 'bg-danger' if investment.portfolio.risk_level == 'High' else 'bg-warning' if investment.portfolio.risk_level == 'Medium' else 'bg-success' }}">
                                        {{ investment.portfolio.risk_level }}
                                    </span>
                                </td>
                                <td>${{ investment.amount|round(2)|format_number }}</td>
                                <td>${{ investment.get_current_value()|round(2)|format_number }}</td>
                                <td class="{{ 'text-success' if investment.get_return() >= 0 else 'text-danger' }}">
                                    {{ investment.get_return()|round(2) }}%
                                </td>
                                <td>{{ investment.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if not investment.is_active %}
                                        <span class="badge bg-secondary">Closed</span>
                                    {% elif not investment.is_funded %}
                                        <span class="badge bg-warning">Pending Funding</span>
                                    {% else %}
                                        <span class="badge bg-success">Active</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('customer.investment_detail', investment_id=investment.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        {% if investment.is_active and investment.is_funded %}
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#closeInvestmentModal-{{ investment.id }}">
                                                <i class="fas fa-times"></i> Close
                                            </button>
                                        {% endif %}
                                    </div>

                                    {% if investment.is_active and investment.is_funded %}
                                        <!-- Close Investment Modal -->
                                        <div class="modal fade" id="closeInvestmentModal-{{ investment.id }}" tabindex="-1" aria-labelledby="closeInvestmentModalLabel-{{ investment.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="closeInvestmentModalLabel-{{ investment.id }}">Close Investment</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to close this investment in <strong>{{ investment.portfolio.name }}</strong>?</p>
                                                        <div class="alert alert-info">
                                                            <p class="mb-1"><strong>Current Value:</strong> ${{ investment.get_current_value()|round(2)|format_number }}</p>
                                                            <p class="mb-0"><strong>Return:</strong> {{ investment.get_return()|round(2) }}%</p>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form action="{{ url_for('customer.close_investment', investment_id=investment.id, next=request.path) }}" method="POST">
                                                            <button type="submit" class="btn btn-danger">Close Investment</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info mb-0">
                This customer doesn't have any investments yet. Click the "New Investment" button to create one.
            </div>
        {% endif %}
    </div>
</div>


<!-- Performance Chart -->
<div class="card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">Performance History</h5>
    </div>
    <div class="card-body">
        <div class="chart-container" style="height: 400px;">
            <canvas id="performanceChart"></canvas>
        </div>
    </div>
</div>

<!-- Edit Customer Modal -->
<div class="modal fade" id="editCustomerModal" tabindex="-1" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCustomerModalLabel">Edit Customer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin.edit_customer', customer_id=customer.id, next=request.path) }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ customer.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ customer.email }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="password" name="password">
                        <div class="form-text">Leave blank to keep the current password.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Portfolio Allocation Chart
    const allocationCtx = document.getElementById('portfolioAllocationChart');

    if (allocationCtx) {
        // Group investments by portfolio
        const portfolios = {};
        {% for investment in investments %}
            {% if investment.is_active %}
                const portfolioName = "{{ investment.portfolio.name }}";
                const currentValue = {{ investment.get_current_value() }};

                if (portfolioName in portfolios) {
                    portfolios[portfolioName] += currentValue;
                } else {
                    portfolios[portfolioName] = currentValue;
                }
            {% endif %}
        {% endfor %}

        // Convert to arrays for Chart.js
        const portfolioNames = Object.keys(portfolios);
        const portfolioValues = portfolioNames.map(name => portfolios[name]);

        // Colors
        const colors = [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(201, 203, 207, 0.7)'
        ];

        // Create chart
        new Chart(allocationCtx, {
            type: 'doughnut',
            data: {
                labels: portfolioNames,
                datasets: [{
                    data: portfolioValues,
                    backgroundColor: colors.slice(0, portfolioNames.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((total, value) => total + value, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: $${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Performance Chart - Sample data over time
    const performanceCtx = document.getElementById('performanceChart');

    if (performanceCtx) {
        // Create sample dates for last 12 months
        const dates = [];
        const now = new Date();
        for (let i = 11; i >= 0; i--) {
            const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
            dates.push(month.toLocaleString('default', { month: 'short', year: 'numeric' }));
        }

        // Create datasets for each active investment
        const datasets = [
            {% for investment in investments %}
                {% if investment.is_active %}
                {
                    label: "{{ investment.portfolio.name }}",
                    data: [
                        // Generate sample performance data (replace with actual data in production)
                        {% for i in range(12) %}
                            {{ (investment.amount * (1 + (loop.index0 * 0.01) + (i * 0.005)))|round(2) }},
                        {% endfor %}
                    ],
                    borderColor: getRandomColor({{ loop.index0 }}),
                    backgroundColor: getRandomColor({{ loop.index0 }}, 0.1),
                    fill: false
                },
                {% endif %}
            {% endfor %}
            // Add total portfolio value
            {
                label: "Total Portfolio Value",
                data: [
                    // Generate sample total value
                    {% for i in range(12) %}
                        {{ (total_invested * (1 + (loop.index0 * 0.01) + (i * 0.003)))|round(2) }},
                    {% endfor %}
                ],
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderWidth: 3,
                fill: true
            }
        ];

        new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += '$' + context.parsed.y.toLocaleString();
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    }

    // Helper function to generate random colors
    function getRandomColor(index, alpha = 1) {
        const colors = [
            `rgba(54, 162, 235, ${alpha})`,
            `rgba(255, 99, 132, ${alpha})`,
            `rgba(255, 206, 86, ${alpha})`,
            `rgba(75, 192, 192, ${alpha})`,
            `rgba(153, 102, 255, ${alpha})`,
            `rgba(255, 159, 64, ${alpha})`,
            `rgba(201, 203, 207, ${alpha})`,
            `rgba(255, 99, 71, ${alpha})`
        ];

        return colors[index % colors.length];
    }
});
</script>
{% endblock %}