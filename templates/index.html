{% extends "base.html" %}

{% block title %}Welcome - Portfolio Dashboard{% endblock %}

{% block extra_head %}
<style>
    .hero-section {
        background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
        background-size: 600% 600%;
        animation: gradientBG 30s ease infinite;
        color: white;
        padding: 4rem 0;
        border-radius: 0.5rem;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }

    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }

    .hero-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect fill="rgba(255,255,255,0.05)" width="50" height="50" x="0" y="0" /><rect fill="rgba(255,255,255,0.05)" width="50" height="50" x="50" y="50" /></svg>');
        opacity: 0.3;
    }

    .feature-card {
        border: none;
        border-radius: 12px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .primary-icon {
        background: linear-gradient(135deg, #6e8efb, #a777e3);
    }

    .success-icon {
        background: linear-gradient(135deg, #42e695, #3bb2b8);
    }

    .info-icon {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
    }

    .feature-title {
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .action-button {
        border-radius: 50px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }

    .action-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .login-button {
        background: linear-gradient(135deg, #2193b0, #6dd5ed);
        border: none;
    }

    .register-button {
        background: linear-gradient(135deg, #11998e, #38ef7d);
        border: none;
    }

    .stats-section {
        padding: 2rem 0;
        background-color: #f8f9fa;
        border-radius: 12px;
        margin-top: 3rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #343a40;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 500;
    }

    .testimonial-section {
        margin-top: 4rem;
    }

    .testimonial-card {
        padding: 2rem;
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .testimonial-text {
        font-style: italic;
        color: #495057;
        margin-bottom: 1.5rem;
    }

    .client-info {
        display: flex;
        align-items: center;
    }

    .client-image {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-right: 1rem;
        background-color: #dee2e6;
    }

    .client-name {
        font-weight: 600;
        margin-bottom: 0;
    }

    .client-position {
        color: #6c757d;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <h1 class="display-3 fw-bold mb-4">Sophisticated Investment Management Platform</h1>
                <p class="lead fs-4 mb-5">Gain unparalleled insights into your portfolio with institutional-grade analytics and comprehensive performance monitoring.</p>
                <div class="d-flex justify-content-center mt-5">
                    <a href="{{ url_for('auth.login') }}" class="btn action-button login-button me-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Access Dashboard
                    </a>
                    <a href="{{ url_for('auth.register') }}" class="btn action-button register-button">
                        <i class="fas fa-user-plus me-2"></i>Create Account
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="container">
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="feature-card card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon primary-icon">
                        <i class="fas fa-chart-pie fa-2x text-white"></i>
                    </div>
                    <h3 class="feature-title">Strategic Portfolio Management</h3>
                    <p class="card-text">Efficiently manage diverse investment portfolios with sophisticated risk profiling and optimal asset allocation strategies.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="feature-card card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon success-icon">
                        <i class="fas fa-chart-line fa-2x text-white"></i>
                    </div>
                    <h3 class="feature-title">Advanced Performance Analytics</h3>
                    <p class="card-text">Monitor performance with institutional-grade metrics including Sharpe ratio, maximum drawdown, and benchmark comparisons.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="feature-card card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon info-icon">
                        <i class="fas fa-file-alt fa-2x text-white"></i>
                    </div>
                    <h3 class="feature-title">Comprehensive Reporting Suite</h3>
                    <p class="card-text">Generate detailed investment reports with interactive visualizations for clear performance attribution and risk assessment.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="container">
    <div class="stats-section">
        <div class="row">
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-number">$2.5B+</div>
                    <div class="stat-label">Assets Managed</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Institutional Clients</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">Years Experience</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="container testimonial-section">
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="testimonial-card">
                <p class="testimonial-text">"This platform has revolutionized how we manage our investment portfolio. The analytics tools and reporting capabilities exceed what we've seen elsewhere in the market."</p>
                <div class="client-info">
                    <div class="client-image"></div>
                    <div>
                        <p class="client-name">Jonathan Holloway</p>
                        <p class="client-position">Chief Investment Officer, Meridian Capital</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="testimonial-card">
                <p class="testimonial-text">"The depth of analysis and portfolio insights available through this platform have significantly improved our investment decision making and client reporting."</p>
                <div class="client-info">
                    <div class="client-image"></div>
                    <div>
                        <p class="client-name">Sophia Richardson</p>
                        <p class="client-position">Portfolio Manager, Apex Investments</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add subtle fade-in animation to the feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 200 + (index * 150));
    });
});
</script>
{% endblock %}