name: <PERSON><PERSON><PERSON> App CI/CD

on:
  push:
    branches:
      - main
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.11.5'

      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1
          
      - name: Build Docker image
        run: |
          docker build -t 977461862622.dkr.ecr.eu-west-1.amazonaws.com/k-report-backend:latest .

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        
      - name: Push Docker image to ECR
        env:
          ECR_REGISTRY: 977461862622.dkr.ecr.eu-west-1.amazonaws.com
        run: |
          docker tag 977461862622.dkr.ecr.eu-west-1.amazonaws.com/k-report-backend:latest $ECR_REGISTRY/k-report-backend:latest
          docker push $ECR_REGISTRY/k-report-backend:latest