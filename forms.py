from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileRequired, FileAllowed
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField
from wtforms import FloatField, DateField, SelectField, TextAreaField
from wtforms.validators import <PERSON>Required, Email, EqualTo, Length, Optional, NumberRange
from datetime import datetime

class LoginForm(FlaskForm):
    """Form for user login"""
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember = <PERSON><PERSON>anField('Remember Me')
    submit = SubmitField('Log In')

class RegistrationForm(FlaskForm):
    """Form for customer registration"""
    name = StringField('Name', validators=[DataRequired(), Length(min=2, max=100)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=8, message="Password must be at least 8 characters long")
    ])
    password_confirm = PasswordField('Confirm Password', validators=[
        DataRequired(),
        EqualTo('password', message='Passwords must match')
    ])
    submit = SubmitField('Register')

class NewInvestmentForm(FlaskForm):
    """Form for creating a new investment"""
    portfolio_id = SelectField('Portfolio', coerce=int, validators=[DataRequired()])
    amount = FloatField('Investment Amount ($)', validators=[
        DataRequired(),
        NumberRange(min=100000, message="Minimum investment amount is $100,000")
    ])
    start_date = DateField('Start Date', default=datetime.utcnow, validators=[DataRequired()])
    paper_investment = BooleanField('Paper Trading Investment')
    submit = SubmitField('Create Investment')

class ImportReportForm(FlaskForm):
    """Form for importing IBKR reports"""
    report_file = FileField('Report File', validators=[
        FileRequired(),
        FileAllowed(['json', 'csv'], 'Only JSON and CSV files are allowed')
    ])
    file_type = SelectField('File Type', choices=[
        ('json', 'JSON'),
        ('csv', 'CSV')
    ], default='json')
    submit = SubmitField('Import Report')

class PortfolioForm(FlaskForm):
    """Form for creating or editing a portfolio"""
    name = StringField('Portfolio Name', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    risk_level = SelectField('Risk Level', choices=[
        ('Low', 'Low Risk'),
        ('Medium', 'Medium Risk'),
        ('High', 'High Risk')
    ], default='Medium')
    submit = SubmitField('Save Portfolio')

class AllocationForm(FlaskForm):
    """Form for adding or editing portfolio allocations"""
    security_id = SelectField('Security', coerce=int, validators=[DataRequired()])
    percentage = FloatField('Allocation Percentage (%)', validators=[
        DataRequired(),
        NumberRange(min=0, max=100, message="Percentage must be between 0 and 100")
    ])
    submit = SubmitField('Save Allocation')

class SecurityForm(FlaskForm):
    """Form for adding or editing securities"""
    symbol = StringField('Symbol', validators=[DataRequired(), Length(min=1, max=20)])
    name = StringField('Name', validators=[DataRequired(), Length(min=1, max=100)])
    type = SelectField('Type', choices=[
        ('Stock', 'Stock'),
        ('ETF', 'ETF'),
        ('Bond', 'Bond'),
        ('Mutual Fund', 'Mutual Fund'),
        ('Cash', 'Cash'),
        ('Other', 'Other')
    ], default='Stock')
    submit = SubmitField('Save Security')

class SecurityPriceForm(FlaskForm):
    """Form for adding security prices"""
    security_id = SelectField('Security', coerce=int, validators=[DataRequired()])
    date = DateField('Date', default=datetime.utcnow, validators=[DataRequired()])
    price = FloatField('Price', validators=[
        DataRequired(),
        NumberRange(min=0, message="Price must be greater than 0")
    ])
    submit = SubmitField('Add Price')

class ReportGenerationForm(FlaskForm):
    """Form for generating custom reports"""
    #TODO: Add other reporting types/forms
    report_type = SelectField('Report Type', choices=[
        ('summary', 'Summary Report'),
        # ('detailed', 'Detailed Report'),
        # ('performance', 'Performance Report')
    ], default='summary')
    start_date = DateField('Start Date', validators=[DataRequired()])
    end_date = DateField('End Date', default=datetime.utcnow, validators=[DataRequired()])
    format = SelectField('Format', choices=[
        # ('html', 'HTML'),
        ('pdf', 'PDF'),
        # ('csv', 'CSV')
    ], default='pdf')
    submit = SubmitField('Generate Report')

class CustomerForm(FlaskForm):
    """Form for adding or editing customers (for admin use)"""
    name = StringField('Name', validators=[DataRequired(), Length(min=2, max=100)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[
        Optional(),
        Length(min=8, message="Password must be at least 8 characters long if provided")
    ])
    is_admin = BooleanField('Admin User')
    submit = SubmitField('Save Customer')
