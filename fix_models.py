# fresh_start.py
import os
import sqlite3
from datetime import datetime

# Delete the database if it exists
db_path = 'portfolio_dashboard.db'
if os.path.exists(db_path):
    os.remove(db_path)
    print(f"Deleted existing database: {db_path}")

# Create a new database with SQLite directly
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Create tables
cursor.execute('''
CREATE TABLE customers (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT,
    is_admin BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
''')

cursor.execute('''
CREATE TABLE portfolios (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    risk_level TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
''')

cursor.execute('''
CREATE TABLE securities (
    id INTEGER PRIMARY KEY,
    symbol TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    type TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
''')

cursor.execute('''
CREATE TABLE portfolio_allocations (
    id INTEGER PRIMARY KEY,
    portfolio_id INTEGER NOT NULL,
    security_id INTEGER NOT NULL,
    percentage FLOAT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (portfolio_id) REFERENCES portfolios (id),
    FOREIGN KEY (security_id) REFERENCES securities (id),
    UNIQUE (portfolio_id, security_id)
)
''')

cursor.execute('''
CREATE TABLE investments (
    id INTEGER PRIMARY KEY,
    customer_id INTEGER NOT NULL,
    portfolio_id INTEGER NOT NULL,
    amount FLOAT NOT NULL,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id),
    FOREIGN KEY (portfolio_id) REFERENCES portfolios (id)
)
''')

cursor.execute('''
CREATE TABLE performances (
    id INTEGER PRIMARY KEY,
    investment_id INTEGER NOT NULL,
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    value FLOAT NOT NULL,
    return_pct FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (investment_id) REFERENCES investments (id)
)
''')

cursor.execute('''
CREATE TABLE security_prices (
    id INTEGER PRIMARY KEY,
    security_id INTEGER NOT NULL,
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    price FLOAT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (security_id) REFERENCES securities (id),
    UNIQUE (security_id, date)
)
''')

# Add admin user
from werkzeug.security import generate_password_hash
admin_password_hash = generate_password_hash('admin')
cursor.execute('''
INSERT INTO customers (name, email, password_hash, is_admin)
VALUES (?, ?, ?, ?)
''', ('Admin', '<EMAIL>', admin_password_hash, 1))

# Add ETFs
etfs = [
    ("SPUS", "SP Funds S&P 500 Sharia Industry Exclusions ETF", "ETF"),
    ("SPWO", "SP Funds Dow Jones Global Sukuk ETF", "ETF"),
    ("GLD", "SPDR Gold Shares", "ETF"),
    ("SPSK", "SP Funds Dow Jones Global Sukuk ETF", "ETF"),
    ("SPRE", "SP Funds S&P Global REIT Sharia ETF", "ETF")
]

for etf in etfs:
    cursor.execute('''
    INSERT INTO securities (symbol, name, type)
    VALUES (?, ?, ?)
    ''', etf)

# Add portfolios
portfolios = [
    ("Conservative Portfolio", "Low-risk portfolio focused on capital preservation with modest growth potential.", "Low"),
    ("Moderate Conservative Portfolio", "Balanced portfolio with emphasis on stability and some growth.", "Medium"),
    ("Balanced Portfolio", "Equal emphasis on capital preservation and growth.", "Medium"),
    ("Moderate Aggressive Portfolio", "Growth-oriented portfolio with higher risk tolerance.", "Medium"),
    ("Aggressive Portfolio", "High-risk portfolio focused on maximum growth potential.", "High")
]

for portfolio in portfolios:
    cursor.execute('''
    INSERT INTO portfolios (name, description, risk_level)
    VALUES (?, ?, ?)
    ''', portfolio)

# Commit changes and close connection
conn.commit()

# Add allocations
# Get portfolio and security IDs
cursor.execute("SELECT id FROM portfolios ORDER BY id")
portfolio_ids = [row[0] for row in cursor.fetchall()]

cursor.execute("SELECT id, symbol FROM securities ORDER BY id")
securities = {row[1]: row[0] for row in cursor.fetchall()}

# Allocation percentages for each portfolio
allocations = [
    # Conservative Portfolio
    [
        {"symbol": "SPUS", "percentage": 20},
        {"symbol": "SPWO", "percentage": 40},
        {"symbol": "GLD", "percentage": 15},
        {"symbol": "SPSK", "percentage": 20},
        {"symbol": "SPRE", "percentage": 5}
    ],
    # Moderate Conservative Portfolio
    [
        {"symbol": "SPUS", "percentage": 30},
        {"symbol": "SPWO", "percentage": 30},
        {"symbol": "GLD", "percentage": 15},
        {"symbol": "SPSK", "percentage": 15},
        {"symbol": "SPRE", "percentage": 10}
    ],
    # Balanced Portfolio
    [
        {"symbol": "SPUS", "percentage": 40},
        {"symbol": "SPWO", "percentage": 20},
        {"symbol": "GLD", "percentage": 15},
        {"symbol": "SPSK", "percentage": 10},
        {"symbol": "SPRE", "percentage": 15}
    ],
    # Moderate Aggressive Portfolio
    [
        {"symbol": "SPUS", "percentage": 50},
        {"symbol": "SPWO", "percentage": 15},
        {"symbol": "GLD", "percentage": 10},
        {"symbol": "SPSK", "percentage": 5},
        {"symbol": "SPRE", "percentage": 20}
    ],
    # Aggressive Portfolio
    [
        {"symbol": "SPUS", "percentage": 60},
        {"symbol": "SPWO", "percentage": 10},
        {"symbol": "GLD", "percentage": 5},
        {"symbol": "SPSK", "percentage": 5},
        {"symbol": "SPRE", "percentage": 20}
    ]
]

for i, portfolio_id in enumerate(portfolio_ids):
    for alloc in allocations[i]:
        security_id = securities[alloc["symbol"]]
        cursor.execute('''
        INSERT INTO portfolio_allocations (portfolio_id, security_id, percentage)
        VALUES (?, ?, ?)
        ''', (portfolio_id, security_id, alloc["percentage"]))

# Add initial prices for ETFs
prices = {
    "SPUS": 52.75,
    "SPWO": 21.45,
    "GLD": 215.30,
    "SPSK": 19.85,
    "SPRE": 23.60
}

current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
for symbol, price in prices.items():
    security_id = securities[symbol]
    cursor.execute('''
    INSERT INTO security_prices (security_id, date, price)
    VALUES (?, ?, ?)
    ''', (security_id, current_date, price))

# Commit all changes
conn.commit()
conn.close()

print("Database created successfully with all required data!")