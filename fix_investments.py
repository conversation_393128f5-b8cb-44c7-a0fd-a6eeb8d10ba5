from flask import Flask
from models import db, Customer, Investment, PortfolioAllocation, Portfolio
from datetime import datetime
import os
from config import config

# Create an app context for database operations
app = Flask(__name__)
app.config.from_object(config['default'])
db.init_app(app)

with app.app_context():
    print("Checking for portfolios with snapshots...")
    from models.performance import PortfolioSnapshot

    # Find portfolios that have valid snapshots
    portfolios_with_snapshots = db.session.query(Portfolio.id, Portfolio.name).join(
        PortfolioSnapshot, Portfolio.id == PortfolioSnapshot.portfolio_id
    ).distinct().all()

    if not portfolios_with_snapshots:
        print("No portfolios with snapshots found!")
        exit(1)

    print(f"Found {len(portfolios_with_snapshots)} portfolios with snapshots")
    for portfolio_id, portfolio_name in portfolios_with_snapshots:
        print(f"Portfolio ID: {portfolio_id}, Name: {portfolio_name}")

    # Choose the portfolio to link investments to
    target_portfolio_id = portfolios_with_snapshots[0][0]  # Default to first one found

    # Get all customers
    customers = Customer.query.all()
    print(f"Found {len(customers)} customers")

    # Create investments for each customer
    for customer in customers:
        # Check if customer already has investment in this portfolio
        existing = Investment.query.filter_by(
            customer_id=customer.id,
            portfolio_id=target_portfolio_id
        ).first()

        if existing:
            print(f"Customer {customer.name} already has investment in portfolio {target_portfolio_id}")
            existing.is_active = True
            existing.is_funded = True
            db.session.add(existing)
        else:
            # Create new investment
            investment = Investment(
                customer_id=customer.id,
                portfolio_id=target_portfolio_id,
                amount=100000,  # Initial investment amount
                start_date=datetime.now(),
                is_active=True,
                is_funded=True
            )
            db.session.add(investment)
            print(f"Created new investment for {customer.name} in portfolio {target_portfolio_id}")

    db.session.commit()
    print("Done! All customers now have active investments.")