# Portfolio Dashboard

A Flask-based web application for tracking and reporting investment portfolios across multiple customers.

## Getting Started

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Set up environment variables:
   Create a `.env` file with:
   ```
   FLASK_APP=app.py
   FLASK_CONFIG=development
   SECRET_KEY=your-secure-secret-key
   DATABASE_URI=sqlite:///portfolio_dashboard.db
   ```

3. Run the application:
   ```
   flask run
   ```

The application will be available at http://localhost:5000
