import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Base configuration."""
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-key-please-change-in-production')
    DEBUG = False
    TESTING = False

    # Database settings
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URI', 'sqlite:///portfolio_dashboard.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # IBKR report settings
    IBKR_REPORTS_DIR = os.environ.get('IBKR_REPORTS_DIR',
                                      os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'reports'))

    # Email settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = bool(os.environ.get('MAIL_USE_TLS', True))
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER')

    SUBSCRIPTION_CUTOFF_HOUR = 20
    # Application settings
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin')
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads'))

    MAINTENANCE_FEE_DAILY = 10.0  # Fixed dollar fee per day (if you need it)
    ENTRY_FEE_RATE = 0.0075  # 0.75% entry (subscription) fee
    MANAGEMENT_FEE_YEARLY = 0.02  # 2% per year management fee (applied daily as mgmt_fee_yearly/365)
    PERFORMANCE_FEE_RATE = 0.20  # 20% performance fee on gains above high-water mark
    BROKERAGE_FEE_DAILY = 5.0  # Fixed dollar brokerage fee per day
    EXIT_FEE_RATE = 0.02  # 2% exit fee upon closure/When Admin Close Customer Account


class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True


class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False


class ProductionConfig(Config):
    """Production configuration."""
    # Override with production settings

    # Use stronger secret key
    SECRET_KEY = os.environ.get('SECRET_KEY', 'secret-key-to-change-in-production')

    # Use production database
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URI')


# Dictionary of available configurations
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}