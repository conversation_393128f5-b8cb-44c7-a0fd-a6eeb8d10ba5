import io
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer

def generate_simple_pdf(customer_name, total_invested, total_current_value, total_return):
    """Generate a very simple PDF with minimal formatting"""
    buffer = io.BytesIO()
    
    # Create a simple document
    doc = SimpleDocTemplate(
        buffer,
        pagesize=letter,
        title=f"Investment Report - {customer_name}"
    )
    
    # Get basic styles
    styles = getSampleStyleSheet()
    
    # Create content
    story = []
    
    # Title
    title = Paragraph(f"Investment Report for {customer_name}", styles['Title'])
    story.append(title)
    story.append(Spacer(1, 12))
    
    # Summary
    summary = Paragraph(f"Total Invested: ${total_invested:,.2f}", styles['Normal'])
    story.append(summary)
    story.append(Spacer(1, 6))
    
    summary = Paragraph(f"Current Value: ${total_current_value:,.2f}", styles['Normal'])
    story.append(summary)
    story.append(Spacer(1, 6))
    
    summary = Paragraph(f"Total Return: {total_return:,.2f}%", styles['Normal'])
    story.append(summary)
    
    # Build the document
    doc.build(story)
    
    # Get the PDF bytes
    pdf_bytes = buffer.getvalue()
    buffer.close()
    
    return pdf_bytes