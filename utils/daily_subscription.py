# daily_subscription.py
from datetime import datetime, timedelta
from models import db
from models.investment import Investment
from models.portfolio import Portfolio
from models.performance import PortfolioSnapshot

def process_pending_investments(subscription_date):
    """
    Batch-process all pending investments for a given subscription_date.
    All investments for a portfolio with start_date equal to subscription_date
    are subscribed together at a locked subscription NAV.
    """
    # Normalize subscription_date to midnight.
    start_day = datetime(subscription_date.year, subscription_date.month, subscription_date.day)
    end_day = start_day + timedelta(days=1)

    # Retrieve all pending investments for that day.
    pending_investments = Investment.query.filter(
        Investment.start_date == start_day,
        Investment.is_active == True,
        Investment.is_funded == False
    ).all()

    if not pending_investments:
        return  # Nothing to process.

    # Group pending investments by portfolio_id.
    portfolios_pending = {}
    for inv in pending_investments:
        portfolios_pending.setdefault(inv.portfolio_id, []).append(inv)

    for portfolio_id, invests in portfolios_pending.items():
        portfolio = Portfolio.query.get(portfolio_id)

        # Retrieve the final snapshot for the subscription day.
        snapshot = PortfolioSnapshot.query.filter(
            PortfolioSnapshot.portfolio_id == portfolio_id,
            PortfolioSnapshot.date >= start_day,
            PortfolioSnapshot.date < end_day
        ).order_by(PortfolioSnapshot.date.desc()).first()
        if not snapshot:
            snapshot = PortfolioSnapshot.query.filter(
                PortfolioSnapshot.portfolio_id == portfolio_id,
                PortfolioSnapshot.date <= subscription_date
            ).order_by(PortfolioSnapshot.date.desc()).first()
        if not snapshot:
            # If no snapshot exists, default NAV.
            subscription_nav = 1000
        else:
            # Determine subscription NAV based on previously funded (subscribed) investments.
            previous_units = db.session.query(db.func.sum(Investment.units)).filter(
                Investment.portfolio_id == portfolio_id,
                Investment.start_date < start_day,
                Investment.is_active == True,
                Investment.is_funded == True
            ).scalar()
            if not previous_units or previous_units <= 0:
                subscription_nav = 1000
            else:
                subscription_nav = snapshot.total_value / previous_units

        # Process each pending investment: assign units and mark as funded.
        for inv in invests:
            inv.units = inv.amount / subscription_nav if subscription_nav > 0 else 0
            inv.entry_nav = subscription_nav
            inv.high_water_mark = inv.amount  # Set baseline equal to the invested amount.
            inv.is_funded = True
            db.session.add(inv)
    db.session.commit()
