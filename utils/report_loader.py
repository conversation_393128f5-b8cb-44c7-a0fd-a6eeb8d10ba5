import os
from glob import glob
from flask import current_app
from utils.ibkr_importer import process_ibkr_report


def process_all_reports():
    """
    Scan the configured IBKR reports directory (recursively) for all JSON files.
    For each JSON report file found, process the report to update positions, prices,
    and create (or update) the portfolio snapshot.

    If a report (identified by its report date and portfolio) is already processed,
    you can choose to skip it (this example always calls process_ibkr_report, which in
    turn may update an existing snapshot if values have changed).

    Returns:
        list: A list of tuples containing (file_path, report_date, status)
    """
    processed = []
    reports_dir = current_app.config.get('IBKR_REPORTS_DIR')
    # Recursively find all JSON files in the reports directory
    json_files = glob(os.path.join(reports_dir, '**', '*.json'), recursive=True)

    for file_path in json_files:
        # Process each file as a JSON report
        result = process_ibkr_report(file_path, file_type='json')
        report_date = result.get('report_date')
        status = result.get('status')
        processed.append((file_path, report_date, status))
    return processed


# For testing purpose:
if __name__ == '__main__':
    # When running standalone, you need to create an application context.
    from app import create_app

    app = create_app('development')
    with app.app_context():
        results = process_all_reports()
        for fp, rpt_date, status in results:
            print(f"Processed file: {fp}, Report Date: {rpt_date}, Status: {status}")
