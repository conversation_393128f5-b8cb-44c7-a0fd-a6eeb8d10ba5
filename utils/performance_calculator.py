import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from flask import current_app
from sqlalchemy import func

from models import db
from models.investment import Investment
from models.performance import Performance
from models.security_price import SecurityPrice


from datetime import datetime
from flask import current_app
from models import db
from models.investment import Investment
from models.performance import Performance, PortfolioSnapshot


# -----------------------------
# 3. In utils/performance_calculator.py – modify calculate_customer_performance
# In utils/performance_calculator.py
# Make sure we store daily investment value in Performance so get_current_value() returns correct fraction-based numbers.

def calculate_customer_performance(investment_id):
    from models import db, Investment
    from models.performance import Performance, PortfolioSnapshot
    from flask import current_app
    from datetime import datetime

    inv = Investment.query.get(investment_id)
    if not inv:
        current_app.logger.error(f"Investment {investment_id} not found")
        return {"status": "error", "message": "Investment not found"}

    # Ensure entry_nav and units are set at the time of investment purchase.
    if not inv.units or inv.units == 0:
        entry_nav = inv.portfolio.get_nav_per_unit(inv.start_date)
        new_units = inv.amount / entry_nav if entry_nav > 0 else 0
        current_app.logger.debug(
            f"[INV {inv.id}] Setting units: amount={inv.amount}, entry_nav={entry_nav}, computed_units={new_units}"
        )
        inv.units = inv.amount / entry_nav
        inv.entry_nav = entry_nav
        # If high_water_mark is None, set it to the invested amount as a baseline.
        if not inv.high_water_mark or inv.high_water_mark <= 0:
            inv.high_water_mark = inv.amount
        db.session.flush()
        db.session.commit()

    # Ensure high_water_mark is properly set (if still None, set default)
    if not inv.high_water_mark or inv.high_water_mark <= 0:
        inv.high_water_mark = inv.amount

    # Retrieve fee settings from config.
    mgmt_fee_yearly = current_app.config.get('MANAGEMENT_FEE_YEARLY', 0.02)
    daily_mgmt_fee_rate = mgmt_fee_yearly / 365.0
    perf_fee_rate = current_app.config.get('PERFORMANCE_FEE_RATE', 0.20)
    brokerage_fee_daily = current_app.config.get('BROKERAGE_FEE_DAILY', 5.0)

    snapshots = PortfolioSnapshot.query.filter(
        PortfolioSnapshot.portfolio_id == inv.portfolio_id,
        PortfolioSnapshot.date >= inv.start_date
    ).order_by(PortfolioSnapshot.date.asc()).all()

    if not snapshots:
        current_app.logger.warning(f"[INV {inv.id}] No snapshots found for portfolio {inv.portfolio_id} after {inv.start_date}")
        return {"status": "error", "message": "No snapshots available"}

    baseline_snap = snapshots[0]
    baseline_total = baseline_snap.total_value
    current_app.logger.debug(
        f"[INV {inv.id}] Baseline snapshot on {baseline_snap.date} with total_value = {baseline_total}"
    )

    for snap in snapshots:
        # Calculate total units held in the portfolio as of snap.date.
        active_investments = Investment.query.filter(
            Investment.portfolio_id == inv.portfolio_id,
            Investment.is_active == True,
            Investment.is_funded == True,
            Investment.start_date <= snap.date
        ).all()
        total_units = sum(i.units for i in active_investments if i.units) or 0

        if total_units > 0:
            nav_per_unit = snap.total_value / total_units
        else:
            # Fallback – ideally, this case should not happen.
            nav_per_unit = inv.entry_nav

        # Compute the gross current value for this investment using the daily NAV per unit:
        gross_value = inv.units * nav_per_unit

        # Calculate fees (management fee, brokerage fee, and performance fee if applicable):
        mgmt_fee_amount = gross_value * daily_mgmt_fee_rate
        b_fee = brokerage_fee_daily

        if not inv.high_water_mark or inv.high_water_mark <= 0:
            inv.high_water_mark = inv.amount

        perf_fee_amount = 0
        if gross_value > inv.high_water_mark:
            gain_above_hwm = gross_value - inv.high_water_mark
            perf_fee_amount = gain_above_hwm * perf_fee_rate

        total_fee = mgmt_fee_amount + b_fee + perf_fee_amount
        net_value = gross_value - total_fee
        net_return_pct = ((net_value - inv.amount) / inv.amount * 100) if inv.amount > 0 else 0

        current_app.logger.debug(
            f"[INV {inv.id}] Snapshot {snap.date}: total_units={total_units}, nav_per_unit={nav_per_unit}, "
            f"gross_value={gross_value}, mgmt_fee={mgmt_fee_amount}, brokerage_fee={b_fee}, "
            f"perf_fee={perf_fee_amount}, total_fee={total_fee}, net_value={net_value}, "
            f"net_return_pct={net_return_pct}"
        )

        perf_record = Performance.query.filter_by(investment_id=inv.id, date=snap.date).first()
        if perf_record:
            perf_record.value = net_value
            perf_record.return_pct = net_return_pct
            current_app.logger.debug(f"[INV {inv.id}] Updated Performance record for {snap.date}")
        else:
            new_perf = Performance(
                investment_id=inv.id,
                date=snap.date,
                value=net_value,
                return_pct=net_return_pct
            )
            db.session.add(new_perf)
            current_app.logger.debug(f"[INV {inv.id}] Created Performance record for {snap.date}")

        db.session.commit()
    return {"status": "success", "message": "Performance calculated successfully"}


def close_investment_with_exit_fee(investment_id):
    from models import db, Investment
    from models.performance import Performance
    from flask import current_app
    from datetime import datetime

    inv = Investment.query.get(investment_id)
    if not inv:
        current_app.logger.error(f"Investment {investment_id} not found")
        return {"status": "error", "message": "Investment not found"}

    if not inv.is_active:
        return {"status": "error", "message": "Investment already closed"}

    # Fetch latest performance record for exit fee calculation
    perf = Performance.query.filter_by(investment_id=investment_id).order_by(Performance.date.desc()).first()
    if not perf:
        current_app.logger.error(f"No performance record found for investment {investment_id}")
        return {"status": "error", "message": "No performance data available"}

    exit_fee_rate = current_app.config.get('EXIT_FEE_RATE', 0.02)
    exit_fee_amount = perf.value * exit_fee_rate
    net_value = perf.value - exit_fee_amount

    current_app.logger.debug(
        f"[INV {investment_id}] Applying exit fee: original value={perf.value}, "
        f"exit_fee_amount={exit_fee_amount}, net_value={net_value}"
    )

    # Update the performance record with net value and recalc return
    perf.value = net_value
    perf.return_pct = ((net_value - inv.amount) / inv.amount * 100) if inv.amount else 0

    # Mark the investment as closed and record exit fee applied
    inv.is_active = False
    inv.exit_fee_applied = True
    inv.end_date = datetime.utcnow()

    db.session.commit()
    current_app.logger.info(f"[INV {investment_id}] Investment closed with exit fee applied")
    return {"status": "success", "message": "Investment closed with exit fee"}


def calculate_all_active_investments(as_of_date=None):
    """
    Calculate performance for all active investments

    Args:
        as_of_date (datetime): Date to calculate performance as of

    Returns:
        dict: Summary of calculated performances
    """
    if as_of_date is None:
        as_of_date = datetime.utcnow()

    try:
        # Get all active investments
        active_investments = Investment.query.filter_by(is_active=True).all()

        results = []
        for investment in active_investments:
            result = calculate_customer_performance(investment.id, as_of_date)
            results.append(result)

        return {
            'status': 'success',
            'date': as_of_date,
            'investments_calculated': len(results),
            'details': results
        }

    except Exception as e:
        return {
            'status': 'error',
            'message': str(e)
        }


def calculate_historical_performance(investment_id, start_date=None, end_date=None, interval='month'):
    """
    Calculate historical performance for an investment

    Args:
        investment_id (int): ID of the investment
        start_date (datetime): Starting date for calculation
        end_date (datetime): Ending date for calculation
        interval (str): Interval for calculations ('day', 'week', 'month')

    Returns:
        list: Historical performance data
    """
    investment = Investment.query.get(investment_id)
    if not investment:
        return {
            'status': 'error',
            'message': f'Investment with ID {investment_id} not found'
        }

    if start_date is None:
        start_date = investment.start_date

    if end_date is None:
        end_date = datetime.utcnow()

    # Generate date range based on interval
    if interval == 'day':
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    elif interval == 'week':
        date_range = pd.date_range(start=start_date, end=end_date, freq='W')
    else:  # default to month
        date_range = pd.date_range(start=start_date, end=end_date, freq='M')

    history = []
    for date in date_range:
        date = date.to_pydatetime()
        result = calculate_customer_performance(investment_id, date)
        if result['status'] == 'success':
            history.append({
                'date': date,
                'value': result['current_value'],
                'return_percentage': result['return_percentage']
            })

    return {
        'status': 'success',
        'investment_id': investment_id,
        'start_date': start_date,
        'end_date': end_date,
        'interval': interval,
        'history': history
    }