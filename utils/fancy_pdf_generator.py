import io
from datetime import datetime

from lxml.html.builder import HR
from openpyxl.drawing import Drawing
from reportlab.lib.pagesizes import A4, inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import (SimpleDocTemplate, Paragraph, Spacer, Image, Table,
                                TableStyle, PageBreak)

from utils.enhanced_pdf_generator import Watermark<PERSON>rand


def generate_fancy_pdf_report(
    report,
    start_date,
    end_date,
    performance_chart_bytes=None,
    allocation_chart_bytes=None
):
    """
    Generate a fancy PDF report using ReportLab.

    Args:
        report (dict): The main dictionary containing:
            - 'total_invested'
            - 'total_current_value'
            - 'total_return_percentage'
            - 'active_investments_count'
            - 'investments' (list)
            - 'portfolios' (list)
            - 'allocation_chart_bytes' (optional, if you generated a chart)
            - 'performance_chart_bytes' (optional, if you generated a chart)
            etc.
        start_date (datetime.date or similar)
        end_date   (datetime.date or similar)

        performance_chart_bytes (bytes): optional PNG data for a performance chart
        allocation_chart_bytes  (bytes): optional PNG data for an allocation chart

    Returns:
        bytes: The PDF file content as raw bytes (in-memory).
    """
    # 1) Prepare the PDF buffer
    buffer = io.BytesIO()

    # 2) Setup the document
    doc = SimpleDocTemplate(
        buffer,
        pagesize=A4,
        leftMargin=0.75 * inch,
        rightMargin=0.75 * inch,
        topMargin=1 * inch,
        bottomMargin=1 * inch,
    )

    # 3) Obtain some base styles
    styles = getSampleStyleSheet()

    # Create custom paragraph styles if needed
    style_title = ParagraphStyle(
        name="Title",
        parent=styles['Title'],
        fontName='Helvetica-Bold',
        fontSize=18,
        textColor=colors.HexColor("#0B3954"),
        alignment=TA_CENTER,
        spaceAfter=12,
    )

    style_subtitle = ParagraphStyle(
        name="Subtitle",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=12,
        textColor=colors.HexColor("#222222"),
        alignment=TA_CENTER,
        spaceAfter=12
    )

    style_heading = ParagraphStyle(
        name="Heading",
        parent=styles['Heading2'],
        textColor=colors.HexColor("#0B3954"),
        fontName='Helvetica-Bold',
        fontSize=14,
        spaceAfter=6
    )

    style_normal = styles['Normal']
    style_normal.fontName = 'Helvetica'
    style_normal.fontSize = 10
    style_normal.leading = 14

    style_small = ParagraphStyle(
        name="Small",
        parent=styles['Normal'],
        fontSize=9,
        leading=11,
        textColor=colors.grey
    )

    # 4) Build up story content
    story = []

    # Title & Subtitle
    title_text = f"FANCY INVESTMENT REPORT"
    story.append(Paragraph(title_text, style_title))

    sub_text = f"Report Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}<br/>Generated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M UTC')}"
    story.append(Paragraph(sub_text, style_subtitle))

    story.append(HR(color=colors.HexColor("#CCCCCC"), thickness=1))
    story.append(Spacer(1, 0.2*inch))

    # 5) Summaries
    total_inv = report.get('total_invested', 0)
    total_val = report.get('total_current_value', 0)
    total_ret = report.get('total_return_percentage', 0)
    active_inv_count = report.get('active_investments_count', 0)

    summary_html = f"""
    <b>Total Invested:</b> ${total_inv:,.2f}<br/>
    <b>Current Value:</b> ${total_val:,.2f}<br/>
    <b>Total Return:</b> {total_ret:.2f}%<br/>
    <b>Active Investments:</b> {active_inv_count}
    """
    story.append(Paragraph(summary_html, style_normal))
    story.append(Spacer(1, 0.3*inch))

    # If we have performance chart bytes, embed them
    if performance_chart_bytes:
        story.append(Paragraph("Performance Chart", style_heading))
        img_perf = Image(io.BytesIO(performance_chart_bytes))
        img_perf._restrictSize(5*inch, 3*inch)
        story.append(img_perf)
        story.append(Spacer(1, 0.3*inch))

    # If we have allocation chart bytes, embed them
    if allocation_chart_bytes:
        story.append(Paragraph("Allocation Chart", style_heading))
        img_alloc = Image(io.BytesIO(allocation_chart_bytes))
        img_alloc._restrictSize(4*inch, 3*inch)
        story.append(img_alloc)
        story.append(Spacer(1, 0.3*inch))

    # 6) Possibly add a Portfolio Summary Table
    portfolios = report.get('portfolios', [])
    if portfolios:
        story.append(Paragraph("Portfolio Summary", style_heading))
        table_data = [["Portfolio", "Risk", "Invested", "Value", "Return"]]
        for p in portfolios:
            name = p.get('name','')
            risk = p.get('risk_level','')
            invested = p.get('amount_invested',0)
            value = p.get('current_value',0)
            ret = p.get('return',0)
            row = [
                name,
                risk,
                f"${invested:,.2f}",
                f"${value:,.2f}",
                f"{ret:,.2f}%"
            ]
            table_data.append(row)

        # Build table
        pf_table = Table(table_data, colWidths=[2.2*inch, 0.9*inch, 1.0*inch, 1.0*inch, 0.8*inch])
        pf_table.hAlign = 'LEFT'
        pf_table.setStyle(TableStyle([
            ('BACKGROUND', (0,0), (-1,0), colors.HexColor("#0B3954")),
            ('TEXTCOLOR', (0,0), (-1,0), colors.whitesmoke),
            ('ALIGN', (2,1), (-1,-1), 'RIGHT'),
            ('GRID', (0,0), (-1,-1), 0.5, colors.grey),
            ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
            ('FONTNAME', (0,1), (-1,-1), 'Helvetica'),
            ('FONTSIZE', (0,0), (-1,0), 10),
            ('FONTSIZE', (0,1), (-1,-1), 9),
            ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
        ]))
        story.append(pf_table)
        story.append(Spacer(1, 0.3*inch))

    # 7) Investment Summary
    investments = report.get('investments', [])
    if investments:
        story.append(Paragraph("Investment Summary", style_heading))
        table_data = [["Portfolio", "Start Date", "Amount", "Current Value", "Return", "Status"]]
        for inv in investments:
            # Might be a dictionary or actual object
            if isinstance(inv, dict):
                pname = inv.get('portfolio_name','')
                start_d = inv.get('start_date','-')
                amt = inv.get('amount',0)
                curr_val = inv.get('current_value',0)
                ret = inv.get('return',0)
                # Status
                if not inv.get('is_active', True):
                    st = "Closed"
                elif not inv.get('is_funded', False):
                    st = "Pending"
                else:
                    st = "Active"
            else:
                # It's likely an ORM object
                pname = inv.portfolio.name
                start_d = inv.start_date.strftime('%Y-%m-%d')
                amt = inv.amount
                curr_val = inv.get_current_value()
                ret = inv.get_return()
                if not inv.is_active:
                    st = "Closed"
                elif not inv.is_funded:
                    st = "Pending"
                else:
                    st = "Active"

            row = [
                pname,
                str(start_d),
                f"${amt:,.2f}",
                f"${curr_val:,.2f}",
                f"{ret:,.2f}%",
                st
            ]
            table_data.append(row)

        inv_table = Table(table_data, colWidths=[2.0*inch, 1.0*inch, 0.9*inch, 0.9*inch, 0.8*inch, 1.0*inch])
        inv_table.setStyle(TableStyle([
            ('BACKGROUND', (0,0), (-1,0), colors.HexColor("#0B3954")),
            ('TEXTCOLOR', (0,0), (-1,0), colors.whitesmoke),
            ('GRID', (0,0), (-1,-1), 0.5, colors.grey),
            ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
            ('FONTNAME', (0,1), (-1,-1), 'Helvetica'),
            ('FONTSIZE', (0,0), (-1,0), 10),
            ('FONTSIZE', (0,1), (-1,-1), 9),
            ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
            ('ALIGN', (2,1), (4,-1), 'RIGHT'),
        ]))
        story.append(inv_table)
        story.append(Spacer(1, 0.3*inch))

    # Possibly add a small note or disclaimers
    disclaim = "Disclaimer: Past performance is not a guarantee of future results. " \
               "Investments may lose value."
    story.append(Paragraph(disclaim, style_small))

    # 8) Build the PDF
    try:
        # For plain build with no headers/footers
        doc.build(story)
    except Exception as e:
        # If that fails, try a simpler story without complex elements
        reduced_story = [s for s in story if not isinstance(s, (Image, Drawing, WatermarkBrand))]
        doc.build(reduced_story)

    # Return the PDF bytes
    pdf_bytes = buffer.getvalue()
    buffer.close()

    return pdf_bytes

    pdf_value = buffer.getvalue()
    buffer.close()
    return pdf_value
