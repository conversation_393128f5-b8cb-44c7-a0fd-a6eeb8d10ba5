import os
import json
import re
from glob import glob

import pandas as pd
from datetime import datetime
from pathlib import Path
from flask import current_app
from werkzeug.utils import secure_filename

from models import db
from models.performance import PortfolioSnapshot
from models.portfolio import Security, Portfolio, PortfolioAllocation
from models.security_price import SecurityPrice


# Existing functions...
def process_ibkr_report(file_path, file_type='json'):
    if file_type.lower() == 'json':
        return process_ibkr_json(file_path)
    elif file_type.lower() == 'csv':
        json_output_path = os.path.join(os.path.dirname(file_path), f"{Path(file_path).stem}_converted.json")
        converted_data = convert_broker_report_to_json(file_path, json_output_path)
        if not converted_data.get('error', False):
            return process_ibkr_json(json_output_path)
        else:
            return process_ibkr_json(json_output_path)
    else:
        raise ValueError(f"Unsupported file type: {file_type}")


def process_ibkr_json(file_path):
    """Process IBKR report in JSON format with improved data extraction."""
    try:
        # Add proper logging
        current_app.logger.info(f"Processing IBKR JSON file: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)

        report_date = datetime.strptime(report_data['metadata']['report_date'], '%Y-%m-%d')
        # Process securities and prices
        securities_updated = 0
        prices_updated = 0

        # Extract portfolio name from report
        portfolio_name = None
        if 'account_info' in report_data and 'name' in report_data['account_info']:
            portfolio_name = report_data['account_info']['name']

        # If account info isn't directly available, try to extract from sections
        if portfolio_name is None and 'sections' in report_data and 'Account Information' in report_data['sections']:
            account_section = report_data['sections']['Account Information']
            if 'tables' in account_section and len(account_section['tables']) > 0:
                for row in account_section['tables'][0].get('rows', []):
                    if row.get('Field Name', '').strip() == 'Name':
                        portfolio_name = row.get('Field Value', '')
                        break

        # If still not found, use a default or extract from filename
        if portfolio_name is None:
            # Try to extract from filename or folder structure
            folder_name = os.path.basename(os.path.dirname(file_path))
            if folder_name and folder_name != 'json':
                portfolio_name = folder_name.replace('_', ' ').title()
                current_app.logger.info(f"Using folder name as portfolio name: {portfolio_name}")
            else:
                portfolio_name = "IBKR Portfolio"
                current_app.logger.warning(f"Could not determine portfolio name, using default: {portfolio_name}")

        current_app.logger.info(f"Portfolio name: {portfolio_name}")

        # Find or create the portfolio
        portfolio = Portfolio.query.filter_by(name=portfolio_name).first()
        if not portfolio:
            portfolio = Portfolio(
                name=portfolio_name,
                description=f"Portfolio imported from IBKR report on {report_date.strftime('%Y-%m-%d')}",
                risk_level="Medium"  # Default risk level
            )
            db.session.add(portfolio)
            db.session.flush()  # Get the portfolio.id
            current_app.logger.info(f"Created new portfolio: {portfolio_name}")

        # Extract total value (NAV) from the report
        total_value = None

        # First try the key_statistics section for ending_nav
        if 'key_statistics' in report_data and 'ending_nav' in report_data['key_statistics']:
            total_value = float(report_data['key_statistics']['ending_nav'])
            current_app.logger.info(f"Found NAV in key_statistics: {total_value}")

        # If not found, try the positions summary
        if total_value is None and 'positions' in report_data and 'summary' in report_data['positions']:
            summary = report_data['positions']['summary']
            if 'portfolio_total' in summary and 'total_value' in summary['portfolio_total']:
                total_value = float(summary['portfolio_total']['total_value'])
                current_app.logger.info(f"Found NAV in positions summary: {total_value}")

        # If still not found, try to extract from Net Asset Value section
        if total_value is None and 'sections' in report_data and 'Net Asset Value' in report_data['sections']:
            nav_section = report_data['sections']['Net Asset Value']
            if 'tables' in nav_section and len(nav_section['tables']) > 0:
                for row in nav_section['tables'][0].get('rows', []):
                    if row.get('Asset Class', '').strip().lower() == 'total':
                        try:
                            total_value = float(row.get('Current Total', 0))
                            current_app.logger.info(f"Found NAV in Net Asset Value section: {total_value}")
                            break
                        except (ValueError, TypeError):
                            current_app.logger.warning(f"Error converting NAV value: {row.get('Current Total')}")

        # If we couldn't extract NAV, log a warning and return error
        if total_value is None:
            current_app.logger.error(f"Could not extract NAV from report data")
            return {
                'status': 'error',
                'message': 'Could not extract NAV from report data',
                'report_date': report_date
            }

        # Update the portfolio snapshot with the extracted data
        normalized_date = datetime.combine(report_date.date(), datetime.min.time())
        snapshot = PortfolioSnapshot.query.filter_by(
            portfolio_id=portfolio.id,
            date=normalized_date
        ).first()

        if snapshot:
            # Update existing snapshot
            if snapshot.total_value != total_value:
                snapshot.total_value = total_value
                current_app.logger.info(
                    f"Updated snapshot for {portfolio_name} on {normalized_date.strftime('%Y-%m-%d')} with value: {total_value}")
            else:
                current_app.logger.info(
                    f"Snapshot already up-to-date for {portfolio_name} on {normalized_date.strftime('%Y-%m-%d')}")
        else:
            # Create new snapshot
            snapshot = PortfolioSnapshot(
                portfolio_id=portfolio.id,
                date=normalized_date,
                total_value=total_value
            )
            db.session.add(snapshot)
            current_app.logger.info(
                f"Created new snapshot for {portfolio_name} on {normalized_date.strftime('%Y-%m-%d')} with value: {total_value}")

        # Store the report data in the snapshot for reference
        snapshot.report_data = report_data

        # Process ETF data to update securities and prices
        if 'positions' in report_data and 'etfs' in report_data['positions']:
            for etf in report_data['positions']['etfs']:
                symbol = etf.get('symbol')
                if not symbol:
                    continue

                security = Security.query.filter_by(symbol=symbol).first()
                if not security:
                    security = Security(
                        symbol=symbol,
                        name=etf.get('description', symbol),
                        type='ETF'
                    )
                    db.session.add(security)
                    securities_updated += 1

                price = float(etf.get('price', 0))
                if price > 0:
                    price_date = parse_date(etf.get('date'), report_date)
                    existing_price = SecurityPrice.query.filter_by(
                        security_id=security.id,
                        date=price_date
                    ).first()

                    if not existing_price:
                        price_record = SecurityPrice(
                            security_id=security.id,
                            date=price_date,
                            price=price
                        )
                        db.session.add(price_record)
                        prices_updated += 1

        # Extract and process stock positions similarly
        if 'positions' in report_data and 'stocks' in report_data['positions']:
            for stock in report_data['positions']['stocks']:
                symbol = stock.get('symbol')
                if not symbol:
                    continue

                security = Security.query.filter_by(symbol=symbol).first()
                if not security:
                    security = Security(
                        symbol=symbol,
                        name=stock.get('description', symbol),
                        type='Stock'
                    )
                    db.session.add(security)
                    securities_updated += 1

                price = float(stock.get('price', 0))
                if price > 0:
                    price_date = parse_date(stock.get('date'), report_date)
                    existing_price = SecurityPrice.query.filter_by(
                        security_id=security.id,
                        date=price_date
                    ).first()

                    if not existing_price:
                        price_record = SecurityPrice(
                            security_id=security.id,
                            date=price_date,
                            price=price
                        )
                        db.session.add(price_record)
                        prices_updated += 1

        # Extract and update portfolio allocations
        allocations = {}
        if 'positions' in report_data:
            total_market_value = 0

            # Calculate market values from ETFs
            for etf in report_data['positions'].get('etfs', []):
                symbol = etf.get('symbol')
                if symbol and 'market_value' in etf:
                    market_value = float(etf['market_value'])
                    allocations[symbol] = market_value
                    total_market_value += market_value

            # Add market values from stocks
            for stock in report_data['positions'].get('stocks', []):
                symbol = stock.get('symbol')
                if symbol and 'market_value' in stock:
                    market_value = float(stock['market_value'])
                    allocations[symbol] = market_value
                    total_market_value += market_value

            # Convert market values to percentages
            if total_market_value > 0:
                for symbol, value in allocations.items():
                    allocations[symbol] = (value / total_market_value) * 100

                # Update portfolio allocations
                for symbol, percentage in allocations.items():
                    security = Security.query.filter_by(symbol=symbol).first()
                    if security:
                        existing_allocation = PortfolioAllocation.query.filter_by(
                            portfolio_id=portfolio.id,
                            security_id=security.id
                        ).first()

                        if existing_allocation:
                            existing_allocation.percentage = percentage
                        else:
                            allocation = PortfolioAllocation(
                                portfolio_id=portfolio.id,
                                security_id=security.id,
                                percentage=percentage
                            )
                            db.session.add(allocation)

        # Commit all changes to the database
        db.session.commit()
        #current_app.logger.info(f"Successfully processed IBKR report: {file_path}")

        return {
            'status': 'success',
            'report_date': report_date,
            'portfolio': portfolio_name,
            'total_value': total_value,
            'securities_updated': securities_updated,
            'prices_updated': prices_updated,
            'snapshot': snapshot
        }

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error processing IBKR report: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())

        return {
            'status': 'error',
            'message': str(e),
            'report_date': report_date if 'report_date' in locals() else None
        }


def process_all_reports():
    """
    Scan the configured IBKR reports directory (recursively) for all JSON files.
    For each JSON report file found, process the report to update positions, prices,
    and create (or update) the portfolio snapshot.

    Returns:
        list: A list of tuples containing (file_path, report_date, status)
    """
    processed = []
    reports_dir = current_app.config.get('IBKR_REPORTS_DIR')

    current_app.logger.info(f"Scanning for reports in: {reports_dir}")

    # Check if directory exists
    if not os.path.exists(reports_dir):
        current_app.logger.error(f"Reports directory does not exist: {reports_dir}")
        return processed

    # Recursively find all JSON files in the reports directory
    json_files = glob(os.path.join(reports_dir, '**', '*.json'), recursive=True)

    current_app.logger.info(f"Found {len(json_files)} JSON files to process")

    for file_path in json_files:
        # Skip files that are clearly not IBKR reports (e.g., converted_*.json)
        if "_converted.json" in file_path:
            continue

        # Process each file as a JSON report
        try:
            current_app.logger.info(f"Processing file: {file_path}")
            result = process_ibkr_report(file_path, file_type='json')
            report_date = result.get('report_date')
            status = result.get('status')
            portfolio = result.get('portfolio', 'Unknown')

            if status == 'success':
                current_app.logger.info(f"Successfully processed {file_path} for portfolio {portfolio}")
            else:
                current_app.logger.warning(f"Issue processing {file_path}: {result.get('message', 'Unknown error')}")

            processed.append((file_path, report_date, status))
        except Exception as e:
            current_app.logger.error(f"Error processing {file_path}: {str(e)}")
            processed.append((file_path, None, f"error: {str(e)}"))

    current_app.logger.info(
        f"Processed {len(processed)} files: {sum(1 for _, _, s in processed if s == 'success')} successful")
    return processed


def create_portfolio_from_report(report_data, portfolio_name=None):
    """
    Create a new portfolio based on an IBKR report.
    This method supports reports with a top-level 'positions' key. If that key is missing,
    it will try to retrieve the required data from the 'sections' key, using the
    'Account Information' section to get the account name and the 'Net Asset Value'
    section to get the total portfolio value. It then creates equal allocations
    across all securities if market values are not available.
    """
    try:
        from datetime import datetime
        from models import db
        from models.portfolio import Portfolio, Security, PortfolioAllocation

        # Determine the portfolio name from the report data:
        if not portfolio_name:
            if 'account_info' in report_data:
                account_name = report_data.get('account_info', {}).get('name', 'IBKR Portfolio')
            elif 'sections' in report_data and 'Account Information' in report_data['sections']:
                acct_table = report_data['sections']['Account Information']['tables'][0]
                account_name = next(
                    (row.get("Field Value") for row in acct_table.get("rows", [])
                     if row.get("Field Name", "").strip() == "Name"),
                    "IBKR Portfolio"
                )
            else:
                account_name = "IBKR Portfolio"
            portfolio_name = f"{account_name} - {datetime.now().strftime('%Y-%m-%d')}"

        # Look up or create the portfolio:
        portfolio = Portfolio.query.filter_by(name=portfolio_name).first()
        if not portfolio:
            portfolio = Portfolio(
                name=portfolio_name,
                description=f"Portfolio created from IBKR report on {datetime.now().strftime('%Y-%m-%d')}",
                risk_level="Medium"
            )
            db.session.add(portfolio)
            db.session.flush()  # To obtain portfolio.id

        symbols = []
        allocation_data = {}
        total_value = 0

        if 'positions' in report_data:
            # Use the positions key if it exists.
            positions = report_data.get('positions', {})
            for etf in positions.get('etfs', []):
                symbol = etf.get('symbol')
                if symbol:
                    symbols.append(symbol)
            for stock in positions.get('stocks', []):
                symbol = stock.get('symbol')
                if symbol:
                    symbols.append(symbol)
            for cash in positions.get('cash', []):
                currency = cash.get('currency')
                if currency:
                    symbols.append(currency)
            total_value = positions.get('summary', {}).get('portfolio_total', {}).get('total_value', 0)
            if total_value > 0:
                # Calculate allocation percentages based on market values.
                for etf in positions.get('etfs', []):
                    symbol = etf.get('symbol')
                    market_value = etf.get('market_value', 0)
                    if symbol and market_value > 0:
                        allocation_data[symbol] = (market_value / total_value) * 100
                for stock in positions.get('stocks', []):
                    symbol = stock.get('symbol')
                    market_value = stock.get('market_value', 0)
                    if symbol and market_value > 0:
                        allocation_data[symbol] = (market_value / total_value) * 100
                for cash in positions.get('cash', []):
                    symbol = cash.get('currency')
                    market_value = cash.get('usd_equivalent', 0)
                    if symbol and market_value > 0:
                        allocation_data[symbol] = (market_value / total_value) * 100

        elif 'sections' in report_data:
            # Fallback for reports lacking a top-level 'positions' key.
            sections = report_data.get('sections', {})
            # Attempt to get total portfolio value from the "Net Asset Value" section.
            nav_section = sections.get('Net Asset Value', {})
            if nav_section.get('tables'):
                for table in nav_section['tables']:
                    for row in table.get('rows', []):
                        if row.get("Asset Class", "").strip() == "Total":
                            total_value = row.get("Current Total", 0)
                            break
            # Retrieve securities list from the "Financial Instrument Information" section.
            fi_section = sections.get('Financial Instrument Information', {})
            if fi_section.get('tables'):
                fi_table = fi_section['tables'][0]
                for row in fi_table.get('rows', []):
                    symbol = row.get("Symbol")
                    if symbol:
                        symbols.append(symbol)
            # If total_value is zero but we have securities, assign equal allocation.
            if total_value <= 0 and symbols:
                total_value = 1  # Prevent division by zero
            if symbols:
                equal_alloc = 100 / len(symbols)
                for sym in symbols:
                    allocation_data[sym] = equal_alloc

        # Look up each security in our database:
        from models.portfolio import Security  # Ensure proper import
        securities_dict = {}
        for symbol in symbols:
            if symbol:
                security = Security.query.filter_by(symbol=symbol).first()
                if security:
                    securities_dict[symbol] = security

        # Create or update portfolio allocations:
        from models.portfolio import PortfolioAllocation
        for symbol, security in securities_dict.items():
            percentage = allocation_data.get(symbol, 0)
            if percentage > 0:
                existing = PortfolioAllocation.query.filter_by(
                    portfolio_id=portfolio.id,
                    security_id=security.id
                ).first()
                if existing:
                    existing.percentage = percentage
                else:
                    allocation = PortfolioAllocation(
                        portfolio_id=portfolio.id,
                        security_id=security.id,
                        percentage=percentage
                    )
                    db.session.add(allocation)
        db.session.commit()

        return {
            'status': 'success',
            'portfolio_id': portfolio.id,
            'portfolio_name': portfolio.name
        }
    except Exception as e:
        db.session.rollback()
        return {
            'status': 'error',
            'message': str(e)
        }

def update_portfolio_snapshot_from_ibkr(json_file_path=None):
    from flask import current_app
    import os, json
    from datetime import datetime
    from models.portfolio import Portfolio
    from models.performance import PortfolioSnapshot
    from models import db

    if not json_file_path:
        reports_dir = current_app.config.get('IBKR_REPORTS_DIR')
        json_dir = os.path.join(reports_dir, 'json')
        json_files = [os.path.join(json_dir, f) for f in os.listdir(json_dir) if f.lower().endswith('.json')]
        if not json_files:
            current_app.logger.error("No JSON files found in the reports directory.")
            return None
        json_files.sort(reverse=True)
        json_file_path = json_files[0]

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        current_app.logger.info(f"Loaded report data from {json_file_path}")
    except Exception as e:
        current_app.logger.error(f"Error loading JSON file {json_file_path}: {str(e)}")
        return None

    report_date_str = report_data.get("metadata", {}).get("report_date")
    try:
        report_date = datetime.strptime(report_date_str, "%Y-%m-%d")
    except Exception:
        report_date = datetime.utcnow()
        current_app.logger.warning(f"Using current date for report since parsing failed for {json_file_path}")
    normalized_report_date = datetime.combine(report_date.date(), datetime.min.time())

    total_value = 0
    nav_section = report_data.get("sections", {}).get("Net Asset Value", {})
    if nav_section and nav_section.get("tables"):
        for row in nav_section["tables"][0].get("rows", []):
            if row.get("Asset Class", "").strip().lower() == "total":
                try:
                    total_value = float(row.get("Current Total", 0))
                except Exception:
                    total_value = 0
                break
    if total_value == 0:
        current_app.logger.error(f"No total portfolio value found in report {json_file_path}")
        return None

    portfolio_name = "IBKR Portfolio"
    account_info_section = report_data.get("sections", {}).get("Account Information", {})
    if account_info_section and account_info_section.get("tables"):
        for row in account_info_section["tables"][0].get("rows", []):
            if row.get("Field Name", "").strip().lower() == "name":
                portfolio_name = row.get("Field Value", "IBKR Portfolio")
                break

    portfolio = Portfolio.query.filter_by(name=portfolio_name).first()
    if not portfolio:
        portfolio = Portfolio(
            name=portfolio_name,
            description=f"Portfolio imported from IBKR report on {normalized_report_date.strftime('%Y-%m-%d')}",
            risk_level="Medium"
        )
        db.session.add(portfolio)
        db.session.flush()
        current_app.logger.info(f"Created new portfolio: {portfolio_name}")

    snapshot = PortfolioSnapshot.query.filter_by(
        portfolio_id=portfolio.id,
        date=normalized_report_date
    ).first()
    if snapshot:
        if snapshot.total_value != total_value:
            snapshot.total_value = total_value
            current_app.logger.info(f"Updated snapshot for {portfolio_name} on {normalized_report_date.strftime('%Y-%m-%d')} to {total_value}")
    else:
        snapshot = PortfolioSnapshot(
            portfolio_id=portfolio.id,
            date=normalized_report_date,
            total_value=total_value,
            report_data=report_data
        )
        db.session.add(snapshot)
        current_app.logger.info(f"Created snapshot for {portfolio_name} on {normalized_report_date.strftime('%Y-%m-%d')} with value {total_value}")

    # Always update report_data to ensure it is stored
    snapshot.report_data = report_data
    db.session.commit()

    snapshot_saved = PortfolioSnapshot.query.filter_by(portfolio_id=portfolio.id, date=normalized_report_date).first()
    current_app.logger.info(f"[DEBUG] Snapshot saved: {snapshot_saved}, report_data exists: {snapshot_saved.report_data is not None}")
    return snapshot_saved


def update_all_portfolio_snapshots():
    from flask import current_app
    import os
    snapshots = []
    reports_dir = current_app.config.get('IBKR_REPORTS_DIR')
    json_dir = os.path.join(reports_dir, 'json')
    current_app.logger.info(f"Scanning JSON reports in {json_dir}")

    for root, dirs, files in os.walk(json_dir):
        for filename in files:
            if filename.lower().endswith('.json') and "_converted" not in filename:
                json_file_path = os.path.join(root, filename)
                current_app.logger.info(f"Processing report: {json_file_path}")
                snapshot = update_portfolio_snapshot_from_ibkr(json_file_path)
                if snapshot:
                    snapshots.append(snapshot)
                else:
                    current_app.logger.error(f"Failed to update snapshot from {json_file_path}")
    return snapshots


def parse_date(date_str, default_date=None):
    """
    Attempt to parse a date string using multiple formats.
    """
    if not date_str:
        return default_date.date() if default_date else datetime.now().date()
    try:
        formats = ['%Y-%m-%d', '%m/%d/%y', '%m/%d/%Y', '%d-%m-%Y', '%d/%m/%Y']
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt).date()
            except ValueError:
                continue
        return default_date.date() if default_date else datetime.now().date()
    except:
        return default_date.date() if default_date else datetime.now().date()


def safe_float_convert(value, default=0.0):
    if value is None or str(value).strip() in ['', ' ']:
        return default
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, AttributeError):
        return default


def clean_string(value):
    if value is None:
        return ""
    return str(value).strip().strip('"')


def convert_broker_report_to_json(file_path: str, output_path: str = None) -> dict:
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        report_data = {
            "metadata": {
                "report_date": datetime.now().strftime("%Y-%m-%d"),
                "file_name": Path(file_path).name,
                "source": "Interactive Brokers"
            },
            "account_info": {},
            "key_statistics": {},
            "positions": {
                "etfs": [],
                "stocks": [],
                "cash": [],
                "summary": {}
            },
            "performance": {
                "benchmark_comparison": [],
                "by_asset_class": [],
                "by_financial_instrument": [],
                "by_region": [],
                "by_sector": [],
                "by_symbol": []
            },
            "allocation": {
                "by_asset_class": [],
                "by_financial_instrument": [],
                "by_region": [],
                "by_sector": []
            },
            "risk_measures": {},
            "income": {
                "projected": [],
                "dividends": [],
                "interest": []
            },
            "transactions": {
                "trades": [],
                "deposits_withdrawals": [],
                "corporate_actions": []
            },
            "fees": [],
            "esg_data": {}
        }
        current_section = ""
        for line in lines:
            parts = line.strip().split(',')
            if not parts or len(parts) < 2:
                continue
            section = parts[0]
            record_type = parts[1] if len(parts) > 1 else ""
            if section == "Introduction" and record_type == "Data":
                report_data["account_info"] = {
                    "name": clean_string(parts[2]),
                    "account": clean_string(parts[3]),
                    "base_currency": clean_string(parts[5]),
                    "account_type": clean_string(parts[6]),
                    "analysis_period": clean_string(parts[7]),
                    "performance_measure": clean_string(parts[8]) if len(parts) > 8 else "TWR"
                }
            elif section == "Key Statistics" and record_type == "Data":
                report_data["key_statistics"] = {
                    "beginning_nav": safe_float_convert(parts[2]),
                    "ending_nav": safe_float_convert(parts[3]),
                    "cumulative_return": safe_float_convert(parts[4]),
                    "monthly_return": {
                        "value": safe_float_convert(parts[5]),
                        "period": clean_string(parts[6])
                    },
                    "quarterly_return": {
                        "value": safe_float_convert(parts[7]),
                        "period": clean_string(parts[8])
                    },
                    "best_return": {
                        "value": safe_float_convert(parts[9]),
                        "date": clean_string(parts[10])
                    },
                    "worst_return": {
                        "value": safe_float_convert(parts[11]),
                        "date": clean_string(parts[12])
                    },
                    "mark_to_market": safe_float_convert(parts[13]),
                    "deposits_withdrawals": safe_float_convert(parts[14]),
                    "dividends": safe_float_convert(parts[15]),
                    "interest": safe_float_convert(parts[16]),
                    "fees_commissions": safe_float_convert(parts[17]),
                    "other": safe_float_convert(parts[18])
                }
            elif section == "Open Position Summary" and record_type == "Data":
                if len(parts) < 13:
                    continue
                if not 'Total' in parts[2]:
                    position_data = {
                        "date": clean_string(parts[2]),
                        "currency": clean_string(parts[4]),
                        "symbol": clean_string(parts[5]),
                        "description": clean_string(parts[6]),
                        "sector": clean_string(parts[7]),
                        "quantity": safe_float_convert(parts[8]),
                        "price": safe_float_convert(parts[9]),
                        "market_value": safe_float_convert(parts[10]),
                        "cost_basis": safe_float_convert(parts[11]),
                        "unrealized_pl": safe_float_convert(parts[12]),
                        "fx_rate": safe_float_convert(parts[13]) if len(parts) > 13 else 1.0
                    }
                    if 'ETFs' in parts[3]:
                        report_data["positions"]["etfs"].append(position_data)
                    elif 'Stocks' in parts[3]:
                        report_data["positions"]["stocks"].append(position_data)
                    elif 'Cash' in parts[3]:
                        cash_data = {
                            "currency": clean_string(parts[5]),
                            "description": clean_string(parts[6]),
                            "amount": safe_float_convert(parts[8]),
                            "exchange_rate": safe_float_convert(parts[9], 1.0),
                            "usd_equivalent": safe_float_convert(parts[10])
                        }
                        report_data["positions"]["cash"].append(cash_data)
                else:
                    if 'ETFs' in parts[3]:
                        report_data["positions"]["summary"]["etfs_total"] = {
                            "market_value": safe_float_convert(parts[10]),
                            "cost_basis": safe_float_convert(parts[11]),
                            "unrealized_pl": safe_float_convert(parts[12])
                        }
                    elif 'Stocks' in parts[3]:
                        report_data["positions"]["summary"]["stocks_total"] = {
                            "market_value": safe_float_convert(parts[10]),
                            "cost_basis": safe_float_convert(parts[11]),
                            "unrealized_pl": safe_float_convert(parts[12])
                        }
                    elif 'Cash' in parts[3]:
                        report_data["positions"]["summary"]["cash_total"] = {
                            "usd_equivalent": safe_float_convert(parts[10])
                        }
            elif section == "Performance by Symbol" and record_type == "Data":
                report_data["performance"]["by_symbol"].append({
                    "symbol": clean_string(parts[2]),
                    "description": clean_string(parts[3]),
                    "instrument_type": clean_string(parts[4]),
                    "sector": clean_string(parts[5]),
                    "avg_weight": safe_float_convert(parts[6]),
                    "return": safe_float_convert(parts[7]),
                    "contribution": safe_float_convert(parts[8]),
                    "unrealized_pl": safe_float_convert(parts[9]),
                    "realized_pl": safe_float_convert(parts[10]),
                    "is_open": clean_string(parts[11]) if len(parts) > 11 else ""
                })
            elif section == "Dividends" and record_type == "Data":
                report_data["income"]["dividends"].append({
                    "pay_date": clean_string(parts[2]),
                    "ex_date": clean_string(parts[3]),
                    "symbol": clean_string(parts[4]),
                    "note": clean_string(parts[5]),
                    "quantity": safe_float_convert(parts[6]),
                    "dividend_per_share": safe_float_convert(parts[7]),
                    "amount": safe_float_convert(parts[8])
                })
            elif section == "Interest Details" and record_type == "Data":
                report_data["income"]["interest"].append({
                    "date": clean_string(parts[2]),
                    "description": clean_string(parts[3]),
                    "amount": safe_float_convert(parts[4])
                })
            elif section == "Deposits And Withdrawals" and record_type == "Data":
                report_data["transactions"]["deposits_withdrawals"].append({
                    "date": clean_string(parts[2]),
                    "type": clean_string(parts[3]),
                    "description": clean_string(parts[4]),
                    "amount": safe_float_convert(parts[5])
                })
            elif section == "Trade Summary" and record_type == "Data":
                report_data["transactions"]["trades"].append({
                    "instrument_type": clean_string(parts[2]),
                    "currency": clean_string(parts[3]),
                    "symbol": clean_string(parts[4]),
                    "description": clean_string(parts[5]),
                    "sector": clean_string(parts[6]),
                    "quantity_bought": safe_float_convert(parts[7]),
                    "avg_price_bought": safe_float_convert(parts[8]),
                    "proceeds_bought": safe_float_convert(parts[9]),
                    "proceeds_bought_base": safe_float_convert(parts[10]),
                    "quantity_sold": safe_float_convert(parts[11]),
                    "avg_price_sold": safe_float_convert(parts[12]),
                    "proceeds_sold": safe_float_convert(parts[13]),
                    "proceeds_sold_base": safe_float_convert(parts[14]) if len(parts) > 14 else 0
                })
            elif section == "ESG" and record_type == "Data":
                if parts[3] == "ESG":
                    report_data["esg_data"]["overall_score"] = safe_float_convert(parts[4])
            elif section == "Fee Summary" and record_type == "Data":
                report_data["fees"].append({
                    "date": clean_string(parts[2]),
                    "description": clean_string(parts[3]),
                    "amount": safe_float_convert(parts[4])
                })

        etfs_value = report_data["positions"]["summary"].get("etfs_total", {}).get("market_value", 0)
        stocks_value = report_data["positions"]["summary"].get("stocks_total", {}).get("market_value", 0)
        cash_value = report_data["positions"]["summary"].get("cash_total", {}).get("usd_equivalent", 0)

        report_data["positions"]["summary"]["portfolio_total"] = {
            "total_value": etfs_value + stocks_value + cash_value,
            "total_positions": len(report_data["positions"]["etfs"]) +
                               len(report_data["positions"]["stocks"]) +
                               len(report_data["positions"]["cash"])
        }

        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=4, ensure_ascii=False)
                print(f"JSON data saved to: {output_path}")

        return report_data

    except Exception as e:
        error_data = {
            "error": True,
            "message": str(e),
            "file_path": file_path,
            "traceback": str(e.__traceback__)
        }
        print(f"Error processing file: {str(e)}")
        return error_data


def load_ibkr_nav(json_file_path=None):
    """
    Reads the IBKR JSON report and returns the ending NAV (as a float).
    If json_file_path is not provided, uses the default path from configuration.
    """
    from flask import current_app
    import os
    import json

    if json_file_path is None:
        reports_dir = current_app.config.get('IBKR_REPORTS_DIR')
        json_file_path = os.path.join(reports_dir, 'json', 'broker_report.json')

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        # Extract the ending NAV from the key_statistics section
        ending_nav = report_data.get("key_statistics", {}).get("ending_nav")
        if ending_nav is None:
            current_app.logger.error("No ending_nav found in the IBKR report.")
            return 0.0
        return float(ending_nav)
    except Exception as e:
        current_app.logger.error(f"Error loading IBKR NAV: {e}")
        return 0.0
