import json
from datetime import datetime
from pathlib import Path


def safe_float_convert(value, default=0.0):
    """Safely convert a value to float, handling empty strings and invalid values"""
    if value is None or str(value).strip() in ['', ' ']:
        return default
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, AttributeError):
        return default


def clean_string(value):
    """Clean string values, removing quotes and extra whitespace"""
    if value is None:
        return ""
    return str(value).strip().strip('"')


def convert_broker_report_to_json(file_path: str, output_path: str = None) -> dict:
    """Convert IBKR broker report CSV file to JSON format"""
    try:
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # Initialize the JSON structure
        report_data = {
            "metadata": {
                "report_date": datetime.now().strftime("%Y-%m-%d"),
                "file_name": Path(file_path).name,
                "source": "Interactive Brokers"
            },
            "account_info": {},
            "key_statistics": {},
            "positions": {
                "etfs": [],
                "stocks": [],
                "cash": [],
                "summary": {}
            },
            "performance": {
                "benchmark_comparison": [],
                "by_asset_class": [],
                "by_financial_instrument": [],
                "by_region": [],
                "by_sector": [],
                "by_symbol": []
            },
            "allocation": {
                "by_asset_class": [],
                "by_financial_instrument": [],
                "by_region": [],
                "by_sector": []
            },
            "risk_measures": {},
            "income": {
                "projected": [],
                "dividends": [],
                "interest": []
            },
            "transactions": {
                "trades": [],
                "deposits_withdrawals": [],
                "corporate_actions": []
            },
            "fees": [],
            "esg_data": {}
        }

        current_section = ""
        for line in lines:
            parts = line.strip().split(',')
            if not parts or len(parts) < 2:
                continue

            section = parts[0]
            record_type = parts[1] if len(parts) > 1 else ""

            # Process Introduction section
            if section == "Introduction" and record_type == "Data":
                report_data["account_info"] = {
                    "name": clean_string(parts[2]),
                    "account": clean_string(parts[3]),
                    "base_currency": clean_string(parts[5]),
                    "account_type": clean_string(parts[6]),
                    "analysis_period": clean_string(parts[7]),
                    "performance_measure": clean_string(parts[8]) if len(parts) > 8 else "TWR"
                }

            # Process Key Statistics section
            elif section == "Key Statistics" and record_type == "Data":
                report_data["key_statistics"] = {
                    "beginning_nav": safe_float_convert(parts[2]),
                    "ending_nav": safe_float_convert(parts[3]),
                    "cumulative_return": safe_float_convert(parts[4]),
                    "monthly_return": {
                        "value": safe_float_convert(parts[5]),
                        "period": clean_string(parts[6])
                    },
                    "quarterly_return": {
                        "value": safe_float_convert(parts[7]),
                        "period": clean_string(parts[8])
                    },
                    "best_return": {
                        "value": safe_float_convert(parts[9]),
                        "date": clean_string(parts[10])
                    },
                    "worst_return": {
                        "value": safe_float_convert(parts[11]),
                        "date": clean_string(parts[12])
                    },
                    "mark_to_market": safe_float_convert(parts[13]),
                    "deposits_withdrawals": safe_float_convert(parts[14]),
                    "dividends": safe_float_convert(parts[15]),
                    "interest": safe_float_convert(parts[16]),
                    "fees_commissions": safe_float_convert(parts[17]),
                    "other": safe_float_convert(parts[18])
                }

            # Process Open Position Summary section
            elif section == "Open Position Summary" and record_type == "Data":
                if len(parts) < 13:
                    continue

                if not 'Total' in parts[2]:
                    position_data = {
                        "date": clean_string(parts[2]),
                        "currency": clean_string(parts[4]),
                        "symbol": clean_string(parts[5]),
                        "description": clean_string(parts[6]),
                        "sector": clean_string(parts[7]),
                        "quantity": safe_float_convert(parts[8]),
                        "price": safe_float_convert(parts[9]),
                        "market_value": safe_float_convert(parts[10]),
                        "cost_basis": safe_float_convert(parts[11]),
                        "unrealized_pl": safe_float_convert(parts[12]),
                        "fx_rate": safe_float_convert(parts[13]) if len(parts) > 13 else 1.0
                    }

                    if 'ETFs' in parts[3]:
                        report_data["positions"]["etfs"].append(position_data)
                    elif 'Stocks' in parts[3]:
                        report_data["positions"]["stocks"].append(position_data)
                    elif 'Cash' in parts[3]:
                        cash_data = {
                            "currency": clean_string(parts[5]),
                            "description": clean_string(parts[6]),
                            "amount": safe_float_convert(parts[8]),
                            "exchange_rate": safe_float_convert(parts[9], 1.0),
                            "usd_equivalent": safe_float_convert(parts[10])
                        }
                        report_data["positions"]["cash"].append(cash_data)
                else:
                    # Process total rows
                    if 'ETFs' in parts[3]:
                        report_data["positions"]["summary"]["etfs_total"] = {
                            "market_value": safe_float_convert(parts[10]),
                            "cost_basis": safe_float_convert(parts[11]),
                            "unrealized_pl": safe_float_convert(parts[12])
                        }
                    elif 'Stocks' in parts[3]:
                        report_data["positions"]["summary"]["stocks_total"] = {
                            "market_value": safe_float_convert(parts[10]),
                            "cost_basis": safe_float_convert(parts[11]),
                            "unrealized_pl": safe_float_convert(parts[12])
                        }
                    elif 'Cash' in parts[3]:
                        report_data["positions"]["summary"]["cash_total"] = {
                            "usd_equivalent": safe_float_convert(parts[10])
                        }

            # Process Performance sections
            elif section == "Performance by Symbol" and record_type == "Data":
                report_data["performance"]["by_symbol"].append({
                    "symbol": clean_string(parts[2]),
                    "description": clean_string(parts[3]),
                    "instrument_type": clean_string(parts[4]),
                    "sector": clean_string(parts[5]),
                    "avg_weight": safe_float_convert(parts[6]),
                    "return": safe_float_convert(parts[7]),
                    "contribution": safe_float_convert(parts[8]),
                    "unrealized_pl": safe_float_convert(parts[9]),
                    "realized_pl": safe_float_convert(parts[10]),
                    "is_open": clean_string(parts[11]) if len(parts) > 11 else ""
                })

            # Process Dividends section
            elif section == "Dividends" and record_type == "Data":
                report_data["income"]["dividends"].append({
                    "pay_date": clean_string(parts[2]),
                    "ex_date": clean_string(parts[3]),
                    "symbol": clean_string(parts[4]),
                    "note": clean_string(parts[5]),
                    "quantity": safe_float_convert(parts[6]),
                    "dividend_per_share": safe_float_convert(parts[7]),
                    "amount": safe_float_convert(parts[8])
                })

            # Process Interest Details section
            elif section == "Interest Details" and record_type == "Data":
                report_data["income"]["interest"].append({
                    "date": clean_string(parts[2]),
                    "description": clean_string(parts[3]),
                    "amount": safe_float_convert(parts[4])
                })

            # Process Deposits and Withdrawals section
            elif section == "Deposits And Withdrawals" and record_type == "Data":
                report_data["transactions"]["deposits_withdrawals"].append({
                    "date": clean_string(parts[2]),
                    "type": clean_string(parts[3]),
                    "description": clean_string(parts[4]),
                    "amount": safe_float_convert(parts[5])
                })

            # Process Trade Summary section
            elif section == "Trade Summary" and record_type == "Data":
                report_data["transactions"]["trades"].append({
                    "instrument_type": clean_string(parts[2]),
                    "currency": clean_string(parts[3]),
                    "symbol": clean_string(parts[4]),
                    "description": clean_string(parts[5]),
                    "sector": clean_string(parts[6]),
                    "quantity_bought": safe_float_convert(parts[7]),
                    "avg_price_bought": safe_float_convert(parts[8]),
                    "proceeds_bought": safe_float_convert(parts[9]),
                    "proceeds_bought_base": safe_float_convert(parts[10]),
                    "quantity_sold": safe_float_convert(parts[11]),
                    "avg_price_sold": safe_float_convert(parts[12]),
                    "proceeds_sold": safe_float_convert(parts[13]),
                    "proceeds_sold_base": safe_float_convert(parts[14]) if len(parts) > 14 else 0
                })

            # Process ESG section
            elif section == "ESG" and record_type == "Data":
                if parts[3] == "ESG":
                    report_data["esg_data"]["overall_score"] = safe_float_convert(parts[4])

            # Process Fee Summary section
            elif section == "Fee Summary" and record_type == "Data":
                report_data["fees"].append({
                    "date": clean_string(parts[2]),
                    "description": clean_string(parts[3]),
                    "amount": safe_float_convert(parts[4])
                })

        # Calculate portfolio totals
        etfs_value = report_data["positions"]["summary"].get("etfs_total", {}).get("market_value", 0)
        stocks_value = report_data["positions"]["summary"].get("stocks_total", {}).get("market_value", 0)
        cash_value = report_data["positions"]["summary"].get("cash_total", {}).get("usd_equivalent", 0)

        report_data["positions"]["summary"]["portfolio_total"] = {
            "total_value": etfs_value + stocks_value + cash_value,
            "total_positions": len(report_data["positions"]["etfs"]) +
                               len(report_data["positions"]["stocks"]) +
                               len(report_data["positions"]["cash"])
        }

        # Save to JSON file if output path is provided
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=4, ensure_ascii=False)
                print(f"JSON data saved to: {output_path}")

        return report_data

    except Exception as e:
        error_data = {
            "error": True,
            "message": str(e),
            "file_path": file_path,
            "traceback": str(e.__traceback__)
        }
        print(f"Error processing file: {str(e)}")
        return error_data


def main():
    # Example usage
    input_file = 'U6454299_20250305.csv'
    output_file = 'U6454299_20250305.json'

    # Convert CSV to JSON and save
    json_data = convert_broker_report_to_json(input_file, output_file)

    # Print sample of the data structure (for verification)
    print("\nSample of converted JSON data structure:")
    print(json.dumps(
        {k: json_data[k] for k in list(json_data.keys())[:2]},
        indent=2
    ))


if __name__ == "__main__":
    main()