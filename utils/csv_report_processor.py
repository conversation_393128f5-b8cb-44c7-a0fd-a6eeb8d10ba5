# File: utils/csv_report_processor.py
import os
import pandas as pd
from datetime import datetime
from models import db
from models.performance import PortfolioSnapshot
from models.portfolio import Portfolio
import os
import json
import pandas as pd
from datetime import datetime
from pathlib import Path
from flask import current_app
from werkzeug.utils import secure_filename

from models import db
from models.performance import PortfolioSnapshot
from models.portfolio import Security, Portfolio, PortfolioAllocation
from models.security_price import SecurityPrice
from utils.ibkr_importer import process_ibkr_json, process_ibkr_csv


def process_ibkr_report(file_path, file_type='json'):
    """
    Process an IBKR report file (JSON or CSV) and update the database.
    For CSV files, the report is converted and processed via process_ibkr_csv.
    """
    if file_type.lower() == 'json':
        return process_ibkr_json(file_path)
    elif file_type.lower() == 'csv':
        # For CSV reports, process using our CSV report processor logic.
        # Here we first convert the CSV into a minimal report_data dict.
        return process_ibkr_csv(file_path)
    else:
        raise ValueError(f"Unsupported file type: {file_type}")

def process_csv_reports(folder_path, portfolio_name):
    """
    Process all CSV files in the given folder for a specific portfolio.
    Extract the ending NAV (and any additional important parameters) from each CSV file and store
    it in the database as a PortfolioSnapshot record.

    Args:
        folder_path (str): Path to the folder containing CSV reports.
        portfolio_name (str): Name of the portfolio (used to locate or create the portfolio).

    Returns:
        dict: A summary of the processing results.
    """
    processed = 0
    errors = []

    # Find the portfolio by name; if not found, create one.
    portfolio = Portfolio.query.filter_by(name=portfolio_name).first()
    if not portfolio:
        portfolio = Portfolio(
            name=portfolio_name,
            description=f"Portfolio created from CSV reports in {folder_path}",
            risk_level="Medium"
        )
        db.session.add(portfolio)
        db.session.commit()

    # Loop through each CSV file in the folder.
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.csv'):
            file_path = os.path.join(folder_path, filename)
            try:
                # Assume the filename contains a date in YYYYMMDD format at the end.
                basename = os.path.splitext(filename)[0]
                date_str = basename[-8:]
                report_date = datetime.strptime(date_str, "%Y%m%d")

                # Read CSV file using pandas.
                df = pd.read_csv(file_path)

                # Attempt to extract the ending NAV from the CSV.
                if 'Ending NAV' in df.columns:
                    nav = df['Ending NAV'].iloc[-1]
                elif 'NAV' in df.columns:
                    nav = df['NAV'].iloc[-1]
                else:
                    raise ValueError("No 'Ending NAV' or 'NAV' column found in the CSV.")

                # Check if a snapshot for this portfolio and date already exists.
                snapshot = PortfolioSnapshot.query.filter_by(
                    portfolio_id=portfolio.id,
                    date=report_date
                ).first()
                if snapshot is None:
                    new_snapshot = PortfolioSnapshot(
                        portfolio_id=portfolio.id,
                        date=report_date,
                        total_value=float(nav)
                    )
                    db.session.add(new_snapshot)
                    processed += 1
                # If snapshot exists, skip this file.
            except Exception as e:
                errors.append(f"{filename}: {str(e)}")
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        errors.append(f"Commit error: {str(e)}")

    return {
        "status": "success" if processed > 0 else "no_updates",
        "processed": processed,
        "errors": errors
    }
