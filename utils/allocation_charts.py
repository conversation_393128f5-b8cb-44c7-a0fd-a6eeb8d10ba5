import io
import matplotlib
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.figure import Figure
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas

# Set matplotlib to use Agg backend (non-interactive)
matplotlib.use('Agg')

# Custom color scheme
BRAND_COLORS = [
    '#0a2463',  # Deep blue
    '#247ba0',  # Medium blue
    '#1e88e5',  # Light blue
    '#43a047',  # Green
    '#ff9800',  # Orange
    '#e53935',  # Red
    '#8e24aa',  # Purple
    '#3949ab',  # Indigo
    '#00acc1',  # Cyan
    '#7cb342',  # Light green
]


def generate_allocation_chart_as_bytes(allocation_data):
    """
    Generate a professional-looking pie chart for portfolio allocation
    and return the raw image bytes.

    Args:
        allocation_data (dict): Dictionary with:
            'labels': List of labels for pie chart
            'values': List of values corresponding to labels
            'colors': Optional list of colors to use (will use default if not provided)

    Returns:
        bytes: PNG image data as bytes
    """
    # Extract data
    labels = allocation_data.get('labels', [])
    values = allocation_data.get('values', [])
    colors = allocation_data.get('colors', BRAND_COLORS[:len(labels)])

    # Create figure with transparent background
    fig = Figure(figsize=(8, 6), dpi=120, facecolor='none')
    ax = fig.add_subplot(111)

    # Create a modified colormap based on our brand colors if there are many categories
    if len(labels) > len(BRAND_COLORS):
        colors = plt.cm.tab20(np.arange(len(labels)))

    # Create pie chart with sophisticated styling
    wedges, texts, autotexts = ax.pie(
        values,
        labels=None,  # We'll add custom legend
        colors=colors,
        autopct='%1.1f%%',
        startangle=90,
        shadow=False,
        wedgeprops={
            'edgecolor': 'white',
            'linewidth': 1,
            'antialiased': True
        },
        textprops={
            'fontsize': 12,
            'fontweight': 'bold',
            'color': '#333333'
        }
    )

    # Equal aspect ratio ensures that pie is drawn as a circle
    ax.axis('equal')

    # Set title with custom styling
    ax.set_title(
        'Portfolio Allocation',
        fontsize=18,
        fontweight='bold',
        color='#0a2463',
        pad=20
    )

    # Create a custom legend
    legend = ax.legend(
        wedges,
        labels,
        title="Asset Classes",
        loc="center left",
        bbox_to_anchor=(1, 0, 0.5, 1),
        fontsize=10
    )
    legend.get_title().set_fontweight('bold')

    # Improve the appearance of percentage labels
    for autotext in autotexts:
        autotext.set_fontsize(10)
        autotext.set_fontweight('bold')
        autotext.set_color('white')

    # Add subtle grid in background (optional, depends on design preference)
    # ax.grid(color='#f0f0f0', linestyle='-', linewidth=0.5, alpha=0.3)

    # Adjust layout to make room for the legend
    fig.tight_layout()

    # Save to BytesIO
    buf = io.BytesIO()
    canvas = FigureCanvas(fig)
    canvas.print_png(buf)
    plt.close(fig)

    # Get the bytes
    buf.seek(0)
    return buf.getvalue()


def generate_performance_chart_as_bytes(dates, values, benchmark_values=None, title="Investment Performance"):
    """
    Generate a professional line chart for performance over time
    and return the raw image bytes.

    Args:
        dates (list): List of dates for x-axis
        values (list): List of values for main series
        benchmark_values (list, optional): List of benchmark values for comparison
        title (str): Chart title

    Returns:
        bytes: PNG image data as bytes
    """
    # Create figure with transparent background
    fig = Figure(figsize=(10, 6), dpi=120, facecolor='none')
    ax = fig.add_subplot(111)

    # Plot the main performance line with gradient alpha fill
    line, = ax.plot(dates, values, color='#0a2463', linewidth=2.5, marker='o',
                    markersize=5, label='Portfolio Value')

    # Add shaded area beneath the line
    ax.fill_between(dates, values, color='#0a2463', alpha=0.1)

    # Add benchmark line if provided
    if benchmark_values:
        ax.plot(dates, benchmark_values, color='#e53935', linewidth=1.5, linestyle='--',
                marker='s', markersize=4, label='Benchmark')

    # Customize grid
    ax.grid(True, linestyle='--', linewidth=0.5, alpha=0.3)

    # Customize spines
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_linewidth(0.5)
    ax.spines['left'].set_linewidth(0.5)

    # Format X and Y axis
    ax.tick_params(axis='both', which='major', labelsize=10)

    # Format y-axis ticks as currency
    ax.yaxis.set_major_formatter('${x:,.0f}')

    # Add labels and title
    ax.set_xlabel('Date', fontsize=12, fontweight='bold', labelpad=10)
    ax.set_ylabel('Value ($)', fontsize=12, fontweight='bold', labelpad=10)
    ax.set_title(title, fontsize=16, fontweight='bold', color='#0a2463', pad=20)

    # Add legend
    ax.legend(loc='upper left', frameon=True, fancybox=True, framealpha=0.8,
              shadow=True, fontsize=10)

    # Add annotations for starting and ending values
    start_value = values[0]
    end_value = values[-1]
    percent_change = ((end_value - start_value) / start_value) * 100

    # Starting point annotation
    ax.annotate(
        f'${start_value:,.2f}',
        xy=(dates[0], start_value),
        xytext=(10, -20),
        textcoords='offset points',
        ha='left',
        fontsize=9,
        bbox=dict(boxstyle='round,pad=0.3', fc='white', ec='#cccccc', alpha=0.8)
    )

    # Ending point annotation with percent change
    ax.annotate(
        f'${end_value:,.2f} ({percent_change:+.2f}%)',
        xy=(dates[-1], end_value),
        xytext=(-10, -20) if percent_change < 0 else (-10, 20),
        textcoords='offset points',
        ha='right',
        fontsize=9,
        bbox=dict(
            boxstyle='round,pad=0.3',
            fc='#e8f5e9' if percent_change >= 0 else '#ffebee',
            ec='#43a047' if percent_change >= 0 else '#e53935',
            alpha=0.8
        )
    )

    # Adjust layout
    fig.tight_layout()

    # Save to BytesIO
    buf = io.BytesIO()
    canvas = FigureCanvas(fig)
    canvas.print_png(buf)
    plt.close(fig)

    # Get the bytes
    buf.seek(0)
    return buf.getvalue()


def generate_comparison_chart_as_bytes(labels, portfolio_returns, benchmark_returns, title="Performance Comparison"):
    """
    Generate a bar chart comparing portfolio vs benchmark returns
    and return the raw image bytes.

    Args:
        labels (list): List of period labels (e.g., ['1M', '3M', '1Y', '3Y', '5Y'])
        portfolio_returns (list): List of portfolio return percentages
        benchmark_returns (list): List of benchmark return percentages
        title (str): Chart title

    Returns:
        bytes: PNG image data as bytes
    """
    # Set width of bars
    bar_width = 0.35

    # Create figure with transparent background
    fig = Figure(figsize=(10, 6), dpi=120, facecolor='none')
    ax = fig.add_subplot(111)

    # Set position of bars on x axis
    r1 = np.arange(len(labels))
    r2 = [x + bar_width for x in r1]

    # Create bars
    portfolio_bars = ax.bar(r1, portfolio_returns, width=bar_width, color='#0a2463', label='Portfolio')
    benchmark_bars = ax.bar(r2, benchmark_returns, width=bar_width, color='#e53935', label='Benchmark')

    # Function to add value labels above bars
    def add_labels(bars):
        for bar in bars:
            height = bar.get_height()
            value = height
            color = '#43a047' if value >= 0 else '#e53935'
            ax.annotate(
                f'{value:+.2f}%',
                xy=(bar.get_x() + bar.get_width() / 2, height),
                xytext=(0, 3),  # 3 points vertical offset
                textcoords="offset points",
                ha='center',
                va='bottom',
                fontsize=9,
                fontweight='bold',
                color=color
            )

    # Add value labels
    add_labels(portfolio_bars)
    add_labels(benchmark_bars)

    # Add labels, title, and legend
    ax.set_xlabel('Time Period', fontsize=12, fontweight='bold', labelpad=10)
    ax.set_ylabel('Return (%)', fontsize=12, fontweight='bold', labelpad=10)
    ax.set_title(title, fontsize=16, fontweight='bold', color='#0a2463', pad=20)
    ax.set_xticks([r + bar_width / 2 for r in range(len(labels))])
    ax.set_xticklabels(labels)

    # Customize grid
    ax.grid(True, axis='y', linestyle='--', linewidth=0.5, alpha=0.3)

    # Customize spines
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_linewidth(0.5)
    ax.spines['left'].set_linewidth(0.5)

    # Add a horizontal line at y=0
    ax.axhline(y=0, color='#bbbbbb', linestyle='-', linewidth=0.8)

    # Add legend
    ax.legend(loc='upper right', frameon=True, fancybox=True, framealpha=0.8,
              shadow=True, fontsize=10)

    # Adjust layout
    fig.tight_layout()

    # Save to BytesIO
    buf = io.BytesIO()
    canvas = FigureCanvas(fig)
    canvas.print_png(buf)
    plt.close(fig)

    # Get the bytes
    buf.seek(0)
    return buf.getvalue()


def generate_returns_heatmap_as_bytes(returns_data, title="Monthly Returns Heatmap"):
    """
    Generate a heatmap of monthly returns
    and return the raw image bytes.

    Args:
        returns_data (dict): Dictionary with years as keys and lists of 12 monthly returns as values
        title (str): Chart title

    Returns:
        bytes: PNG image data as bytes
    """
    # Extract years and create data matrix
    years = list(returns_data.keys())
    data = np.array([returns_data[year] for year in years])

    # Define custom colormap for returns (red for negative, green for positive)
    cmap = LinearSegmentedColormap.from_list(
        'returns_colormap',
        [
            (0.0, '#ffcdd2'),  # Light red for most negative
            (0.4, '#ef9a9a'),  # Medium red
            (0.5, '#f5f5f5'),  # Neutral gray/white
            (0.6, '#a5d6a7'),  # Medium green
            (1.0, '#43a047')  # Dark green for most positive
        ]
    )

    # Create figure with transparent background
    fig = Figure(figsize=(12, len(years) * 0.6 + 2), dpi=120, facecolor='none')
    ax = fig.add_subplot(111)

    # Create heatmap
    months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    im = ax.imshow(data, cmap=cmap, aspect='auto')

    # We want to show all ticks and label them
    ax.set_xticks(np.arange(len(months)))
    ax.set_yticks(np.arange(len(years)))

    # Label ticks with months and years
    ax.set_xticklabels(months)
    ax.set_yticklabels(years)

    # Rotate month labels
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

    # Add text annotations showing the actual return values
    for i in range(len(years)):
        for j in range(len(months)):
            value = data[i, j]
            text_color = 'black' if abs(value) < 5 else 'white'

            ax.text(j, i, f"{value:+.2f}%",
                    ha="center", va="center", color=text_color,
                    fontsize=9, fontweight='bold')

    # Add title and labels
    ax.set_title(title, fontsize=16, fontweight='bold', color='#0a2463', pad=20)
    fig.colorbar(im, ax=ax, orientation='vertical', shrink=0.8,
                 label='Monthly Return (%)')

    # Adjust layout
    fig.tight_layout()

    # Save to BytesIO
    buf = io.BytesIO()
    canvas = FigureCanvas(fig)
    canvas.print_png(buf)
    plt.close(fig)

    # Get the bytes
    buf.seek(0)
    return buf.getvalue()