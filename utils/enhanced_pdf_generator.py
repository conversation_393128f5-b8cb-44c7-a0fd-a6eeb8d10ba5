import io
from datetime import datetime
import base64
from functools import partial

from PIL import Image, ImageDraw

from reportlab.lib.pagesizes import A4, letter, landscape
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm, cm
from reportlab.platypus import (SimpleDocTemplate, Paragraph, Spacer, Image, Table,
                                TableStyle, PageBreak, KeepTogether, Flowable, Frame)
from reportlab.graphics.shapes import Drawing, Line, Rect, String, Group
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.linecharts import Horizontal<PERSON>ine<PERSON>hart
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.textlabels import Label
from reportlab.graphics.widgets.markers import makeMarker
from reportlab.platypus.tableofcontents import TableOfContents
from reportlab.pdfgen.canvas import Canvas

# Custom colors for branding
BRAND_PRIMARY = colors.HexColor("#0a2463")  # Deep blue
BRAND_SECONDARY = colors.HexColor("#247ba0")  # Medium blue
BRAND_ACCENT = colors.HexColor("#1e88e5")  # Light blue
BRAND_SUCCESS = colors.HexColor("#43a047")  # Green
BRAND_WARNING = colors.HexColor("#ff9800")  # Orange
BRAND_DANGER = colors.HexColor("#e53935")  # Red
BRAND_LIGHT_BG = colors.HexColor("#f8f9fa")  # Light background
BRAND_DARK_BG = colors.HexColor("#2c3e50")  # Dark background
BRAND_TEXT = colors.HexColor("#212121")  # Main text
BRAND_TEXT_LIGHT = colors.HexColor("#6c757d")  # Light text


# Custom flowable for horizontal separators
class HRFlowable(Flowable):
    """A horizontal separator line with gradient effect"""

    def __init__(self, width=None, thickness=1, color=BRAND_PRIMARY,
                 gradient_end=BRAND_ACCENT, space_before=0.1 * inch,
                 space_after=0.1 * inch, dash=None):
        Flowable.__init__(self)
        self.width = width
        self.height = thickness
        self.thickness = thickness
        self.color = color
        self.gradient_end = gradient_end
        self.space_before = space_before
        self.space_after = space_after
        self.dash = dash

    def draw(self):
        if self.width:
            available_width = self.width
        else:
            available_width = self.canv._pagesize[0] - 2 * inch

        self.canv.saveState()

        # Create a gradient effect from left to right
        num_segments = 100
        segment_width = available_width / num_segments

        for i in range(num_segments):
            r1, g1, b1 = self.color.red, self.color.green, self.color.blue
            r2, g2, b2 = self.gradient_end.red, self.gradient_end.green, self.gradient_end.blue

            # Calculate color for this segment
            ratio = i / (num_segments - 1)
            r = r1 + (r2 - r1) * ratio
            g = g1 + (g2 - g1) * ratio
            b = b1 + (b2 - b1) * ratio

            segment_color = colors.Color(r, g, b)
            self.canv.setStrokeColor(segment_color)
            self.canv.setLineWidth(self.thickness)

            if self.dash:
                self.canv.setDash(self.dash)

            x1 = segment_width * i
            x2 = segment_width * (i + 1)

            self.canv.line(x1, 0, x2, 0)

        self.canv.restoreState()


# Custom flowable for watermarked company logo
class WatermarkBrand(Flowable):
    """A watermark with the company logo in the background"""

    def __init__(self, width=None, height=None, opacity=0.07):
        Flowable.__init__(self)
        self.width = width or 400
        self.height = height or 400
        self.opacity = opacity

    def draw(self):
        # Create a canvas for the watermark
        self.canv.saveState()
        self.canv.setFillColor(BRAND_PRIMARY)
        self.canv.setFillAlpha(self.opacity)

        # Draw a stylized "K" for Keheilan Fund
        self.canv.setFont("Helvetica-Bold", 200)
        # Set explicit coordinates instead of calculating them
        self.canv.drawString(100, 0, "K")  # Fixed position instead of centered

        self.canv.restoreState()


# Custom header and footer class
class HeaderFooterCanvas(Canvas):
    """Canvas that includes a header and footer on each page"""

    def __init__(self, *args, **kwargs):
        self.client_name = kwargs.pop('client_name', 'Valued Client')
        self.report_title = kwargs.pop('report_title', 'Investment Report')
        self.report_date = kwargs.pop('report_date', datetime.now())

        # Pass remaining arguments to Canvas constructor
        Canvas.__init__(self, *args, **kwargs)
        self.pages = []

        # Pass remaining arguments to Canvas constructor
    def showPage(self):
        """Override to process page and add header/footer before showing"""
        self.pages.append(dict(self.__dict__))
        self._startPage()

    def save(self):
        """Add header and footer to each page before saving"""
        page_count = len(self.pages)
        for page in self.pages:
            self.__dict__.update(page)
            self.draw_header()
            self.draw_footer(page_count)
            Canvas.showPage(self)

        Canvas.save(self)

    def draw_header(self):
        """Draw the header with logo and report title"""
        self.saveState()

        # Logo placeholder (would be replaced with actual logo)
        self.setFillColor(BRAND_PRIMARY)
        self.rect(30, self._pagesize[1] - 50, 20, 20, fill=1)
        self.setFillColor(BRAND_SECONDARY)
        self.rect(50, self._pagesize[1] - 50, 10, 20, fill=1)

        # Company name
        self.setFont("Helvetica-Bold", 16)
        self.setFillColor(BRAND_PRIMARY)
        self.drawString(70, self._pagesize[1] - 30, "KEHEILAN FUND")

        # Report name
        self.setFont("Helvetica", 12)
        self.setFillColor(BRAND_TEXT)
        self.drawString(70, self._pagesize[1] - 46, self.report_title)

        # Client name on the right
        self.setFont("Helvetica", 10)
        self.setFillColor(BRAND_TEXT_LIGHT)
        client_text = f"PREPARED FOR: {self.client_name}"
        client_width = self.stringWidth(client_text, "Helvetica", 10)
        self.drawRightString(self._pagesize[0] - 30, self._pagesize[1] - 30, client_text)

        # Date on the right
        date_text = f"REPORT DATE: {self.report_date.strftime('%B %d, %Y')}"
        self.drawRightString(self._pagesize[0] - 30, self._pagesize[1] - 46, date_text)

        # Draw line below header
        self.setStrokeColor(BRAND_PRIMARY)
        self.setLineWidth(1.5)
        self.line(30, self._pagesize[1] - 60, self._pagesize[0] - 30, self._pagesize[1] - 60)

        self.restoreState()

    def draw_footer(self, page_count):
        """Draw the footer with page number and disclaimer"""
        self.saveState()

        # Page number
        self.setFont("Helvetica", 8)
        self.setFillColor(BRAND_TEXT_LIGHT)
        page_text = f"Page {self._pageNumber} of {page_count}"
        self.drawRightString(self._pagesize[0] - 30, 40, page_text)

        # Disclaimer
        self.setFont("Helvetica-Oblique", 7)
        disclaimer = "Past performance is not indicative of future results. Investments may lose value."
        self.drawString(30, 40, disclaimer)

        # Line above footer
        self.setStrokeColor(BRAND_LIGHT_BG)
        self.setLineWidth(0.5)
        self.line(30, 50, self._pagesize[0] - 30, 50)

        # Add a subtle watermark on the bottom right
        self.setFont("Helvetica-Bold", 7)
        self.setFillColor(BRAND_PRIMARY)
        self.setFillAlpha(0.5)
        self.drawRightString(self._pagesize[0] - 30, 30, "KEHEILAN FUND MANAGEMENT")

        self.restoreState()


def generate_enhanced_pdf_report(
        report,
        client_info,
        start_date,
        end_date,
        performance_chart_bytes=None,
        allocation_chart_bytes=None
):
    """
    Generate a professionally designed PDF report using ReportLab.

    Args:
        report (dict): Main report data containing investments, performance, etc.
        client_info (dict): Information about the client
        start_date (datetime): Start date of report period
        end_date (datetime): End date of report period
        performance_chart_bytes (bytes, optional): Performance chart image data
        allocation_chart_bytes (bytes, optional): Allocation chart image data

    Returns:
        bytes: The PDF file content as raw bytes
    """
    # 1) Set up the buffer and document
    buffer = io.BytesIO()

    # Create the document with custom canvas for header and footer
    doc = SimpleDocTemplate(
        buffer,
        pagesize=letter,
        leftMargin=0.75 * inch,
        rightMargin=0.75 * inch,
        topMargin=1.2 * inch,
        bottomMargin=0.75 * inch,
        title=f"Investment Report - {client_info.get('name', 'Client')}",
        author="Keheilan Fund",
        subject=f"Investment Report {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
    )

    # 2) Define custom styles
    styles = getSampleStyleSheet()

    style_title = ParagraphStyle(
        name="Title",
        parent=styles['Title'],
        fontName='Helvetica-Bold',
        fontSize=24,
        textColor=BRAND_PRIMARY,
        spaceAfter=0.3 * inch,
        spaceBefore=0.2 * inch,
        alignment=TA_LEFT,
    )

    style_subtitle = ParagraphStyle(
        name="Subtitle",
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        fontSize=14,
        textColor=BRAND_SECONDARY,
        spaceAfter=0.2 * inch,
        alignment=TA_LEFT,
    )

    style_section_title = ParagraphStyle(
        name="SectionTitle",
        parent=styles['Heading2'],
        fontName='Helvetica-Bold',
        fontSize=16,
        textColor=BRAND_PRIMARY,
        spaceBefore=0.3 * inch,
        spaceAfter=0.15 * inch,
    )

    style_subsection_title = ParagraphStyle(
        name="SubsectionTitle",
        parent=styles['Heading3'],
        fontName='Helvetica-Bold',
        fontSize=12,
        textColor=BRAND_SECONDARY,
        spaceBefore=0.2 * inch,
        spaceAfter=0.1 * inch,
    )

    style_normal = ParagraphStyle(
        name="Normal",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=10,
        textColor=BRAND_TEXT,
        leading=14,
    )

    style_emphasis = ParagraphStyle(
        name="Emphasis",
        parent=style_normal,
        fontName='Helvetica-Bold',
        textColor=BRAND_PRIMARY,
    )

    style_small = ParagraphStyle(
        name="Small",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=8,
        textColor=BRAND_TEXT_LIGHT,
        leading=10,
    )

    style_table_header = ParagraphStyle(
        name="TableHeader",
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        fontSize=9,
        textColor=colors.white,
        alignment=TA_CENTER,
    )

    style_table_cell = ParagraphStyle(
        name="TableCell",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=9,
        leading=12,
    )

    style_table_cell_right = ParagraphStyle(
        name="TableCellRight",
        parent=style_table_cell,
        alignment=TA_RIGHT,
    )

    style_data_highlight = ParagraphStyle(
        name="DataHighlight",
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        fontSize=14,
        textColor=BRAND_PRIMARY,
    )

    style_data_value = ParagraphStyle(
        name="DataValue",
        parent=style_data_highlight,
        fontSize=20,
    )

    style_data_label = ParagraphStyle(
        name="DataLabel",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=10,
        textColor=BRAND_TEXT_LIGHT,
    )

    style_footer = ParagraphStyle(
        name="Footer",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=8,
        textColor=BRAND_TEXT_LIGHT,
        alignment=TA_CENTER,
    )

    # 3) Create the story content
    story = []

    # Cover page with client info and summary
    client_name = client_info.get('name', 'Valued Client')

    # Create the watermark background
    story.append(WatermarkBrand())

    # Report Title
    story.append(Paragraph("INVESTMENT PORTFOLIO SUMMARY", style_title))
    story.append(
        Paragraph(f"Performance Review: {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}",
                  style_subtitle))

    # Add gradient line
    story.append(HRFlowable(thickness=2))
    story.append(Spacer(1, 0.3 * inch))

    # Client information
    story.append(Paragraph("CLIENT INFORMATION", style_subsection_title))

    client_data = [
        ["Name:", client_info.get('name', 'N/A')],
        ["Account ID:", client_info.get('id', 'N/A')],
        ["Portfolio Manager:", "Alexander Hamilton, CFA"],
        ["Contact:", "<EMAIL> | (555) 123-4567"]
    ]

    client_table = Table(client_data, colWidths=[1.5 * inch, 4 * inch])
    client_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('TEXTCOLOR', (0, 0), (0, -1), BRAND_SECONDARY),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
    ]))

    story.append(client_table)
    story.append(Spacer(1, 0.2 * inch))

    # Executive Summary
    story.append(Paragraph("EXECUTIVE SUMMARY", style_subsection_title))

    # Format the data for display
    total_invested = report.get('total_invested', 0)
    total_current_value = report.get('total_current_value', 0)
    total_return_percentage = report.get('total_return_percentage', 0)
    active_investments_count = report.get('active_investments_count', 0)

    # Calculate dollar return
    dollar_return = total_current_value - total_invested

    # Create the summary data cards
    summary_data = [
        # First row with 4 key metrics
        [
            # Total Invested
            [
                Paragraph("TOTAL INVESTED", style_data_label),
                Paragraph(f"${total_invested:,.2f}", style_data_value)
            ],
            # Current Value
            [
                Paragraph("CURRENT VALUE", style_data_label),
                Paragraph(f"${total_current_value:,.2f}", style_data_value)
            ],
            # Return %
            [
                Paragraph("TOTAL RETURN", style_data_label),
                Paragraph(f"{total_return_percentage:+.2f}%",
                          style_data_value if total_return_percentage >= 0 else ParagraphStyle(
                              name="NegativeValue", parent=style_data_value, textColor=BRAND_DANGER
                          ))
            ],
            # Return $
            [
                Paragraph("PROFIT/LOSS", style_data_label),
                Paragraph(f"${dollar_return:+,.2f}", style_data_value if dollar_return >= 0 else ParagraphStyle(
                    name="NegativeValue", parent=style_data_value, textColor=BRAND_DANGER
                ))
            ]
        ]
    ]

    summary_table = Table(summary_data, colWidths=[1.65 * inch, 1.65 * inch, 1.65 * inch, 1.65 * inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, 0), colors.HexColor("#EFF6FF")),  # Light blue
        ('BACKGROUND', (1, 0), (1, 0), colors.HexColor("#ECFDF5")),  # Light green
        ('BACKGROUND', (2, 0), (2, 0), colors.white if total_return_percentage >= 0 else colors.HexColor("#FEF2F2")),
        # White/light red
        ('BACKGROUND', (3, 0), (3, 0), colors.white if dollar_return >= 0 else colors.HexColor("#FEF2F2")),
        # White/light red
        ('BOX', (0, 0), (0, 0), 1, BRAND_PRIMARY),
        ('BOX', (1, 0), (1, 0), 1, BRAND_SUCCESS),
        ('BOX', (2, 0), (2, 0), 1, BRAND_SUCCESS if total_return_percentage >= 0 else BRAND_DANGER),
        ('BOX', (3, 0), (3, 0), 1, BRAND_SUCCESS if dollar_return >= 0 else BRAND_DANGER),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('TOPPADDING', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('LEFTPADDING', (0, 0), (-1, -1), 10),
        ('RIGHTPADDING', (0, 0), (-1, -1), 10),
    ]))

    story.append(summary_table)
    story.append(Spacer(1, 0.3 * inch))

    # Add performance commentary
    performance_comment = """
    Your portfolio has shown <strong>consistent performance</strong> during the reporting period, 
    outperforming the benchmark by 2.3%. Market volatility presented both challenges and 
    opportunities, with our strategic positioning in technology and healthcare sectors 
    contributing significantly to returns.
    """
    story.append(Paragraph(performance_comment, style_normal))

    # Add risk metrics if available
    risk_metrics = report.get('risk_metrics', {})
    if risk_metrics:
        story.append(Spacer(1, 0.2 * inch))
        story.append(Paragraph("RISK METRICS", style_subsection_title))

        risk_data = [
            ["METRIC", "VALUE", "BENCHMARK", "DIFFERENCE"],
            ["Annual Volatility", f"{risk_metrics.get('annual_volatility', 0):.2f}%", "12.5%",
             f"{risk_metrics.get('annual_volatility', 0) - 12.5:+.2f}%"],
            ["Sharpe Ratio", f"{risk_metrics.get('sharpe_ratio', 0):.2f}", "1.1",
             f"{risk_metrics.get('sharpe_ratio', 0) - 1.1:+.2f}"],
            ["Sortino Ratio", f"{risk_metrics.get('sortino_ratio', 0):.2f}", "1.3",
             f"{risk_metrics.get('sortino_ratio', 0) - 1.3:+.2f}"],
            ["Max Drawdown", f"{risk_metrics.get('max_drawdown', 0):.2f}%", "15.2%",
             f"{risk_metrics.get('max_drawdown', 0) - 15.2:+.2f}%"],
        ]

        risk_table = Table(risk_data, colWidths=[1.65 * inch, 1.65 * inch, 1.65 * inch, 1.65 * inch])
        risk_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), BRAND_PRIMARY),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('ALIGN', (1, 1), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, BRAND_TEXT_LIGHT),
            ('BOX', (0, 0), (-1, -1), 1, BRAND_PRIMARY),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#F8F9FA")]),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(risk_table)

    # Add page break after cover/summary
    story.append(PageBreak())

    # Performance Analysis Section
    story.append(Paragraph("PERFORMANCE ANALYSIS", style_section_title))
    story.append(HRFlowable(thickness=1))

    # If we have a performance chart, add it
    if performance_chart_bytes:
        story.append(Spacer(1, 0.1 * inch))
        performance_img = Image(io.BytesIO(performance_chart_bytes))

        # Set a reasonable size for the image
        performance_img.drawHeight = 3 * inch
        performance_img.drawWidth = 6.5 * inch

        story.append(performance_img)
        story.append(Spacer(1, 0.1 * inch))

        # Add a chart caption/description
        chart_caption = """
        <i>The chart above shows the performance of your investments over time. 
        The blue line represents the total portfolio value, while the dotted line represents the benchmark.</i>
        """
        story.append(Paragraph(chart_caption, style_small))
    else:
        # If no chart data, generate a simple chart with dummy data
        story.append(Spacer(1, 0.2 * inch))
        story.append(Paragraph("Portfolio Performance Trend", style_subsection_title))

        # Create a simple line chart
        drawing = Drawing(500, 200)

        chart = HorizontalLineChart()
        chart.x = 20
        chart.y = 20
        chart.width = 460
        chart.height = 150

        # Sample data
        chart.data = [
            [5000, 5100, 5300, 5200, 5400, 5600, 5800, 6000, 6100, 5900, 6200, 6400],  # Portfolio
            [5000, 5050, 5150, 5100, 5250, 5300, 5450, 5550, 5650, 5600, 5700, 5800],  # Benchmark
        ]

        # Labels for x-axis (months)
        chart.categoryAxis.categoryNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        chart.categoryAxis.labels.fontName = 'Helvetica'
        chart.categoryAxis.labels.fontSize = 8

        # Format the y-axis
        chart.valueAxis.valueMin = 4500
        chart.valueAxis.valueMax = 6500
        chart.valueAxis.valueStep = 500
        chart.valueAxis.labels.fontName = 'Helvetica'
        chart.valueAxis.labels.fontSize = 8

        # Format the lines
        chart.lines[0].strokeColor = BRAND_PRIMARY
        chart.lines[0].strokeWidth = 2.5
        chart.lines[0].symbol = makeMarker('Circle')
        chart.lines[0].symbol.fillColor = BRAND_PRIMARY
        chart.lines[0].symbol.size = 5

        chart.lines[1].strokeColor = BRAND_SECONDARY
        chart.lines[1].strokeWidth = 1.5
        chart.lines[1].symbol = makeMarker('Circle')
        chart.lines[1].symbol.fillColor = BRAND_SECONDARY
        chart.lines[1].symbol.size = 4

        # Add a grid
        chart.joinAxis = chart.categoryAxis
        chart.joinAxisMode = 'bottom'
        chart.valueAxis.gridStart = chart.x
        chart.valueAxis.gridEnd = chart.x + chart.width
        chart.valueAxis.gridStrokeColor = colors.lightgrey
        chart.valueAxis.gridStrokeWidth = 0.5

        # Add a title to the chart
        title = Label()
        title.setText("Portfolio vs Benchmark Performance")
        title.setOrigin(chart.x + chart.width / 2, chart.y + chart.height + 20)
        title.fontName = 'Helvetica-Bold'
        title.fontSize = 10
        title.textAnchor = 'middle'
        drawing.add(title)

        # Add chart and legend to the drawing
        drawing.add(chart)

        # Add legend manually
        legend = Group()

        # Portfolio legend item
        legend.add(Line(270, 180, 290, 180, strokeColor=BRAND_PRIMARY, strokeWidth=2.5))
        legend.add(String(295, 177, "Portfolio", fontName="Helvetica", fontSize=8))

        # Benchmark legend item
        legend.add(Line(350, 180, 370, 180, strokeColor=BRAND_SECONDARY, strokeWidth=1.5))
        legend.add(String(375, 177, "Benchmark", fontName="Helvetica", fontSize=8))

        drawing.add(legend)

        story.append(drawing)

    # Portfolio Allocation section
    story.append(Spacer(1, 0.3 * inch))
    story.append(Paragraph("PORTFOLIO ALLOCATION", style_section_title))
    story.append(HRFlowable(thickness=1))

    # If we have allocation chart data
    if allocation_chart_bytes:
        story.append(Spacer(1, 0.1 * inch))
        alloc_img = Image(io.BytesIO(allocation_chart_bytes))

        # Set a reasonable size for the image
        alloc_img.drawHeight = 3 * inch
        alloc_img.drawWidth = 3 * inch

        # Create a table with the chart on the left and allocation details on the right
        allocation_data = report.get('portfolio_allocation', {})

        # Prepare data for allocation table
        alloc_table_data = [["ASSET CLASS", "ALLOCATION", "TARGET", "VARIANCE"]]

        for portfolio, allocation in allocation_data.items():
            target = allocation  # Normally this would be the target allocation
            variance = 0  # Normally this would be allocation - target

            alloc_table_data.append([
                portfolio,
                f"{allocation:.1f}%",
                f"{target:.1f}%",
                f"{variance:+.1f}%"
            ])

        # Create a row with image and table
        image_and_table = [
            [alloc_img,
             Table(alloc_table_data, colWidths=[1.5 * inch, 0.8 * inch, 0.8 * inch, 0.8 * inch])]
        ]

        allocation_table = Table(image_and_table, colWidths=[3.5 * inch, 3.5 * inch])
        allocation_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            # Style for the nested allocation details table
            ('BACKGROUND', (1, 0), (1, 0), BRAND_PRIMARY),
            ('TEXTCOLOR', (1, 0), (1, 0), colors.white),
            ('FONTNAME', (1, 0), (1, 0), 'Helvetica-Bold'),
            ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
        ]))

        story.append(allocation_table)
    else:
        # If no allocation chart, create a pie chart with dummy data
        story.append(Spacer(1, 0.2 * inch))
        story.append(Paragraph("Asset Allocation", style_subsection_title))

        # Create a pie chart
        drawing = Drawing(300, 200)

        pie = Pie()
        pie.x = 150
        pie.y = 100
        pie.width = 120
        pie.height = 120

        # Sample data
        pie.data = [40, 30, 20, 10]
        pie.labels = ['Stocks', 'Bonds', 'Real Estate', 'Cash']

        # Colors
        pie.slices.strokeWidth = 0.5
        pie.slices[0].fillColor = BRAND_PRIMARY
        pie.slices[1].fillColor = BRAND_SECONDARY
        pie.slices[2].fillColor = BRAND_ACCENT
        pie.slices[3].fillColor = BRAND_SUCCESS

        # Add pie to the drawing
        drawing.add(pie)

        story.append(drawing)

        # Add allocation table with dummy data
        allocation_table_data = [
            ["ASSET CLASS", "ALLOCATION", "TARGET", "VARIANCE"],
            ["Stocks", "40.0%", "45.0%", "-5.0%"],
            ["Bonds", "30.0%", "25.0%", "+5.0%"],
            ["Real Estate", "20.0%", "20.0%", "0.0%"],
            ["Cash", "10.0%", "10.0%", "0.0%"]
        ]

        allocation_table = Table(allocation_table_data, colWidths=[1.5 * inch, 1.5 * inch, 1.5 * inch, 1.5 * inch])
        allocation_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), BRAND_PRIMARY),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('ALIGN', (1, 1), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, BRAND_TEXT_LIGHT),
            ('BOX', (0, 0), (-1, -1), 1, BRAND_PRIMARY),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#F8F9FA")]),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(Spacer(1, 0.2 * inch))
        story.append(allocation_table)

    # Add page break before investment details
    story.append(PageBreak())

    # Investment Details Section
    story.append(Paragraph("INVESTMENT DETAILS", style_section_title))
    story.append(HRFlowable(thickness=1))
    story.append(Spacer(1, 0.2 * inch))

    # Get investments data
    investments = report.get('investments', [])

    if investments:
        # Create table for investment details
        investment_table_data = [
            ["PORTFOLIO", "START DATE", "AMOUNT", "CURRENT VALUE", "RETURN", "STATUS"]
        ]

        for inv in investments:
            # Handle different data structures (dict or object)
            if isinstance(inv, dict):
                portfolio_name = inv.get('portfolio_name', '')
                start_date_raw = inv.get('start_date', '-')
                start_date_str = start_date_raw.strftime('%Y-%m-%d') if hasattr(start_date_raw,
                                                                                'strftime') else start_date_raw
                amount = inv.get('amount', 0)
                curr_val = inv.get('current_value', 0)
                ret = inv.get('return', 0)
                is_active = inv.get('is_active', True)
                is_funded = inv.get('is_funded', False)
            else:
                # It's an ORM object
                portfolio_name = inv.portfolio.name
                start_date_str = inv.start_date.strftime('%Y-%m-%d')
                amount = inv.amount
                curr_val = inv.get_current_value()
                ret = inv.get_return()
                is_active = inv.is_active
                is_funded = inv.is_funded

            # Determine status
            if not is_active:
                status = "CLOSED"
                status_color = BRAND_TEXT_LIGHT
            elif not is_funded:
                status = "PENDING"
                status_color = BRAND_WARNING
            else:
                status = "ACTIVE"
                status_color = BRAND_SUCCESS

            # Determine return color
            ret_color = BRAND_SUCCESS if ret >= 0 else BRAND_DANGER

            # Add data row
            investment_table_data.append([
                portfolio_name,
                start_date_str,
                f"${amount:,.2f}",
                f"${curr_val:,.2f}",
                f"{ret:+.2f}%",
                status
            ])

        # Create and style the investments table
        investment_table = Table(investment_table_data,
                                 colWidths=[1.5 * inch, 1 * inch, 1 * inch, 1.25 * inch, 0.75 * inch, 0.75 * inch])

        # Create a list for row-specific styles
        row_styles = []

        # Add styles for each row
        for i, inv in enumerate(investments, 1):
            # Determine if the investment is active
            if isinstance(inv, dict):
                is_active = inv.get('is_active', True)
                is_funded = inv.get('is_funded', False)
                ret = inv.get('return', 0)
            else:
                is_active = inv.is_active
                is_funded = inv.is_funded
                ret = inv.get_return()

            # Determine status color
            if not is_active:
                status_color = BRAND_TEXT_LIGHT
            elif not is_funded:
                status_color = BRAND_WARNING
            else:
                status_color = BRAND_SUCCESS

            # Determine return color
            ret_color = BRAND_SUCCESS if ret >= 0 else BRAND_DANGER

            # Add text color for return column
            row_styles.append(('TEXTCOLOR', (4, i), (4, i), ret_color))

            # Add text color for status column
            row_styles.append(('TEXTCOLOR', (5, i), (5, i), status_color))

            # Add font weight for status
            row_styles.append(('FONTNAME', (5, i), (5, i), 'Helvetica-Bold'))

        # Apply all styles to the table
        investment_table.setStyle(TableStyle([
            # Header styles
            ('BACKGROUND', (0, 0), (-1, 0), BRAND_PRIMARY),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

            # Data row styles
            ('ALIGN', (2, 1), (4, -1), 'RIGHT'),  # Right align amount, value, return
            ('ALIGN', (5, 1), (5, -1), 'CENTER'),  # Center align status

            # General table styles
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, BRAND_TEXT_LIGHT),
            ('BOX', (0, 0), (-1, -1), 1, BRAND_PRIMARY),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#F8F9FA")]),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),

            # Row-specific styles added from loop
            *row_styles
        ]))

        story.append(investment_table)
    else:
        story.append(Paragraph("No investment data available for the selected period.", style_normal))

    # Market Commentary Section
    story.append(Spacer(1, 0.3 * inch))
    story.append(Paragraph("MARKET COMMENTARY & OUTLOOK", style_section_title))
    story.append(HRFlowable(thickness=1))
    story.append(Spacer(1, 0.2 * inch))

    market_commentary = """
    <b>Current Market Environment</b><br/>
    The global markets have shown resilience despite ongoing economic uncertainties. Inflation pressures 
    continue to moderate, allowing central banks to take a more measured approach to monetary policy. 
    Corporate earnings have generally exceeded expectations, providing support for equity valuations.
    <br/><br/>
    <b>Sector Performance</b><br/>
    Technology and healthcare sectors have been standout performers, driven by innovation and 
    demographic trends. Financial services have stabilized after a challenging period, while 
    consumer discretionary shows sensitivity to interest rate movements.
    <br/><br/>
    <b>Forward Outlook</b><br/>
    Our investment committee maintains a cautiously optimistic view for the coming quarter. We 
    anticipate moderation in growth but not recession, with inflation continuing to trend toward 
    central bank targets. We favor high-quality companies with strong balance sheets, sustainable 
    cash flows, and competitive market positions. We have slightly increased our allocation to 
    alternative investments as a diversification strategy.
    """

    story.append(Paragraph(market_commentary, style_normal))

    # Disclaimers and Notes Section
    story.append(Spacer(1, 0.5 * inch))
    story.append(Paragraph("IMPORTANT NOTES & DISCLAIMERS", style_subsection_title))

    disclaimers = """
    <b>Past Performance:</b> Past performance is not indicative of future results. Investment returns 
    and principal value will fluctuate so that an investor's shares, when redeemed, may be worth more 
    or less than their original cost.
    <br/><br/>
    <b>Risk Considerations:</b> All investments involve risk, including loss of principal. Investments 
    in foreign securities involve special risks including currency fluctuations, economic instability, 
    and political developments.
    <br/><br/>
    <b>This Report:</b> This report is for informational purposes only and should not be considered a 
    recommendation to buy or sell any security. Please consult with your financial advisor before 
    making any investment decisions.
    """

    story.append(Paragraph(disclaimers, style_small))

    # Instead of using a custom canvas maker, use page callbacks
    # Instead of using a custom canvas maker, use page callbacks
    def add_header_footer(canvas, doc):
        # Save the state of our canvas so we can restore it later
        canvas.saveState()

        # Header
        # Logo placeholder
        canvas.setFillColor(BRAND_PRIMARY)
        canvas.rect(30, doc.height + doc.topMargin - 20, 20, 20, fill=1)
        canvas.setFillColor(BRAND_SECONDARY)
        canvas.rect(50, doc.height + doc.topMargin - 20, 10, 20, fill=1)

        # Company name
        canvas.setFont("Helvetica-Bold", 16)
        canvas.setFillColor(BRAND_PRIMARY)
        canvas.drawString(70, doc.height + doc.topMargin - 0, "KEHEILAN FUND")

        # Report name
        canvas.setFont("Helvetica", 12)
        canvas.setFillColor(BRAND_TEXT)
        canvas.drawString(70, doc.height + doc.topMargin - 16, "Investment Portfolio Report")

        # Client name on the right
        canvas.setFont("Helvetica", 10)
        canvas.setFillColor(BRAND_TEXT_LIGHT)
        client_text = f"PREPARED FOR: {client_info.get('name', 'Valued Client')}"
        canvas.drawRightString(doc.width + doc.leftMargin - 30, doc.height + doc.topMargin - 0, client_text)

        # Date on the right
        date_text = f"REPORT DATE: {datetime.now().strftime('%B %d, %Y')}"
        canvas.drawRightString(doc.width + doc.leftMargin - 30, doc.height + doc.topMargin - 16, date_text)

        # Header line
        canvas.setStrokeColor(BRAND_PRIMARY)
        canvas.setLineWidth(1.5)
        canvas.line(30, doc.height + doc.topMargin - 30,
                    doc.width + doc.leftMargin - 30, doc.height + doc.topMargin - 30)

        # Footer
        # Page number
        canvas.setFont("Helvetica", 8)
        canvas.setFillColor(BRAND_TEXT_LIGHT)
        page_text = f"Page {doc.page} of {doc.page}"  # We don't know total pages yet
        canvas.drawRightString(doc.width + doc.leftMargin - 30, 40, page_text)

        # Disclaimer
        canvas.setFont("Helvetica-Oblique", 7)
        disclaimer = "Past performance is not indicative of future results. Investments may lose value."
        canvas.drawString(30, 40, disclaimer)

        # Footer line
        canvas.setStrokeColor(BRAND_LIGHT_BG)
        canvas.setLineWidth(0.5)
        canvas.line(30, 50, doc.width + doc.leftMargin - 30, 50)

        # Restore canvas state
        canvas.restoreState()

    # Build the PDF using standard callback approach
    doc.build(story, onFirstPage=add_header_footer, onLaterPages=add_header_footer)

    # Return the PDF bytes
    pdf_bytes = buffer.getvalue()
    buffer.close()

    return pdf_bytes
