# utils/market_data.py
import yfinance as yf


def get_market_summary():
    """
    Fetches real-time market data for selected indices and rates.

    Returns:
        dict: A dictionary containing price and percent change for each market.
    """
    summary = {}

    # Fetch S&P 500 data. Ticker symbol ^GSPC is used by Yahoo Finance.
    sp500 = yf.Ticker("^GSPC").info
    summary['S&P 500'] = {
        'price': sp500.get('regularMarketPrice', 0),
        'change_pct': sp500.get('regularMarketChangePercent', 0)
    }

    # Fetch NASDAQ Composite data. Use ^IXIC ticker.
    nasdaq = yf.Ticker("^IXIC").info
    summary['NASDAQ'] = {
        'price': nasdaq.get('regularMarketPrice', 0),
        'change_pct': nasdaq.get('regularMarketChangePercent', 0)
    }

    # Fetch Dow Jones Industrial Average data. Use ^DJI ticker.
    dow = yf.Ticker("^DJI").info
    summary['DOW'] = {
        'price': dow.get('regularMarketPrice', 0),
        'change_pct': dow.get('regularMarketChangePercent', 0)
    }

    # Fetch 10-Year Treasury Yield (commonly used ^TNX for approximate yield, value in percent)
    tnx = yf.Ticker("^TNX").info
    # Note: ^TNX returns the yield as a number, not a percentage change in the same format.
    summary['10-YR'] = {
        'price': tnx.get('regularMarketPrice', 0) / 100,  # Divide by 100 to convert to percent if necessary
        # For demonstration, we assume no percent change; you can add logic if your data includes change.
        'change_pct': 0
    }

    # Fetch USD/EUR exchange rate using the pair EURUSD=X (Yahoo Finance returns price as USD per EUR)
    usd_eur = yf.Ticker("EURUSD=X").info
    summary['USD/EUR'] = {
        'price': usd_eur.get('regularMarketPrice', 0),
        'change_pct': usd_eur.get('regularMarketChangePercent', 0)
    }

    return summary
