# In something like utils/chart_builders.py

import io
import random
import matplotlib.pyplot as plt

def build_allocation_chart(portfolios):
    """
    A dummy example of creating a pie chart in Matplotlib
    from a 'portfolios' list of dict with 'name', 'amount_invested'.
    Returns raw PNG bytes.
    """
    labels = [p['name'] for p in portfolios]
    sizes = [p['amount_invested'] for p in portfolios]

    fig, ax = plt.subplots()
    ax.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=140)
    ax.axis('equal')  # Equal aspect ratio ensures a perfect circle

    png_image = io.BytesIO()
    plt.savefig(png_image, format='png', bbox_inches='tight')
    plt.close(fig)
    png_image.seek(0)
    return png_image.read()
