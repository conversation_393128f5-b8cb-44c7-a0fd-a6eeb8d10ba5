from flask import make_response, current_app
from datetime import datetime, date
import types  # For Ellipsis type checking


def convert_to_datetime(d):
    """Convert a date or datetime to datetime"""
    if isinstance(d, date) and not isinstance(d, datetime):
        return datetime.combine(d, datetime.min.time())
    return d


def generate_fancy_customer_report(customer_id, start_date=None, end_date=None):
    """
    Generate a fancy PDF report for a customer

    Args:
        customer_id (int): The customer ID
        start_date (datetime or date, optional): Start date for report period
        end_date (datetime or date, optional): End date for report period

    Returns:
        bytes: The PDF file content as raw bytes
    """
    # Fix import to match your project structure
    from models.models_customer import Customer  # Changed from models.customer
    from models.investment import Investment
    from models.portfolio import Portfolio
    from models.performance import Performance
    from utils.report_generator import generate_customer_report
    from utils.enhanced_pdf_generator import generate_enhanced_pdf_report
    from utils.allocation_charts import (
        generate_allocation_chart_as_bytes,
        generate_performance_chart_as_bytes,
        generate_comparison_chart_as_bytes,
        generate_returns_heatmap_as_bytes
    )

    # Set default dates if not provided
    if not start_date:
        start_date = datetime(datetime.now().year, 1, 1)  # Start of current year
    if not end_date:
        end_date = datetime.now()

    # Convert dates to datetime objects for comparison
    start_date = convert_to_datetime(start_date)
    end_date = convert_to_datetime(end_date)

    current_app.logger.info(f"Generating report for customer {customer_id} from {start_date} to {end_date}")

    # Get customer information
    customer = Customer.query.get(customer_id)
    if not customer:
        current_app.logger.error(f"Customer ID {customer_id} not found")
        return None

    # Get the base report data
    report = generate_customer_report(customer_id, end_date)

    # Get all investments for this customer
    investments = Investment.query.filter_by(customer_id=customer_id).all()

    # Prepare client info
    client_info = {
        'id': f"ACCOUNT-{customer_id:06d}",
        'name': customer.name,
        'email': customer.email
    }

    # Generate performance chart data
    performance_dates = []
    performance_values = []
    benchmark_values = []

    # Get all investments and their performances
    active_investments = [inv for inv in investments if inv.is_active and inv.is_funded]

    # Create a combined timeline of performance data
    all_dates = set()
    for inv in active_investments:
        perfs = Performance.query.filter_by(investment_id=inv.id).order_by(Performance.date).all()
        for p in perfs:
            # Convert p.date to datetime if it's a date object
            perf_date = convert_to_datetime(p.date)
            if start_date <= perf_date <= end_date:
                all_dates.add(perf_date)

    # Sort the dates
    all_dates = sorted(all_dates)

    # For each date, get the total value across all investments
    for date_point in all_dates:
        performance_dates.append(date_point)

        # Calculate total value on this date
        total_value = 0
        for inv in active_investments:
            # We need to handle both date and datetime in the query
            if isinstance(date_point, datetime):
                # If we have datetime objects in all_dates, we might need to query by date portion
                date_only = date_point.date()
                perf = Performance.query.filter_by(investment_id=inv.id).filter(
                    Performance.date == date_point
                ).first() or Performance.query.filter_by(investment_id=inv.id).filter(
                    Performance.date == date_only
                ).first()
            else:
                perf = Performance.query.filter_by(investment_id=inv.id, date=date_point).first()

            if perf:
                total_value += perf.value

        performance_values.append(total_value)

        # Create a simple benchmark (e.g., 5% annual growth from initial value)
        if len(performance_values) > 0:
            days_since_start = (date_point - all_dates[0]).days
            annual_growth_rate = 0.05  # 5% annually
            daily_growth_rate = (1 + annual_growth_rate) ** (1 / 365) - 1
            benchmark_value = performance_values[0] * (1 + daily_growth_rate) ** days_since_start
            benchmark_values.append(benchmark_value)

    # Generate performance chart if we have data
    if performance_dates and performance_values:
        performance_chart_bytes = generate_performance_chart_as_bytes(
            dates=performance_dates,
            values=performance_values,
            benchmark_values=benchmark_values,
            title="Portfolio Value Over Time"
        )
    else:
        performance_chart_bytes = None

    # Generate allocation chart
    portfolio_allocation = report.get('portfolio_allocation', {})

    if portfolio_allocation:
        allocation_data = {
            'labels': list(portfolio_allocation.keys()),
            'values': list(portfolio_allocation.values())
        }
        allocation_chart_bytes = generate_allocation_chart_as_bytes(allocation_data)
    else:
        allocation_chart_bytes = None

    # Generate comparison chart (sample data if real data not available)
    comparison_labels = ['1M', '3M', '6M', 'YTD', '1Y', '3Y']
    portfolio_returns = [1.2, 3.5, 7.8, 5.6, 12.3, 28.5]
    benchmark_returns = [0.8, 2.9, 6.5, 4.2, 10.1, 25.2]

    comparison_chart_bytes = generate_comparison_chart_as_bytes(
        labels=comparison_labels,
        portfolio_returns=portfolio_returns,
        benchmark_returns=benchmark_returns,
        title="Portfolio vs Benchmark Performance"
    )

    # Generate returns heatmap (sample data if real data not available)
    returns_data = {
        '2022': [-0.5, 1.2, -2.1, 3.5, -1.8, 0.7, 2.9, 1.1, -3.2, 4.5, 2.2, -1.5],
        '2023': [2.1, 1.8, -0.6, 3.2, 2.5, 1.1, 4.2, -0.8, 2.7, -1.2, 3.6, 2.0],
        '2024': [1.5, 2.2, 3.1, -1.2, 2.5, 1.9, 0.8, 3.3, 2.1, 0.5, 0.0, 0.0],  # Last months might be empty
    }

    returns_heatmap_bytes = generate_returns_heatmap_as_bytes(
        returns_data=returns_data,
        title="Monthly Returns (%) Heatmap"
    )

    # Enhanced report data
    enhanced_report = {
        **report,
        'comparison_chart_bytes': comparison_chart_bytes,
        'returns_heatmap_bytes': returns_heatmap_bytes
    }

    # Generate the enhanced PDF report
    pdf_bytes = generate_enhanced_pdf_report(
        report=enhanced_report,
        client_info=client_info,
        start_date=start_date,
        end_date=end_date,
        performance_chart_bytes=performance_chart_bytes,
        allocation_chart_bytes=allocation_chart_bytes
    )

    return pdf_bytes


def fancy_pdf_report_route(customer_id=None):
    """
    Route handler for generating and serving a fancy PDF report

    Args:
        customer_id (int, optional): The customer ID. If not provided, uses current_user.id

    Returns:
        Response: Flask response with PDF attachment
    """
    from flask import current_app
    from flask_login import current_user
    from datetime import datetime

    # Use provided customer_id or fall back to current user
    if customer_id is None or customer_id is Ellipsis:
        customer_id = current_user.id

    # Ensure customer_id is a valid integer
    try:
        customer_id = int(customer_id)
    except (TypeError, ValueError):
        current_app.logger.error(f"Invalid customer_id: {customer_id}")
        return make_response("Invalid customer ID", 400)

    # Default dates
    start_date = datetime(datetime.now().year, 1, 1)  # Start of current year
    end_date = datetime.now()

    try:
        # Generate the fancy PDF report
        pdf_bytes = generate_fancy_customer_report(
            customer_id=customer_id,
            start_date=start_date,
            end_date=end_date
        )

        if not pdf_bytes:
            current_app.logger.error(f"Failed to generate PDF for customer {customer_id}")
            return make_response("Failed to generate report", 500)

        # Create response with PDF bytes
        response = make_response(pdf_bytes)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers[
            'Content-Disposition'] = f'attachment; filename=Investment_Report_{datetime.now().strftime("%Y%m%d")}.pdf'

        return response

    except Exception as e:
        current_app.logger.error(f"Error generating PDF: {str(e)}")
        return make_response(f"Error generating report: {str(e)}", 500)