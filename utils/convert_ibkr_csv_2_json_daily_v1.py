import csv
import json
from datetime import datetime
from pathlib import Path


def convert_ibkr_activity_csv_to_json(file_path: str, output_path: str = None):
    """
    Parses an IBKR Activity Statement CSV where each line has this general form:
       Section,RecordType,<columns...>
    Example sections: "Statement", "Account Information", "Net Asset Value", etc.
    Example record types: "Header", "Data", possibly "SubTotal", "Total", etc.

    We create a JSON with a top-level dictionary 'sections', whose keys are the
    names from column 0 (e.g. "Account Information", "Net Asset Value"). Within each
    section, we store a list of 'tables'. Each table is introduced by a line whose
    RecordType is 'Header' and holds the column headers. The subsequent lines with
    RecordType == 'Data' (or anything other than 'Header') are placed into that table
    as rows.

    If the same section has multiple 'Header' lines, we assume multiple sub-tables.

    By doing this, we capture all lines, even if IBKR changes or expands the format.
    """

    # Prepare a top-level JSON structure
    report_data = {
        "metadata": {
            "report_date": datetime.now().strftime("%Y-%m-%d"),
            "file_name": Path(file_path).name,
            "source": "Interactive Brokers"
        },
        # We'll store everything else in sections
        "sections": {}
    }

    with open(file_path, mode='r', encoding='utf-8-sig', newline='') as csv_file:
        reader = csv.reader(csv_file)
        # The IBKR activity statement often has a BOM; we use 'utf-8-sig' to handle that

        # For each section, we keep a list of tables. Each table has:
        #   { "headers": [...], "rows": [ {...}, {...} ] }
        # We'll track the *current* table for each section as we parse lines.
        # If we encounter a new "Header" line, we start a new table in that section.
        current_tables = {}  # maps section_name -> [list_of_tables]

        for row in reader:
            if not row:
                continue

            section = row[0].strip()
            record_type = row[1].strip() if len(row) > 1 else ""

            # The rest of the columns after the first two are what we'll parse differently
            content = row[2:] if len(row) > 2 else []

            # Make sure there's a place in the dictionary for this section
            if section not in current_tables:
                current_tables[section] = []

            if record_type == "Header":
                # Start a new "table" under this section
                new_table = {
                    "headers": content,  # we'll store these directly as a list
                    "rows": []
                }
                current_tables[section].append(new_table)

            else:
                # This is typically "Data", "Total", "SubTotal", etc. We treat them all
                # as row data lines, belonging to the last table in this section
                if not current_tables[section]:
                    # If there's no table started yet, let's create a placeholder
                    # with no headers. We'll handle it anyway so we don't lose data.
                    current_tables[section].append({
                        "headers": [],
                        "rows": []
                    })

                # The "active" table is the last one in current_tables[section]
                active_table = current_tables[section][-1]

                # Build a dict from content. If we have fewer columns than headers, some
                # fields may remain empty. If we have more columns than headers, we'll
                # store them in "extra_col_X".
                row_dict = {}
                num_headers = len(active_table["headers"])
                for i, val in enumerate(content):
                    if i < num_headers and active_table["headers"][i]:
                        col_name = active_table["headers"][i]
                        row_dict[col_name] = maybe_to_float(row_dict[col_name])
                    else:
                        # If there's no header for this column, store it in an "extra" key
                        row_dict[f"extra_col_{i}"] = val.strip()

                # We might also want to store record_type in each row, just to keep it
                # so we know if it was "Data", "SubTotal", "Total", etc.
                row_dict["_record_type"] = record_type

                # Add the row to the table
                active_table["rows"].append(row_dict)

        # Move all the tables into our final structure
        # We'll store them under: report_data["sections"][<section_name>]["tables"] = [...]
        for section_name, tables in current_tables.items():
            report_data["sections"][section_name] = {
                "tables": tables
            }

    # Finally, write out the JSON if an output_path is given
    if output_path:
        with open(output_path, 'w', encoding='utf-8') as out:
            json.dump(report_data, out, indent=4)
            print(f"[INFO] Wrote JSON to {output_path}")

    return report_data

def maybe_to_float(value):
    """
    Try to parse the string 'value' as a float. If impossible, return the original string.
    Removes commas, so e.g. '1,234.56' -> 1234.56
    """
    if not value or not isinstance(value, str):
        return value
    candidate = value.replace(",", "").strip()
    if candidate in ("", "--"):
        # Example: some IBKR fields use "--" to indicate no value
        return value
    try:
        return float(candidate)
    except ValueError:
        return value

def convert_ibkr_activity_csv_to_json_enhanced(file_path: str, output_path: str = None):
    """
    A variant of your CSV-to-JSON parser that:
      1) Preserves the same overall 'sections' -> 'tables' -> 'rows' structure
      2) Automatically tries to parse numeric fields into floats
      3) Optionally splits data rows from 'Subtotal/Total' rows
    """
    report_data = {
        "metadata": {
            "report_date": datetime.now().strftime("%Y-%m-%d"),
            "file_name": Path(file_path).name,
            "source": "Interactive Brokers"
        },
        "sections": {}
    }

    with open(file_path, mode='r', encoding='utf-8-sig', newline='') as csv_file:
        reader = csv.reader(csv_file)
        current_tables = {}  # section_name -> [list of tables]

        for row in reader:
            if not row:
                continue

            section = row[0].strip()
            record_type = row[1].strip() if len(row) > 1 else ""
            content = row[2:] if len(row) > 2 else []

            if section not in current_tables:
                current_tables[section] = []

            if record_type == "Header":
                # Create a new table
                new_table = {
                    "headers": content,
                    "rows": [],
                    # We'll store subtotals/totals separate if we want
                    "totals": []
                }
                current_tables[section].append(new_table)

            else:
                # This is typically Data, SubTotal, or Total, etc.
                if not current_tables[section]:
                    # If there's no table started, create one
                    current_tables[section].append({
                        "headers": [],
                        "rows": [],
                        "totals": []
                    })
                active_table = current_tables[section][-1]

                # Build row dictionary
                row_dict = {}
                for i, val in enumerate(content):
                    if i < len(active_table["headers"]):
                        col_name = active_table["headers"][i]
                    else:
                        col_name = f"extra_col_{i}"

                    # Convert to float if possible
                    row_dict[col_name] = maybe_to_float(val)

                # Add the record_type for reference
                row_dict["_record_type"] = record_type

                # If you'd like to treat SubTotal/Total differently
                if record_type in ("SubTotal", "Total"):
                    active_table["totals"].append(row_dict)
                else:
                    active_table["rows"].append(row_dict)

        # Move tables into final structure
        for section_name, tables in current_tables.items():
            report_data["sections"][section_name] = {
                "tables": tables
            }

    # Write to JSON if requested
    if output_path:
        with open(output_path, 'w', encoding='utf-8') as out:
            json.dump(report_data, out, indent=4)
            print(f"[INFO] Enhanced JSON saved to {output_path}")

    return report_data

def main():
    input_file = 'U6454299_20250305.csv'
    output_file = 'U6454299_20250305.json'

    data = convert_ibkr_activity_csv_to_json_enhanced(input_file, output_file)
    print("Conversion complete. Check:", output_file)


if __name__ == "__main__":
    main()
