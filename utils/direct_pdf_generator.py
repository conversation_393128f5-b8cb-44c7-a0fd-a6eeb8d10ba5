import io
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak


# Brand colors
BRAND_PRIMARY = colors.HexColor("#055A57")
BRAND_SECONDARY = colors.HexColor("#00917C")
# BRAND_SUCCESS = colors.HexColor("#43a047")
# BRAND_DANGER = colors.HexColor("#e53935")


def generate_direct_pdf(report, client_info, start_date, end_date):
    """Generate a fancy PDF without any complex components that might cause errors"""
    buffer = io.BytesIO()

    # Create document
    doc = SimpleDocTemplate(
        buffer,
        pagesize=letter,
        leftMargin=0.75 * inch,
        rightMargin=0.75 * inch,
        topMargin=0.75 * inch,
        bottomMargin=0.75 * inch,
        title=f"Investment Report - {client_info.get('name', 'Client')}"
    )

    # Styles
    styles = getSampleStyleSheet()

    style_title = ParagraphStyle(
        name="Title",
        parent=styles['Title'],
        fontName='Helvetica-Bold',
        fontSize=20,
        textColor=BRAND_PRIMARY,
        spaceAfter=0.3 * inch,
        alignment=TA_LEFT,
    )

    style_heading = ParagraphStyle(
        name="Heading",
        parent=styles['Heading1'],
        fontName='Helvetica-Bold',
        fontSize=16,
        textColor=BRAND_SECONDARY,
        spaceBefore=0.2 * inch,
        spaceAfter=0.1 * inch,
    )

    style_normal = ParagraphStyle(
        name="Normal",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=10,
        leading=14,
    )

    # Content
    story = []

    # Header
    story.append(Paragraph(f"KEHEILAN FUND", style_title))
    story.append(Paragraph(f"Investment Portfolio Report", style_heading))
    story.append(Paragraph(f"Prepared for: {client_info.get('name', 'Valued Client')}", style_normal))
    story.append(Paragraph(f"Report Period: {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}",
                           style_normal))
    story.append(Spacer(1, 0.25 * inch))

    # Summary section
    story.append(Paragraph("PORTFOLIO SUMMARY", style_heading))

    total_invested = report.get('total_invested', 0)
    total_current_value = report.get('total_current_value', 0)
    total_return_percentage = report.get('total_return_percentage', 0)

    summary_data = [
        ["METRIC", "VALUE"],
        ["Total Invested", f"${total_invested:,.2f}"],
        ["Current Value", f"${total_current_value:,.2f}"],
        ["Total Return", f"{total_return_percentage:+.2f}%"]
    ]

    summary_table = Table(summary_data, colWidths=[2.5 * inch, 2.5 * inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), BRAND_PRIMARY),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#f5f5f5")]),
    ]))

    story.append(summary_table)
    story.append(Spacer(1, 0.25 * inch))

    # Portfolio Allocation section
    story.append(Paragraph("PORTFOLIO ALLOCATION", style_heading))

    # Use text instead of charts
    portfolio_allocation = report.get('portfolio_allocation', {})

    if portfolio_allocation:
        allocation_data = [["PORTFOLIO", "ALLOCATION"]]
        for portfolio, allocation in portfolio_allocation.items():
            allocation_data.append([portfolio, f"{allocation:.2f}%"])

        allocation_table = Table(allocation_data, colWidths=[3 * inch, 2 * inch])
        allocation_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), BRAND_PRIMARY),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#f5f5f5")]),
        ]))

        story.append(allocation_table)
    else:
        story.append(Paragraph("No allocation data available", style_normal))

    story.append(Spacer(1, 0.25 * inch))

    # Investment Details section
    story.append(Paragraph("INVESTMENT DETAILS", style_heading))

    investments = report.get('investments', [])

    if investments:
        investment_data = [["PORTFOLIO", "START DATE", "AMOUNT", "CURRENT VALUE", "RETURN", "STATUS"]]

        for inv in investments:
            try:
                # Handle different data structures (dict or object)
                if isinstance(inv, dict):
                    portfolio_name = inv.get('portfolio_name', '')
                    start_date_raw = inv.get('start_date', '-')
                    start_date_str = start_date_raw.strftime('%Y-%m-%d') if hasattr(start_date_raw,
                                                                                    'strftime') else str(start_date_raw)
                    amount = inv.get('amount', 0)
                    curr_val = inv.get('current_value', 0)
                    ret = inv.get('return', 0)
                    is_active = inv.get('is_active', True)
                    is_funded = inv.get('is_funded', False)
                else:
                    # It's an ORM object
                    portfolio_name = inv.portfolio.name
                    start_date_str = inv.start_date.strftime('%Y-%m-%d')
                    amount = inv.amount
                    curr_val = inv.get_current_value()
                    ret = inv.get_return()
                    is_active = inv.is_active
                    is_funded = inv.is_funded

                # Determine status
                if not is_active:
                    status = "CLOSED"
                elif not is_funded:
                    status = "PENDING"
                else:
                    status = "ACTIVE"

                # Add row
                investment_data.append([
                    portfolio_name,
                    start_date_str,
                    f"${amount:,.2f}",
                    f"${curr_val:,.2f}",
                    f"{ret:+.2f}%",
                    status
                ])
            except Exception as e:
                # Skip problematic investments
                continue

        if len(investment_data) > 1:  # If we have data rows beyond the header
            investment_table = Table(investment_data)
            investment_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), BRAND_PRIMARY),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('ALIGN', (2, 1), (4, -1), 'RIGHT'),  # Right align numeric columns
                ('ALIGN', (5, 1), (5, -1), 'CENTER'),  # Center status column
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#f5f5f5")])
            ]))

            story.append(investment_table)
        else:
            story.append(Paragraph("No investment data could be processed", style_normal))
    else:
        story.append(Paragraph("No investment data available", style_normal))

    # Disclaimer
    story.append(Spacer(1, 0.5 * inch))
    story.append(Paragraph("DISCLAIMER", style_heading))
    story.append(Paragraph(
        "Past performance is not indicative of future results. Investment returns and principal value will fluctuate so that an investor's shares, when redeemed, may be worth more or less than their original cost.",
        style_normal))

    # Adding DoD report page
    story.append(PageBreak())
    # Build the document with simple direct approach
    doc.build(story)

    # Return the PDF bytes
    pdf_bytes = buffer.getvalue()
    buffer.close()

    return pdf_bytes