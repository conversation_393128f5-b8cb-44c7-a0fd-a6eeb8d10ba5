# utils/performance_metrics.py

import numpy as np
import numpy_financial as npf  # install via: pip install numpy_financial
import pandas as pd
from math import sqrt
from datetime import datetime, timedelta

def compute_daily_returns(dates, values):
    """
    Given lists of dates (in chronological order) and corresponding portfolio values,
    calculate daily returns as (value_today/value_yesterday - 1).

    Parameters:
        dates (list of datetime): Sorted list of dates.
        values (list or np.array of float): Portfolio snapshot values.
    Returns:
        np.array: Daily returns (as decimals, e.g., 0.02 for 2%).
    """
    values = np.array(values)
    returns = values[1:] / values[:-1] - 1.0
    return returns

def compute_volatility(daily_returns, trading_days=252):
    """
    Computes annualized volatility given daily returns.

    Parameters:
        daily_returns (np.array): Daily returns (decimals).
        trading_days (int): Number of trading days per year (default 252).
    Returns:
        float: Annualized volatility.
    """
    daily_std = np.std(daily_returns, ddof=1)
    annual_vol = daily_std * sqrt(trading_days)
    return annual_vol

def compute_sharpe_ratio(daily_returns, risk_free_rate=0.0, trading_days=252):
    """
    Computes the annualized Sharpe ratio.

    Parameters:
        daily_returns (np.array): Daily returns (decimals).
        risk_free_rate (float): Annual risk-free rate as a decimal (e.g., 0.02 for 2%).
        trading_days (int): Trading days per year (default 252).
    Returns:
        float: Annualized Sharpe ratio.
    """
    daily_rf = risk_free_rate / trading_days
    excess_returns = daily_returns - daily_rf
    mean_excess = np.mean(excess_returns)
    std_excess = np.std(excess_returns, ddof=1)
    if std_excess == 0:
        return 0.0
    sharpe = mean_excess / std_excess * sqrt(trading_days)
    return sharpe

def compute_sortino_ratio(daily_returns, risk_free_rate=0.0, trading_days=252):
    """
    Computes the annualized Sortino ratio using only downside risk.

    Parameters:
        daily_returns (np.array): Daily returns (decimals).
        risk_free_rate (float): Annual risk-free rate (decimal).
        trading_days (int): Trading days per year.
    Returns:
        float: Annualized Sortino ratio.
    """
    daily_rf = risk_free_rate / trading_days
    excess_returns = daily_returns - daily_rf
    downside_returns = excess_returns[excess_returns < 0]
    if len(downside_returns) == 0:
        return float('inf')
    downside_std = np.std(downside_returns, ddof=1)
    mean_excess = np.mean(excess_returns)
    sortino = mean_excess / downside_std * sqrt(trading_days)
    return sortino

def compute_max_drawdown(values):
    """
    Computes the maximum drawdown from a time series of portfolio values.

    Parameters:
        values (list or np.array of float): Portfolio values in chronological order.
    Returns:
        float: Maximum drawdown as a decimal (e.g., 0.15 for 15% drawdown).
    """
    values = np.array(values)
    peak = values[0]
    max_dd = 0.0
    for v in values:
        if v > peak:
            peak = v
        dd = (peak - v) / peak
        if dd > max_dd:
            max_dd = dd
    return max_dd

def compute_beta_alpha(portfolio_returns, benchmark_returns, trading_days=252):
    """
    Computes beta and annualized alpha of portfolio returns relative to benchmark returns.

    Parameters:
        portfolio_returns (np.array): Daily portfolio returns (decimals).
        benchmark_returns (np.array): Daily benchmark returns (decimals).
        trading_days (int): Trading days per year.
    Returns:
        tuple: (beta, annual_alpha), where annual_alpha is annualized.
    """
    portfolio_returns = np.array(portfolio_returns)
    benchmark_returns = np.array(benchmark_returns)
    if np.std(benchmark_returns, ddof=1) == 0:
        return 0.0, 0.0
    covariance = np.cov(portfolio_returns, benchmark_returns, ddof=1)[0][1]
    variance = np.var(benchmark_returns, ddof=1)
    beta = covariance / variance
    daily_alpha = np.mean(portfolio_returns) - beta * np.mean(benchmark_returns)
    annual_alpha = daily_alpha * trading_days
    return beta, annual_alpha

def compute_tracking_error(portfolio_returns, benchmark_returns):
    """
    Computes the tracking error (standard deviation of the difference between portfolio and benchmark returns).

    Parameters:
        portfolio_returns (np.array): Daily portfolio returns (decimals).
        benchmark_returns (np.array): Daily benchmark returns (decimals).
    Returns:
        float: Tracking error.
    """
    portfolio_returns = np.array(portfolio_returns)
    benchmark_returns = np.array(benchmark_returns)
    diff = portfolio_returns - benchmark_returns
    tracking_error = np.std(diff, ddof=1)
    return tracking_error

def compute_mwr(cash_flows, dates, trading_days=252):
    """
    Computes the Money-Weighted Return (MWR), which is equivalent to the Internal Rate of Return (IRR).
    Cash flows should be provided as a list where investments are negative (outflows) and withdrawals
    plus the final portfolio value are positive (inflows). The IRR is then annualized.

    Parameters:
        cash_flows (list of float): Cash flows in chronological order.
        dates (list of datetime): Corresponding dates for each cash flow.
        trading_days (int): Trading days per year (used to annualize the IRR).
    Returns:
        float: Annualized MWR as a decimal.
    """
    # Create an array of year fractions for each cash flow relative to the first date.
    t0 = dates[0]
    year_fractions = np.array([(d - t0).days / 365.0 for d in dates])
    # To use numpy_financial.irr we need equally spaced periods.
    # As an approximation, we adjust the IRR computed for periodic cash flows.
    irr_periodic = npf.irr(cash_flows)
    # Annualize the periodic IRR (assuming periods are in years)
    annual_mwr = (1 + irr_periodic) ** (1/1.0) - 1
    return annual_mwr

def compute_twr(values):
    """
    Computes the Time-Weighted Return (TWR) from a series of portfolio values.
    TWR is computed by chaining the daily (or period) returns.

    Parameters:
        values (list or np.array of float): Portfolio values in chronological order.
    Returns:
        float: TWR as a decimal.
    """
    values = np.array(values)
    # Compute period returns (e.g., daily if values are daily)
    period_returns = values[1:] / values[:-1] - 1.0
    twr = np.prod(1 + period_returns) - 1.0
    return twr

if __name__ == "__main__":
    # --- Quick tests of the above functions ---

    # Example portfolio snapshot values over 5 days:
    dates = [datetime(2025, 4, 8) + timedelta(days=i) for i in range(5)]
    values = [1000000.0, 1050000.0, 1100000.0, 1150000.0, 1200000.0]

    daily_returns = compute_daily_returns(dates, values)
    print("Daily Returns:", daily_returns)

    vol = compute_volatility(daily_returns)
    print("Annualized Volatility:", vol)

    sharpe = compute_sharpe_ratio(daily_returns, risk_free_rate=0.02)
    print("Sharpe Ratio:", sharpe)

    sortino = compute_sortino_ratio(daily_returns, risk_free_rate=0.02)
    print("Sortino Ratio:", sortino)

    mdd = compute_max_drawdown(values)
    print("Maximum Drawdown:", mdd)

    # Example benchmark returns (95% of portfolio returns as a simple example)
    benchmark_returns = daily_returns * 0.95
    beta, alpha = compute_beta_alpha(daily_returns, benchmark_returns)
    print("Beta:", beta, "Annualized Alpha:", alpha)

    te = compute_tracking_error(daily_returns, benchmark_returns)
    print("Tracking Error:", te)

    mwr = compute_mwr([-1000000, 0, 1200000], [dates[0], dates[2], dates[-1]])
    print("Money-Weighted Return (annualized):", mwr)

    twr = compute_twr(values)
    print("Time-Weighted Return:", twr)
