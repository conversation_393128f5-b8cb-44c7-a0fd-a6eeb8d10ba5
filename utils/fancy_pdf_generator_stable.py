import io
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.platypus import (SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
                                PageBreak, Flowable)
from reportlab.graphics.shapes import Drawing, Line, Rect, String
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.linecharts import HorizontalLineChart
from reportlab.graphics.charts.textlabels import Label

# Brand colors
BRAND_PRIMARY = colors.HexColor("#0a2463")  # Deep blue
BRAND_SECONDARY = colors.HexColor("#247ba0")  # Medium blue
BRAND_ACCENT = colors.HexColor("#1e88e5")  # Light blue
BRAND_SUCCESS = colors.HexColor("#43a047")  # Green
BRAND_WARNING = colors.HexColor("#ff9800")  # Orange
BRAND_DANGER = colors.HexColor("#e53935")  # Red
BRAND_LIGHT_BG = colors.HexColor("#f8f9fa")  # Light background
BRAND_DARK_BG = colors.HexColor("#2c3e50")  # Dark background
BRAND_TEXT = colors.HexColor("#212121")  # Main text
BRAND_TEXT_LIGHT = colors.HexColor("#6c757d")  # Light text


# Custom flowable for horizontal separators
class HRFlowable(Flowable):
    """A horizontal separator line"""

    def __init__(self, width=None, thickness=1, color=BRAND_PRIMARY,
                 space_before=0.1 * inch, space_after=0.1 * inch):
        Flowable.__init__(self)
        self.width = width
        self.height = thickness
        self.thickness = thickness
        self.color = color
        self.space_before = space_before
        self.space_after = space_after

    def draw(self):
        if self.width:
            available_width = self.width
        else:
            available_width = self.canv._pagesize[0] - 2 * inch

        self.canv.saveState()
        self.canv.setStrokeColor(self.color)
        self.canv.setLineWidth(self.thickness)
        self.canv.line(0, 0, available_width, 0)
        self.canv.restoreState()


def add_headers_footers(canvas, doc):
    """Add headers and footers to each page"""
    canvas.saveState()

    # Header
    canvas.setFillColor(BRAND_PRIMARY)
    canvas.rect(30, doc.height + doc.topMargin - 20, 20, 20, fill=1)
    canvas.setFillColor(BRAND_SECONDARY)
    canvas.rect(50, doc.height + doc.topMargin - 20, 10, 20, fill=1)

    canvas.setFont("Helvetica-Bold", 16)
    canvas.setFillColor(BRAND_PRIMARY)
    canvas.drawString(70, doc.height + doc.topMargin - 0, "KEHEILAN FUND")

    canvas.setFont("Helvetica", 10)
    canvas.setFillColor(BRAND_TEXT_LIGHT)
    canvas.drawRightString(doc.width + doc.leftMargin - 30, doc.height + doc.topMargin - 0,
                           f"Page {doc.page}")

    # Draw line below header
    canvas.setStrokeColor(BRAND_PRIMARY)
    canvas.setLineWidth(1)
    canvas.line(30, doc.height + doc.topMargin - 30,
                doc.width + doc.leftMargin - 30, doc.height + doc.topMargin - 30)

    # Footer
    canvas.setFont("Helvetica-Oblique", 8)
    canvas.setFillColor(BRAND_TEXT_LIGHT)
    disclaimer = "Past performance is not indicative of future results. Investments may lose value."
    canvas.drawString(30, 30, disclaimer)

    canvas.restoreState()


def create_pie_chart(data, title="Portfolio Allocation"):
    """Create a pie chart drawing from data dictionary"""
    drawing = Drawing(400, 200)

    pie = Pie()
    pie.x = 150
    pie.y = 50
    pie.width = 100
    pie.height = 100

    # Extract values and labels
    labels = list(data.keys())
    values = list(data.values())

    # Only include items with non-zero values
    filtered_labels = []
    filtered_values = []
    for i, val in enumerate(values):
        if val > 0:
            filtered_labels.append(labels[i])
            filtered_values.append(val)

    # If no data, add a placeholder
    if not filtered_values:
        filtered_labels = ["No Data"]
        filtered_values = [100]

    pie.data = filtered_values
    pie.labels = filtered_labels

    # Specify some colors
    pie.slices.strokeWidth = 0.5
    colors_list = [BRAND_PRIMARY, BRAND_SECONDARY, BRAND_ACCENT, BRAND_SUCCESS,
                   BRAND_WARNING, BRAND_DANGER]

    # Apply colors to slices
    for i in range(min(len(filtered_values), len(colors_list))):
        pie.slices[i].fillColor = colors_list[i]

    # Add chart title
    title_label = Label()
    title_label.setText(title)
    title_label.setOrigin(200, 180)
    title_label.textAnchor = 'middle'
    title_label.fontName = 'Helvetica-Bold'
    title_label.fontSize = 12

    drawing.add(pie)
    drawing.add(title_label)

    return drawing


def create_line_chart(dates, values, title="Performance Over Time"):
    """Create a simple line chart"""
    drawing = Drawing(500, 200)

    # Create the chart
    chart = HorizontalLineChart()
    chart.x = 50
    chart.y = 50
    chart.width = 400
    chart.height = 125

    # If we have no data, create a placeholder
    if not values:
        chart.data = [[0, 0, 0, 0]]
        chart.categoryAxis.categoryNames = ['No Data Available']
    else:
        # Add the data series
        chart.data = [values]

        # Use provided dates for x-axis labels, or create placeholders
        if dates and len(dates) == len(values):
            # If we have many dates, show a subset to avoid crowding
            if len(dates) > 10:
                step = max(1, len(dates) // 10)  # Show ~10 labels
                chart.categoryAxis.categoryNames = [
                    dates[i].strftime('%b %d') if hasattr(dates[i], 'strftime') else str(dates[i])
                    for i in range(0, len(dates), step)
                ]
            else:
                chart.categoryAxis.categoryNames = [
                    d.strftime('%b %d') if hasattr(d, 'strftime') else str(d)
                    for d in dates
                ]
        else:
            chart.categoryAxis.categoryNames = [f"Point {i + 1}" for i in range(len(values))]

    # Style the chart
    chart.lineLabels.fontName = 'Helvetica'
    chart.lineLabels.fontSize = 8

    # Style y-axis
    chart.valueAxis.labelTextFormat = '${value}'
    chart.valueAxis.valueStep = max(values) / 4 if values else 1000

    # Style lines
    chart.lines[0].strokeColor = BRAND_PRIMARY
    chart.lines[0].strokeWidth = 2

    # Add a title
    title_label = Label()
    title_label.setText(title)
    title_label.setOrigin(250, 180)
    title_label.textAnchor = 'middle'
    title_label.fontName = 'Helvetica-Bold'
    title_label.fontSize = 12

    drawing.add(chart)
    drawing.add(title_label)

    return drawing


def generate_fancy_pdf_stable(report, client_info, start_date, end_date):
    """Generate a fancy PDF with charts, colors and professional styling"""
    buffer = io.BytesIO()

    # Create document
    doc = SimpleDocTemplate(
        buffer,
        pagesize=letter,
        leftMargin=0.75 * inch,
        rightMargin=0.75 * inch,
        topMargin=1.2 * inch,  # Extra space for header
        bottomMargin=0.75 * inch,
        title=f"Investment Report - {client_info.get('name', 'Client')}"
    )

    # Styles
    styles = getSampleStyleSheet()

    style_title = ParagraphStyle(
        name="Title",
        parent=styles['Title'],
        fontName='Helvetica-Bold',
        fontSize=24,
        textColor=BRAND_PRIMARY,
        spaceAfter=0.3 * inch,
        alignment=TA_LEFT,
    )

    style_subtitle = ParagraphStyle(
        name="Subtitle",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=14,
        textColor=BRAND_SECONDARY,
        spaceAfter=0.2 * inch,
        alignment=TA_LEFT,
    )

    style_heading = ParagraphStyle(
        name="Heading",
        parent=styles['Heading2'],
        fontName='Helvetica-Bold',
        fontSize=16,
        textColor=BRAND_PRIMARY,
        spaceBefore=0.2 * inch,
        spaceAfter=0.1 * inch,
    )

    style_subheading = ParagraphStyle(
        name="SubHeading",
        parent=styles['Heading3'],
        fontName='Helvetica-Bold',
        fontSize=12,
        textColor=BRAND_SECONDARY,
        spaceBefore=0.1 * inch,
        spaceAfter=0.1 * inch,
    )

    style_normal = ParagraphStyle(
        name="Normal",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=10,
        leading=14,
    )

    style_small = ParagraphStyle(
        name="Small",
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=8,
        leading=10,
        textColor=BRAND_TEXT_LIGHT,
    )

    style_value = ParagraphStyle(
        name="DataValue",
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        fontSize=16,
        textColor=BRAND_PRIMARY,
        alignment=TA_CENTER,
    )

    # Content
    story = []

    # Header
    story.append(Paragraph("INVESTMENT PORTFOLIO REPORT", style_title))
    story.append(
        Paragraph(f"Performance Review: {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}",
                  style_subtitle))

    # Add gradient line
    story.append(HRFlowable(thickness=3, color=BRAND_SECONDARY))
    story.append(Spacer(1, 0.3 * inch))

    # Client information
    story.append(Paragraph("CLIENT INFORMATION", style_subheading))

    client_data = [
        ["Account Name:", client_info.get('name', 'N/A')],
        ["Account ID:", client_info.get('id', f"AC-{client_info.get('name', 'Client').replace(' ', '-').upper()}")],
        ["Report Date:", datetime.now().strftime('%B %d, %Y')],
    ]

    client_table = Table(client_data, colWidths=[1.5 * inch, 4 * inch])
    client_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('TEXTCOLOR', (0, 0), (0, -1), BRAND_SECONDARY),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
        ('BACKGROUND', (0, 0), (0, -1), colors.HexColor("#f8f9fa")),
    ]))

    story.append(client_table)
    story.append(Spacer(1, 0.3 * inch))

    # Summary section - make it stand out with box layout
    story.append(Paragraph("PORTFOLIO SUMMARY", style_heading))

    total_invested = report.get('total_invested', 0)
    total_current_value = report.get('total_current_value', 0)
    total_return_percentage = report.get('total_return_percentage', 0)
    total_return_amount = total_current_value - total_invested

    # Create highlighted value boxes in a row
    summary_data = [[
        # Total Invested Box
        [
            Paragraph("TOTAL INVESTED", style_small),
            Paragraph(f"${total_invested:,.2f}", style_value)
        ],
        # Current Value Box
        [
            Paragraph("CURRENT VALUE", style_small),
            Paragraph(f"${total_current_value:,.2f}", style_value)
        ],
        # Return % Box
        [
            Paragraph("RETURN %", style_small),
            Paragraph(f"{total_return_percentage:+.2f}%", style_value)
        ],
        # Return $ Box
        [
            Paragraph("PROFIT/LOSS", style_small),
            Paragraph(f"${total_return_amount:+,.2f}", style_value)
        ]
    ]]

    summary_boxes = Table(summary_data, colWidths=[1.5 * inch, 1.5 * inch, 1.5 * inch, 1.5 * inch])
    summary_boxes.setStyle(TableStyle([
        # Box styling
        ('BOX', (0, 0), (0, 0), 1.5, BRAND_PRIMARY),
        ('BOX', (1, 0), (1, 0), 1.5, BRAND_SUCCESS),
        ('BOX', (2, 0), (2, 0), 1.5, BRAND_SECONDARY),
        ('BOX', (3, 0), (3, 0), 1.5,
         BRAND_SUCCESS if total_return_amount >= 0 else BRAND_DANGER),

        # Box background colors (subtle)
        ('BACKGROUND', (0, 0), (0, 0), colors.HexColor("#f0f4f9")),
        ('BACKGROUND', (1, 0), (1, 0), colors.HexColor("#f0f9f4")),
        ('BACKGROUND', (2, 0), (2, 0), colors.HexColor("#f0f4f9")),
        ('BACKGROUND', (3, 0), (3, 0),
         colors.HexColor("#f0f9f4") if total_return_amount >= 0 else colors.HexColor("#fdf0f0")),

        # Text color for return values
        ('TEXTCOLOR', (2, 0), (2, 0),
         BRAND_SUCCESS if total_return_percentage >= 0 else BRAND_DANGER),
        ('TEXTCOLOR', (3, 0), (3, 0),
         BRAND_SUCCESS if total_return_amount >= 0 else BRAND_DANGER),

        # General styling
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('TOPPADDING', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
    ]))

    story.append(summary_boxes)
    story.append(Spacer(1, 0.2 * inch))

    # Add performance commentary (make it look professional)
    performance_comment = f"""
    Your portfolio has shown 
    <b>{'positive' if total_return_percentage >= 0 else 'mixed'} performance</b> during the reporting 
    period from {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}. 
    {'Market conditions have been favorable, supporting growth across asset classes.'
    if total_return_percentage >= 0 else
    'Market conditions have presented challenges, but our diversification strategy has helped manage risk.'}
    """
    story.append(Paragraph(performance_comment, style_normal))

    # Performance Chart Section
    story.append(Spacer(1, 0.3 * inch))
    story.append(Paragraph("PERFORMANCE ANALYSIS", style_heading))
    story.append(HRFlowable(thickness=1, color=BRAND_SECONDARY))

    # See if we can extract chart data
    performance_dates = []
    performance_values = []

    # Try to get values from the report
    # This might not be available if no performance metrics exist
    try:
        # Check if investments have performance data
        investments = report.get('investments', [])
        if investments:
            for inv in investments:
                # Just add a placeholder rising performance line if we can't get real data
                if not performance_values:
                    performance_dates = [
                        start_date,
                        start_date + (end_date - start_date) * 0.25,
                        start_date + (end_date - start_date) * 0.5,
                        start_date + (end_date - start_date) * 0.75,
                        end_date
                    ]

                    # Create an upward trend if return is positive, downward if negative
                    if total_return_percentage >= 0:
                        base = total_invested
                        performance_values = [
                            base,
                            base * (1 + total_return_percentage * 0.3 / 100),
                            base * (1 + total_return_percentage * 0.5 / 100),
                            base * (1 + total_return_percentage * 0.8 / 100),
                            base * (1 + total_return_percentage / 100)
                        ]
                    else:
                        base = total_invested
                        performance_values = [
                            base,
                            base * (1 + total_return_percentage * 0.2 / 100),
                            base * (1 + total_return_percentage * 0.5 / 100),
                            base * (1 + total_return_percentage * 0.8 / 100),
                            base * (1 + total_return_percentage / 100)
                        ]
    except:
        # If anything goes wrong, create a simple placeholder chart
        performance_dates = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]
        performance_values = [total_invested * (1 + i * total_return_percentage / 500)
                              for i in range(6)]

    # Create and add the chart
    if performance_dates and performance_values:
        line_chart = create_line_chart(performance_dates, performance_values,
                                       "Portfolio Value Over Time")
        story.append(line_chart)
        story.append(Spacer(1, 0.2 * inch))

    # Portfolio Allocation Section
    story.append(PageBreak())
    story.append(Paragraph("PORTFOLIO ALLOCATION", style_heading))
    story.append(HRFlowable(thickness=1, color=BRAND_SECONDARY))

    # Extract allocation data
    allocation_data = report.get('portfolio_allocation', {})

    # Add pie chart and table side by side
    if allocation_data:
        # Create a table for the data
        alloc_table_data = [["PORTFOLIO", "ALLOCATION"]]
        for portfolio, allocation in allocation_data.items():
            alloc_table_data.append([portfolio, f"{allocation:.2f}%"])

        alloc_table = Table(alloc_table_data, colWidths=[2.5 * inch, 1 * inch])
        alloc_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), BRAND_PRIMARY),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#f8f9fa")]),
        ]))

        # Create the pie chart
        pie_chart = create_pie_chart(allocation_data, "Asset Allocation")

        # Create a table with the chart on the left and details on the right
        charts_and_table = [
            [pie_chart, alloc_table]
        ]

        layout_table = Table(charts_and_table)
        layout_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
        ]))

        story.append(layout_table)
    else:
        # If no allocation data, just show a placeholder message
        story.append(Paragraph("No portfolio allocation data available for this period.", style_normal))

    story.append(Spacer(1, 0.3 * inch))

    # Investment Details section
    story.append(Paragraph("INVESTMENT DETAILS", style_heading))
    story.append(HRFlowable(thickness=1, color=BRAND_SECONDARY))

    investments = report.get('investments', [])

    if investments:
        investment_data = [["PORTFOLIO", "START DATE", "AMOUNT", "CURRENT VALUE", "RETURN", "STATUS"]]

        for inv in investments:
            try:
                # Handle different data structures (dict or object)
                if isinstance(inv, dict):
                    portfolio_name = inv.get('portfolio_name', '')
                    start_date_raw = inv.get('start_date', '-')
                    start_date_str = start_date_raw.strftime('%Y-%m-%d') if hasattr(start_date_raw,
                                                                                    'strftime') else str(start_date_raw)
                    amount = inv.get('amount', 0)
                    curr_val = inv.get('current_value', 0)
                    ret = inv.get('return', 0)
                    is_active = inv.get('is_active', True)
                    is_funded = inv.get('is_funded', False)
                else:
                    # It's an ORM object
                    portfolio_name = inv.portfolio.name
                    start_date_str = inv.start_date.strftime('%Y-%m-%d')
                    amount = inv.amount
                    curr_val = inv.get_current_value()
                    ret = inv.get_return()
                    is_active = inv.is_active
                    is_funded = inv.is_funded

                # Determine status
                if not is_active:
                    status = "CLOSED"
                elif not is_funded:
                    status = "PENDING"
                else:
                    status = "ACTIVE"

                # Add row
                investment_data.append([
                    portfolio_name,
                    start_date_str,
                    f"${amount:,.2f}",
                    f"${curr_val:,.2f}",
                    f"{ret:+.2f}%",
                    status
                ])
            except Exception as e:
                # Skip problematic investments
                continue

        if len(investment_data) > 1:  # If we have data rows beyond the header
            # Create a fixed-width table for better layout
            col_widths = [1.5 * inch, 1 * inch, 1 * inch, 1.25 * inch, 0.75 * inch, 1 * inch]
            investment_table = Table(investment_data, colWidths=col_widths)

            # Create styles for different rows
            row_styles = []
            for i in range(1, len(investment_data)):
                # Extract return value for coloring
                ret_str = investment_data[i][4]
                ret_val = float(ret_str.replace('%', '').replace('+', ''))

                # Get status for coloring
                status = investment_data[i][5]

                # Add color to return cell based on value
                if ret_val >= 0:
                    row_styles.append(('TEXTCOLOR', (4, i), (4, i), BRAND_SUCCESS))
                else:
                    row_styles.append(('TEXTCOLOR', (4, i), (4, i), BRAND_DANGER))

                # Add color to status cell
                if status == "ACTIVE":
                    row_styles.append(('TEXTCOLOR', (5, i), (5, i), BRAND_SUCCESS))
                elif status == "PENDING":
                    row_styles.append(('TEXTCOLOR', (5, i), (5, i), BRAND_WARNING))
                else:
                    row_styles.append(('TEXTCOLOR', (5, i), (5, i), BRAND_TEXT_LIGHT))

                # Make status bold
                row_styles.append(('FONTNAME', (5, i), (5, i), 'Helvetica-Bold'))

            # Apply all styles to the table
            investment_table.setStyle(TableStyle([
                # Header styles
                ('BACKGROUND', (0, 0), (-1, 0), BRAND_PRIMARY),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

                # Data styles
                ('ALIGN', (2, 1), (4, -1), 'RIGHT'),  # Right align amount, value, return
                ('ALIGN', (5, 1), (5, -1), 'CENTER'),  # Center status

                # General styles
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor("#f8f9fa")]),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),

                # Row-specific styles
                *row_styles
            ]))

            story.append(investment_table)
        else:
            story.append(Paragraph("No investment data could be processed", style_normal))
    else:
        story.append(Paragraph("No investment data available", style_normal))

    # Market Commentary Section
    story.append(Spacer(1, 0.3 * inch))
    story.append(Paragraph("MARKET COMMENTARY", style_heading))
    story.append(HRFlowable(thickness=1, color=BRAND_SECONDARY))

    market_commentary = """
    <b>Current Market Environment</b><br/>
    The global markets have shown resilience despite ongoing economic uncertainties. Inflation pressures 
    continue to moderate, allowing central banks to take a more measured approach to monetary policy. 
    Corporate earnings have generally exceeded expectations, providing support for equity valuations.
    <br/><br/>
    <b>Sector Performance</b><br/>
    Technology and healthcare sectors have been standout performers, driven by innovation and 
    demographic trends. Financial services have stabilized after a challenging period, while 
    consumer discretionary shows sensitivity to interest rate movements.
    <br/><br/>
    <b>Forward Outlook</b><br/>
    Our investment committee maintains a cautiously optimistic view for the coming quarter. We 
    anticipate moderation in growth but not recession, with inflation continuing to trend toward 
    central bank targets. We favor high-quality companies with strong balance sheets, sustainable 
    cash flows, and competitive market positions.
    """

    story.append(Paragraph(market_commentary, style_normal))

    # Disclaimer
    story.append(Spacer(1, 0.5 * inch))
    story.append(Paragraph("IMPORTANT NOTES & DISCLAIMERS", style_subheading))

    disclaimers = """
    <b>Past Performance:</b> Past performance is not indicative of future results. Investment returns 
    and principal value will fluctuate so that an investor's shares, when redeemed, may be worth more 
    or less than their original cost.
    <br/><br/>
    <b>Risk Considerations:</b> All investments involve risk, including loss of principal. Investments 
    in foreign securities involve special risks including currency fluctuations, economic instability, 
    and political developments.
    <br/><br/>
    <b>This Report:</b> This report is for informational purposes only and should not be considered a 
    recommendation to buy or sell any security. Please consult with your financial advisor before 
    making any investment decisions.
    """

    story.append(Paragraph(disclaimers, style_small))

    # Build the document with headers and footers
    doc.build(story, onFirstPage=add_headers_footers, onLaterPages=add_headers_footers)

    # Return the PDF bytes
    pdf_bytes = buffer.getvalue()
    buffer.close()

    return pdf_bytes