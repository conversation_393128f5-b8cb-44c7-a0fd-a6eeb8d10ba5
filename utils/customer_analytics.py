"""
Customer Analytics Module - Generate advanced visualizations for customer investments
"""
import pandas as pd
from datetime import datetime, timedelta
import json
from flask import current_app

from models import db
from models.models_customer import Customer
from models.investment import Investment
from models.performance import Performance
from models.portfolio import Portfolio


def get_customer_investment_data(customer_id, for_chart=False):
    """
    Retrieve comprehensive investment data for a customer
    organized for visualization purposes

    Args:
        customer_id: ID of the customer
        for_chart: Whether to format data for Chart.js (defaults to False)

    Returns:
        Dictionary with investment data for visualization
    """
    # Get the customer and their investments
    customer = Customer.query.get(customer_id)
    if not customer:
        return {"error": "Customer not found"}

    # Only include active and funded investments for data analysis
    active_investments = Investment.query.filter_by(
        customer_id=customer_id,
        is_active=True,
        is_funded=True
    ).all()

    # Prepare datasets for plotting
    datasets = []
    portfolio_amounts = {}
    portfolio_values = {}

    # Collect all dates from performance records across all investments
    all_dates = set()
    for investment in active_investments:
        performances = Performance.query.filter_by(investment_id=investment.id).order_by(Performance.date).all()
        for perf in performances:
            all_dates.add(perf.date.strftime('%Y-%m-%d'))

    # Sort dates chronologically
    date_labels = sorted(all_dates)

    # For each investment, create a dataset
    for investment in active_investments:
        # Get portfolio info
        portfolio = investment.portfolio

        # Track portfolio totals for pie charts
        if portfolio.name not in portfolio_amounts:
            portfolio_amounts[portfolio.name] = 0
            portfolio_values[portfolio.name] = 0
        portfolio_amounts[portfolio.name] += investment.amount
        portfolio_values[portfolio.name] += investment.get_current_value()

        # Get performance history
        performances = Performance.query.filter_by(investment_id=investment.id).order_by(Performance.date).all()

        # Create a map of dates to values for efficient lookup
        value_map = {perf.date.strftime('%Y-%m-%d'): perf.value for perf in performances}
        return_map = {perf.date.strftime('%Y-%m-%d'): perf.return_pct for perf in performances}

        # Fill in values for all dates (or null if no data for that date)
        values = []
        returns = []
        for date in date_labels:
            values.append(value_map.get(date, None))
            returns.append(return_map.get(date, None))

        # Create dataset for value chart (Chart.js format)
        value_dataset = {
            "label": f"{portfolio.name} (${investment.amount:,.0f})",
            "data": values,
            "borderWidth": 2,
            "borderColor": get_color_for_portfolio(portfolio.name, investment.id),
            "fill": False,
            "tension": 0.1
        }

        # Create dataset for return chart (Chart.js format)
        return_dataset = {
            "label": f"{portfolio.name} (${investment.amount:,.0f})",
            "data": returns,
            "borderWidth": 2,
            "borderColor": get_color_for_portfolio(portfolio.name, investment.id),
            "fill": False,
            "tension": 0.1
        }

        datasets.append({
            "investment_id": investment.id,
            "portfolio_name": portfolio.name,
            "amount": investment.amount,
            "current_value": investment.get_current_value(),
            "return_pct": investment.get_return(),
            "start_date": investment.start_date.strftime('%Y-%m-%d'),
            "value_dataset": value_dataset,
            "return_dataset": return_dataset
        })

    # Calculate totals
    total_invested = sum(inv.amount for inv in active_investments)
    total_current_value = sum(inv.get_current_value() for inv in active_investments)
    total_return = ((total_current_value - total_invested) / total_invested * 100) if total_invested > 0 else 0

    # Create a cumulative dataset for all investments combined
    cumulative_values = [0] * len(date_labels)
    for dataset in datasets:
        values = dataset["value_dataset"]["data"]
        for i, value in enumerate(values):
            if value is not None:
                if cumulative_values[i] is None:
                    cumulative_values[i] = value
                else:
                    cumulative_values[i] += value

    # Create combined dataset for all investments
    combined_dataset = {
        "label": "Total Portfolio Value",
        "data": cumulative_values,
        "borderWidth": 3,
        "borderColor": "rgba(75, 192, 192, 1)",
        "backgroundColor": "rgba(75, 192, 192, 0.2)",
        "fill": True,
        "tension": 0.1
    }

    # Calculate risk metrics
    if len(active_investments) > 0 and len(date_labels) > 1:
        risk_metrics = calculate_risk_metrics(customer_id)
    else:
        risk_metrics = {
            "volatility": None,
            "sharpe_ratio": None,
            "sortino_ratio": None,
            "max_drawdown": None
        }

    # Prepare result for charts or API
    if for_chart:
        return {
            "labels": date_labels,
            "value_datasets": [d["value_dataset"] for d in datasets] + [combined_dataset],
            "return_datasets": [d["return_dataset"] for d in datasets],
            "portfolio_distribution": {
                "labels": list(portfolio_amounts.keys()),
                "invested_data": list(portfolio_amounts.values()),
                "current_data": list(portfolio_values.values())
            }
        }

    # Return full data
    return {
        "customer": {
            "id": customer.id,
            "name": customer.name,
            "email": customer.email
        },
        "summary": {
            "total_invested": total_invested,
            "total_current_value": total_current_value,
            "total_return": total_return,
            "risk_metrics": risk_metrics
        },
        "date_labels": date_labels,
        "investments": datasets,
        "combined_dataset": combined_dataset,
        "portfolio_distribution": {
            "labels": list(portfolio_amounts.keys()),
            "invested_amounts": list(portfolio_amounts.values()),
            "current_values": list(portfolio_values.values())
        }
    }


def get_color_for_portfolio(portfolio_name, investment_id=None):
    """
    Generate a consistent color for a portfolio, with slight variations 
    for multiple investments in the same portfolio.
    """
    # Base colors for different portfolios
    color_map = {
        "Keheilan Fund": "rgba(54, 162, 235, {alpha})",
        "Alpha Growth Fund": "rgba(255, 99, 132, {alpha})",
        "Conservative Income": "rgba(75, 192, 192, {alpha})",
        "Balanced Growth": "rgba(255, 206, 86, {alpha})",
        "High Risk Aggressive": "rgba(153, 102, 255, {alpha})",
        "Global Equity": "rgba(255, 159, 64, {alpha})",
        "Fixed Income": "rgba(201, 203, 207, {alpha})",
        "Dividend Focus": "rgba(255, 99, 71, {alpha})"
    }

    # If portfolio is in our map, use that color
    base_color = color_map.get(portfolio_name, "rgba(54, 162, 235, {alpha})")

    # If investment_id is provided, use it to create slight variations
    # in color for multiple investments in the same portfolio
    if investment_id:
        # Change the RGB values slightly based on investment ID
        # This keeps similar colors for the same portfolio but makes them distinguishable
        parts = base_color.replace("rgba(", "").replace("{alpha})", "").split(",")
        r = int(parts[0])
        g = int(parts[1])
        b = int(parts[2])

        # Create slight variations (±10%)
        variation = (investment_id % 20) - 10  # -10 to +9
        r = max(0, min(255, r + variation))
        g = max(0, min(255, g - variation))
        b = max(0, min(255, b + variation // 2))

        return f"rgba({r}, {g}, {b}, 1)"

    # Otherwise return with full opacity
    return base_color.format(alpha=1)


def calculate_risk_metrics(customer_id):
    """
    Calculate advanced risk metrics for a customer's portfolio.

    Returns a dictionary with:
    - volatility: Annual volatility
    - sharpe_ratio: Sharpe ratio (assuming 2% risk-free rate)
    - sortino_ratio: Sortino ratio (downside risk only)
    - max_drawdown: Maximum drawdown percentage
    """
    from utils.performance_metrics import (
        compute_daily_returns,
        compute_volatility,
        compute_sharpe_ratio,
        compute_sortino_ratio,
        compute_max_drawdown
    )

    # Get active investments
    active_investments = Investment.query.filter_by(
        customer_id=customer_id,
        is_active=True,
        is_funded=True
    ).all()

    # Collect all dates and corresponding portfolio values
    date_value_map = {}
    for investment in active_investments:
        performances = Performance.query.filter_by(investment_id=investment.id).order_by(Performance.date).all()
        for perf in performances:
            if perf.date not in date_value_map:
                date_value_map[perf.date] = 0
            date_value_map[perf.date] += perf.value

    if len(date_value_map) < 2:
        return {
            "volatility": None,
            "sharpe_ratio": None,
            "sortino_ratio": None,
            "max_drawdown": None
        }

    # Sort dates and corresponding values
    sorted_dates = sorted(date_value_map.keys())
    values = [date_value_map[date] for date in sorted_dates]

    # Calculate daily returns
    daily_returns = compute_daily_returns(sorted_dates, values)

    # Calculate risk metrics
    volatility = compute_volatility(daily_returns)
    sharpe = compute_sharpe_ratio(daily_returns, risk_free_rate=0.02)
    sortino = compute_sortino_ratio(daily_returns, risk_free_rate=0.02)
    max_dd = compute_max_drawdown(values)

    return {
        "volatility": volatility * 100,  # Convert to percentage
        "sharpe_ratio": sharpe,
        "sortino_ratio": sortino,
        "max_drawdown": max_dd * 100  # Convert to percentage
    }


def generate_customer_dashboard_chart_data(customer_id):
    """
    Generate all chart data needed for a comprehensive customer dashboard
    """
    # Get basic investment data
    data = get_customer_investment_data(customer_id, for_chart=True)
    if "error" in data:
        return data

    # Get customer info and active investments
    customer = Customer.query.get(customer_id)
    active_investments = Investment.query.filter_by(
        customer_id=customer_id,
        is_active=True,
        is_funded=True
    ).all()

    # Calculate monthly performance for bar chart
    monthly_performance = calculate_monthly_performance(active_investments)

    # Get risk metrics for radar chart
    risk_metrics = calculate_risk_metrics(customer_id)

    # Create comparison to benchmarks
    benchmark_data = generate_benchmark_comparison(data["labels"], active_investments)

    # Return all chart data
    return {
        "customer_name": customer.name,
        "value_chart": {
            "type": "line",
            "data": {
                "labels": data["labels"],
                "datasets": data["value_datasets"]
            }
        },
        "return_chart": {
            "type": "line",
            "data": {
                "labels": data["labels"],
                "datasets": data["return_datasets"]
            }
        },
        "allocation_chart": {
            "type": "pie",
            "data": {
                "labels": data["portfolio_distribution"]["labels"],
                "datasets": [{
                    "data": data["portfolio_distribution"]["current_data"],
                    "backgroundColor": [
                        get_color_for_portfolio(label, 0.7)
                        for label in data["portfolio_distribution"]["labels"]
                    ]
                }]
            }
        },
        "monthly_performance": monthly_performance,
        "risk_metrics": risk_metrics,
        "benchmark_comparison": benchmark_data
    }


def calculate_monthly_performance(investments):
    """Calculate monthly performance for bar chart visualization"""
    # Get performance grouped by month
    monthly_data = {}

    for investment in investments:
        performances = Performance.query.filter_by(investment_id=investment.id).order_by(Performance.date).all()
        for perf in performances:
            month_key = perf.date.strftime('%Y-%m')
            if month_key not in monthly_data:
                monthly_data[month_key] = {
                    "month": perf.date.strftime('%b %Y'),
                    "total_value": 0,
                    "prev_month_value": 0
                }
            monthly_data[month_key]["total_value"] += perf.value

    # Sort months chronologically
    sorted_months = sorted(monthly_data.keys())

    # Calculate month-over-month returns
    labels = []
    values = []

    for i, month_key in enumerate(sorted_months):
        labels.append(monthly_data[month_key]["month"])

        if i > 0:
            prev_month = sorted_months[i - 1]
            curr_value = monthly_data[month_key]["total_value"]
            prev_value = monthly_data[prev_month]["total_value"]

            if prev_value > 0:
                month_return = ((curr_value - prev_value) / prev_value) * 100
                values.append(month_return)
            else:
                values.append(0)
        else:
            values.append(0)  # First month has no previous data

    return {
        "labels": labels,
        "data": values
    }


def generate_benchmark_comparison(date_labels, investments):
    """Generate comparison data against market benchmarks"""
    # Get customer portfolio returns
    portfolio_values = {date: 0 for date in date_labels}

    for investment in investments:
        performances = Performance.query.filter_by(investment_id=investment.id).all()
        for perf in performances:
            date_str = perf.date.strftime('%Y-%m-%d')
            if date_str in portfolio_values:
                portfolio_values[date_str] += perf.value

    # Calculate cumulative returns
    if len(date_labels) > 1:
        portfolio_returns = []
        initial_value = portfolio_values[date_labels[0]]

        for date in date_labels:
            if initial_value > 0:
                ret = ((portfolio_values[date] - initial_value) / initial_value) * 100
                portfolio_returns.append(ret)
            else:
                portfolio_returns.append(0)
    else:
        portfolio_returns = [0]

    # Generate sample benchmark returns
    # In a production app, you'd use real market data from an API
    sp500_returns = generate_sample_benchmark_returns(date_labels, volatility=0.8)
    nasdaq_returns = generate_sample_benchmark_returns(date_labels, volatility=1.2)
    bond_returns = generate_sample_benchmark_returns(date_labels, volatility=0.3, bias=0.5)

    return {
        "labels": date_labels,
        "datasets": [
            {
                "label": "Your Portfolio",
                "data": portfolio_returns,
                "borderColor": "rgba(75, 192, 192, 1)",
                "backgroundColor": "rgba(75, 192, 192, 0.2)",
                "fill": False
            },
            {
                "label": "S&P 500",
                "data": sp500_returns,
                "borderColor": "rgba(54, 162, 235, 1)",
                "backgroundColor": "rgba(54, 162, 235, 0)",
                "fill": False
            },
            {
                "label": "NASDAQ",
                "data": nasdaq_returns,
                "borderColor": "rgba(255, 99, 132, 1)",
                "backgroundColor": "rgba(255, 99, 132, 0)",
                "fill": False
            },
            {
                "label": "US Agg Bond",
                "data": bond_returns,
                "borderColor": "rgba(153, 102, 255, 1)",
                "backgroundColor": "rgba(153, 102, 255, 0)",
                "fill": False
            }
        ]
    }


def generate_sample_benchmark_returns(date_labels, volatility=1.0, bias=1.0):
    """
    Generate sample benchmark returns for demonstration purposes

    In a production app, you would fetch real benchmark data from a financial API
    """
    import random
    import math

    # Set random seed for consistent demo
    random.seed(42)

    # Generate sample cumulative returns
    returns = [0]  # Start at 0%

    # Parameters to make returns somewhat realistic
    monthly_drift = 0.008 * bias  # ~10% annual return expectation * bias

    for i in range(1, len(date_labels)):
        # Random component based on volatility
        random_component = random.normalvariate(0, 0.015 * volatility)

        # Drift component (expected return)
        drift = monthly_drift

        # Previous value plus new increment
        new_value = returns[-1] + drift + random_component

        # Add some cyclicality
        cycle = 0.005 * math.sin(i / 10)

        returns.append(new_value * 100 + cycle)  # Convert to percentage

    return returns