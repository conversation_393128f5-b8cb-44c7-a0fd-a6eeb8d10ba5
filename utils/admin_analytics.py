"""
Admin Analytics Module - Advanced metrics and visualizations for administrators
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict

from models import db
from models.models_customer import Customer
from models.investment import Investment
from models.performance import Performance, PortfolioSnapshot
from models.portfolio import Portfolio
from utils.performance_metrics import compute_daily_returns, compute_volatility, compute_sharpe_ratio

def get_admin_dashboard_data():
    """
    Retrieves comprehensive data for the admin dashboard including:
    - Customer statistics
    - Portfolio performance
    - Investment trends
    - Risk metrics
    
    Returns:
        Dictionary containing all admin dashboard data
    """
    # Get basic counts
    customer_count = Customer.query.filter_by(is_admin=False).count()
    portfolio_count = Portfolio.query.count()
    investment_count = Investment.query.count()
    
    # Calculate total assets under management
    assets_under_management = db.session.query(
        db.func.sum(Investment.amount)
    ).filter(
        Investment.is_active == True,
        Investment.is_funded == True
    ).scalar() or 0
    
    # Calculate current total value
    active_investments = Investment.query.filter_by(
        is_active=True,
        is_funded=True
    ).all()
    
    total_current_value = sum(inv.get_current_value() for inv in active_investments)
    total_return = ((total_current_value - assets_under_management) / assets_under_management * 100) if assets_under_management > 0 else 0
    
    # Get portfolio performance data
    portfolio_performance = get_portfolio_performance_data()
    
    # Get customer performance data
    customer_performance = get_customer_performance_data()
    
    # Get investment trend data over time
    investment_trends = get_investment_trends()
    
    # Get risk metrics for all portfolios
    risk_metrics = get_portfolio_risk_metrics()
    
    return {
        "summary": {
            "customer_count": customer_count,
            "portfolio_count": portfolio_count,
            "investment_count": investment_count,
            "assets_under_management": assets_under_management,
            "total_current_value": total_current_value,
            "total_return": total_return
        },
        "portfolio_performance": portfolio_performance,
        "customer_performance": customer_performance,
        "investment_trends": investment_trends,
        "risk_metrics": risk_metrics
    }

def get_portfolio_performance_data():
    """
    Gets performance data for all portfolios
    
    Returns:
        Dictionary with portfolio performance metrics
    """
    portfolios = Portfolio.query.all()
    performance_data = []
    
    for portfolio in portfolios:
        # Get latest snapshot
        latest_snapshot = PortfolioSnapshot.query.filter_by(
            portfolio_id=portfolio.id
        ).order_by(PortfolioSnapshot.date.desc()).first()
        
        # Get first snapshot
        first_snapshot = PortfolioSnapshot.query.filter_by(
            portfolio_id=portfolio.id
        ).order_by(PortfolioSnapshot.date.asc()).first()
        
        # Calculate total invested in this portfolio
        total_invested = db.session.query(
            db.func.sum(Investment.amount)
        ).filter(
            Investment.portfolio_id == portfolio.id,
            Investment.is_active == True,
            Investment.is_funded == True
        ).scalar() or 0
        
        # Calculate investor count
        investor_count = db.session.query(
            db.func.count(db.func.distinct(Investment.customer_id))
        ).filter(
            Investment.portfolio_id == portfolio.id,
            Investment.is_active == True,
            Investment.is_funded == True
        ).scalar() or 0
        
        # Calculate performance
        current_value = latest_snapshot.total_value if latest_snapshot else 0
        initial_value = first_snapshot.total_value if first_snapshot else 0
        
        if initial_value > 0:
            return_pct = ((current_value - initial_value) / initial_value) * 100
        else:
            return_pct = 0
            
        # Get historical NAV values
        snapshots = PortfolioSnapshot.query.filter_by(
            portfolio_id=portfolio.id
        ).order_by(PortfolioSnapshot.date).all()
        
        dates = [snapshot.date.strftime("%Y-%m-%d") for snapshot in snapshots]
        values = [snapshot.total_value for snapshot in snapshots]
        
        performance_data.append({
            "id": portfolio.id,
            "name": portfolio.name,
            "risk_level": portfolio.risk_level,
            "current_value": current_value,
            "total_invested": total_invested,
            "investor_count": investor_count,
            "return_pct": return_pct,
            "dates": dates,
            "values": values
        })
    
    return performance_data

def get_customer_performance_data():
    """
    Gets performance data for all customers
    
    Returns:
        List of dictionaries with customer performance metrics
    """
    # Get all non-admin customers
    customers = Customer.query.filter_by(is_admin=False).all()
    performance_data = []
    
    for customer in customers:
        # Get all active investments for this customer
        investments = Investment.query.filter_by(
            customer_id=customer.id, 
            is_active=True,
            is_funded=True
        ).all()
        
        # Calculate totals
        total_invested = sum(inv.amount for inv in investments)
        total_current_value = sum(inv.get_current_value() for inv in investments)
        
        if total_invested > 0:
            total_return = ((total_current_value - total_invested) / total_invested) * 100
        else:
            total_return = 0
        
        # Count portfolios
        portfolio_ids = set(inv.portfolio_id for inv in investments)
        
        # Find first investment date
        first_investment = Investment.query.filter_by(
            customer_id=customer.id
        ).order_by(Investment.start_date.asc()).first()
        
        first_date = first_investment.start_date if first_investment else None
        
        performance_data.append({
            "id": customer.id,
            "name": customer.name,
            "email": customer.email,
            "total_invested": total_invested,
            "total_current_value": total_current_value,
            "total_return": total_return,
            "investment_count": len(investments),
            "portfolio_count": len(portfolio_ids),
            "first_investment_date": first_date.strftime("%Y-%m-%d") if first_date else None,
            "joined_date": customer.created_at.strftime("%Y-%m-%d")
        })
    
    return performance_data

def get_investment_trends():
    """
    Analyzes investment trends over time
    
    Returns:
        Dictionary with investment trend data
    """
    # Get all investments
    investments = Investment.query.all()
    
    # Group by month
    monthly_data = defaultdict(lambda: {"count": 0, "amount": 0})
    
    for inv in investments:
        month_key = inv.start_date.strftime("%Y-%m")
        monthly_data[month_key]["count"] += 1
        monthly_data[month_key]["amount"] += inv.amount
    
    # Sort by month
    sorted_months = sorted(monthly_data.keys())
    
    # Format for charts
    months = []
    counts = []
    amounts = []
    
    for month in sorted_months:
        months.append(month)
        counts.append(monthly_data[month]["count"])
        amounts.append(monthly_data[month]["amount"])
    
    # Group by portfolio
    portfolio_data = defaultdict(int)
    
    for inv in investments:
        portfolio = Portfolio.query.get(inv.portfolio_id)
        if portfolio:
            portfolio_data[portfolio.name] += inv.amount
    
    # Format for pie chart
    portfolio_labels = []
    portfolio_values = []
    
    for name, amount in portfolio_data.items():
        portfolio_labels.append(name)
        portfolio_values.append(amount)
    
    return {
        "monthly_trends": {
            "labels": months,
            "counts": counts,
            "amounts": amounts
        },
        "portfolio_distribution": {
            "labels": portfolio_labels,
            "values": portfolio_values
        }
    }

def get_portfolio_risk_metrics():
    """
    Calculates risk metrics for all portfolios
    
    Returns:
        Dictionary with risk metrics per portfolio
    """
    portfolios = Portfolio.query.all()
    risk_data = []
    
    for portfolio in portfolios:
        # Get all snapshots for this portfolio
        snapshots = PortfolioSnapshot.query.filter_by(
            portfolio_id=portfolio.id
        ).order_by(PortfolioSnapshot.date).all()
        
        if len(snapshots) >= 2:
            # Calculate daily returns
            dates = [snap.date for snap in snapshots]
            values = [snap.total_value for snap in snapshots]
            
            daily_returns = compute_daily_returns(dates, values)
            
            # Calculate risk metrics
            volatility = compute_volatility(daily_returns) * 100  # Convert to percentage
            sharpe = compute_sharpe_ratio(daily_returns, risk_free_rate=0.02)
            
            # Calculate max drawdown
            max_drawdown = 0
            peak = values[0]
            
            for value in values:
                if value > peak:
                    peak = value
                else:
                    drawdown = (peak - value) / peak
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown
            
            risk_data.append({
                "id": portfolio.id,
                "name": portfolio.name,
                "volatility": volatility,
                "sharpe_ratio": sharpe,
                "max_drawdown": max_drawdown * 100,  # Convert to percentage
                "risk_level": portfolio.risk_level
            })
    
    return risk_data

def get_customer_detailed_analytics(customer_id):
    """
    Get detailed performance analytics for a specific customer
    
    Args:
        customer_id: ID of the customer to analyze
        
    Returns:
        Dictionary with detailed customer analytics
    """
    customer = Customer.query.get(customer_id)
    if not customer:
        return {"error": "Customer not found"}
    
    # Get all active investments
    investments = Investment.query.filter_by(
        customer_id=customer_id,
        is_active=True,
        is_funded=True
    ).all()
    
    # Calculate totals
    total_invested = sum(inv.amount for inv in investments)
    total_current_value = sum(inv.get_current_value() for inv in investments)
    
    if total_invested > 0:
        total_return = ((total_current_value - total_invested) / total_invested) * 100
    else:
        total_return = 0
    
    # Group by portfolio
    portfolio_data = {}
    for inv in investments:
        portfolio = Portfolio.query.get(inv.portfolio_id)
        if portfolio:
            if portfolio.name not in portfolio_data:
                portfolio_data[portfolio.name] = {
                    "id": portfolio.id,
                    "risk_level": portfolio.risk_level,
                    "invested": 0,
                    "current_value": 0
                }
            
            portfolio_data[portfolio.name]["invested"] += inv.amount
            portfolio_data[portfolio.name]["current_value"] += inv.get_current_value()
    
    # Calculate returns for each portfolio
    for name, data in portfolio_data.items():
        if data["invested"] > 0:
            data["return_pct"] = ((data["current_value"] - data["invested"]) / data["invested"]) * 100
        else:
            data["return_pct"] = 0
    
    # Get all performance records
    all_performances = []
    for inv in investments:
        performances = Performance.query.filter_by(
            investment_id=inv.id
        ).order_by(Performance.date).all()
        
        for perf in performances:
            all_performances.append({
                "investment_id": inv.id,
                "portfolio_id": inv.portfolio_id,
                "date": perf.date,
                "value": perf.value,
                "return_pct": perf.return_pct
            })
    
    # Sort by date
    all_performances.sort(key=lambda x: x["date"])
    
    # Group by date to get total portfolio value over time
    daily_totals = defaultdict(float)
    for perf in all_performances:
        date_str = perf["date"].strftime("%Y-%m-%d")
        daily_totals[date_str] += perf["value"]
    
    # Format for chart
    dates = sorted(daily_totals.keys())
    values = [daily_totals[date] for date in dates]
    
    # Calculate first investment date
    first_investment = Investment.query.filter_by(
        customer_id=customer_id
    ).order_by(Investment.start_date.asc()).first()
    
    first_date = first_investment.start_date if first_investment else None
    
    # Calculate investment growth
    investment_growth = []
    cumulative_invested = 0
    
    for inv in sorted(investments, key=lambda x: x.start_date):
        date_str = inv.start_date.strftime("%Y-%m-%d")
        cumulative_invested += inv.amount
        
        investment_growth.append({
            "date": date_str,
            "amount": cumulative_invested
        })
    
    return {
        "customer": {
            "id": customer.id,
            "name": customer.name,
            "email": customer.email,
            "joined_date": customer.created_at.strftime("%Y-%m-%d"),
            "first_investment_date": first_date.strftime("%Y-%m-%d") if first_date else None
        },
        "summary": {
            "total_invested": total_invested,
            "total_current_value": total_current_value,
            "total_return": total_return,
            "investment_count": len(investments),
            "portfolio_count": len(portfolio_data)
        },
        "portfolio_allocation": {
            "labels": list(portfolio_data.keys()),
            "invested": [data["invested"] for name, data in portfolio_data.items()],
            "current_values": [data["current_value"] for name, data in portfolio_data.items()],
            "returns": [data["return_pct"] for name, data in portfolio_data.items()],
            "risk_levels": [data["risk_level"] for name, data in portfolio_data.items()]
        },
        "performance_history": {
            "dates": dates,
            "values": values
        },
        "investment_growth": investment_growth,
        "investments": [
            {
                "id": inv.id,
                "portfolio_id": inv.portfolio_id,
                "portfolio_name": Portfolio.query.get(inv.portfolio_id).name,
                "amount": inv.amount,
                "start_date": inv.start_date.strftime("%Y-%m-%d"),
                "current_value": inv.get_current_value(),
                "return_pct": inv.get_return(),
                "risk_level": Portfolio.query.get(inv.portfolio_id).risk_level
            } for inv in investments
        ]
    }

def get_performance_comparison_data():
    """
    Gets comparative performance data for portfolios
    
    Returns:
        Dictionary with performance comparison data
    """
    portfolios = Portfolio.query.all()
    
    # Get S&P 500 equivalent data for comparison (sample data)
    sp500_dates = []
    sp500_values = []
    
    # In a real application, you would fetch this from a financial API
    # This is sample data for demonstration
    base_value = 1000000
    for i in range(30):
        date = (datetime.now() - timedelta(days=30-i)).strftime("%Y-%m-%d")
        sp500_dates.append(date)
        
        # Add some randomness to simulate market movements
        daily_change = np.random.normal(0.0005, 0.01)  # Mean daily return 0.05%, std 1%
        if i > 0:
            sp500_values.append(sp500_values[-1] * (1 + daily_change))
        else:
            sp500_values.append(base_value)
    
    # Normalize values for comparison (set first day to 100)
    normalized_data = []
    
    # Normalize S&P 500
    norm_sp500 = [100 * val / sp500_values[0] for val in sp500_values]
    
    normalized_data.append({
        "name": "S&P 500",
        "data": norm_sp500,
        "dates": sp500_dates,
        "color": "rgba(128, 128, 128, 1)"  # Grey for benchmark
    })
    
    # Normalize each portfolio
    for portfolio in portfolios:
        snapshots = PortfolioSnapshot.query.filter_by(
            portfolio_id=portfolio.id
        ).order_by(PortfolioSnapshot.date).all()
        
        if snapshots:
            dates = [snap.date.strftime("%Y-%m-%d") for snap in snapshots]
            values = [snap.total_value for snap in snapshots]
            
            # Normalize to 100 at start
            norm_values = [100 * val / values[0] for val in values]
            
            # Get color based on risk level
            color = get_color_for_risk_level(portfolio.risk_level)
            
            normalized_data.append({
                "name": portfolio.name,
                "data": norm_values,
                "dates": dates,
                "color": color
            })
    
    return normalized_data

def get_color_for_risk_level(risk_level, alpha=1):
    """Generate consistent color based on risk level"""
    if risk_level == "High":
        return f"rgba(220, 53, 69, {alpha})"  # Red
    elif risk_level == "Medium":
        return f"rgba(255, 193, 7, {alpha})"  # Yellow
    else:
        return f"rgba(40, 167, 69, {alpha})"  # Green