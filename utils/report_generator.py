import pandas as pd
import matplotlib.pyplot as plt
import os
from flask import current_app, render_template
from datetime import datetime
import io
import base64

from models.investment import Investment
from models.performance import Performance
import matplotlib

from utils.allocation_charts import generate_allocation_chart_as_bytes

matplotlib.use("Agg")
# File: utils/report_generator.py
import pandas as pd
import matplotlib.pyplot as plt
import os
from flask import current_app, render_template
from datetime import datetime
import io
import base64

from models.investment import Investment
from models.performance import Performance
import matplotlib
matplotlib.use("Agg")
from datetime import datetime
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
from reportlab.lib.pagesizes import letter, landscape
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from io import BytesIO



# -----------------------------
# 1. In utils/report_generator.py – modify generate_customer_report
# In utils/report_generator.py
# generate_customer_report uses each investment’s Performance-based get_current_value().

import pandas as pd
import matplotlib.pyplot as plt
import os
from flask import current_app, render_template
from datetime import datetime
import io
import base64

from models.investment import Investment
from models.performance import Performance
import matplotlib

matplotlib.use("Agg")

# Import new performance metrics functions
from utils.performance_metrics import (
    compute_daily_returns,
    compute_volatility,
    compute_sharpe_ratio,
    compute_sortino_ratio,
    compute_max_drawdown,
    compute_twr
)


def generate_performance_chart_as_bytes(dates, values):
    """
    Example of generating a line chart in memory (using matplotlib).
    Replace with your real code that draws the chart you show in HTML.
    """
    import matplotlib.pyplot as plt

    fig, ax = plt.subplots(figsize=(6, 3.5))
    ax.plot(dates, values, marker='o', linewidth=2.0)
    ax.set_title("Total Investment Performance")
    ax.set_xlabel("Date")
    ax.set_ylabel("Value ($)")

    buf = BytesIO()
    plt.savefig(buf, format='png', dpi=100)
    buf.seek(0)
    plt.close(fig)
    return buf


def generate_customer_report(customer_id, report_date=None):
    from datetime import datetime
    from models.investment import Investment

    # Get all investments for display purposes
    all_investments = Investment.query.filter_by(customer_id=customer_id).all()
    if not all_investments:
        return {'status': 'error', 'message': 'No investments found for this customer'}

    # CRITICAL FIX: Filter active and funded investments for calculations
    active_funded_investments = [inv for inv in all_investments if inv.is_active and inv.is_funded]

    # Calculate totals using ONLY active and funded investments
    total_invested = sum(inv.amount for inv in active_funded_investments)
    total_current_value = sum(inv.get_current_value() for inv in active_funded_investments)
    total_return = ((total_current_value - total_invested) / total_invested * 100) if total_invested > 0 else 0

    # Portfolio allocation based only on active funded investments
    portfolio_allocation = {}
    for inv in active_funded_investments:
        pf = inv.portfolio
        portfolio_allocation[pf.name] = portfolio_allocation.get(pf.name, 0) + inv.amount

    for k in portfolio_allocation:
        portfolio_allocation[k] = (portfolio_allocation[k] / total_invested) * 100 if total_invested > 0 else 0

    # Generate charts based on the filtered investments (unchanged)
    performance_chart = generate_performance_chart(customer_id, active_funded_investments)
    allocation_chart = generate_allocation_chart(portfolio_allocation)

    # --- NEW CODE TO INTEGRATE RISK METRICS ---
    # Build an aggregated time series of net values from Performance records
    # (Assume that for each investment we have a set of Performance entries with a 'date' and 'value')
    all_dates = set()
    for inv in active_funded_investments:
        perfs = Performance.query.filter_by(investment_id=inv.id).order_by(Performance.date).all()
        for p in perfs:
            all_dates.add(p.date)
    all_dates = sorted(all_dates)  # sorted list of datetimes

    aggregated_values = []
    for d in all_dates:
        day_total = 0
        for inv in active_funded_investments:
            perf = Performance.query.filter_by(investment_id=inv.id, date=d).first()
            if perf:
                day_total += perf.value
        aggregated_values.append(day_total)

    # If we have enough data, compute risk metrics; otherwise set as None.
    risk_metrics = {}
    if len(aggregated_values) >= 2:
        # Compute daily returns from the aggregated time series
        daily_returns = compute_daily_returns(all_dates, aggregated_values)
        risk_metrics['annual_volatility'] = compute_volatility(daily_returns)
        risk_metrics['sharpe_ratio'] = compute_sharpe_ratio(daily_returns, risk_free_rate=0.02)
        risk_metrics['sortino_ratio'] = compute_sortino_ratio(daily_returns, risk_free_rate=0.02)
        risk_metrics['max_drawdown'] = compute_max_drawdown(aggregated_values)
        risk_metrics['twr'] = compute_twr(aggregated_values)
    else:
        risk_metrics = None
    # --- END NEW CODE ---

    return {
        'status': 'success',
        'customer_id': customer_id,
        'report_date': report_date if report_date else datetime.utcnow(),
        'total_invested': total_invested,
        'total_current_value': total_current_value,
        'total_return_percentage': total_return,
        'portfolio_allocation': portfolio_allocation,
        'performance_chart': performance_chart,
        'allocation_chart': allocation_chart,
        'investments': all_investments,  # full list for display
        'active_investments_count': len(active_funded_investments),
        'risk_metrics': risk_metrics  # New field added to report dictionary
    }


# Update the performance chart generator too
def generate_performance_chart(customer_id, filtered_investments=None):
    import matplotlib.pyplot as plt
    import matplotlib
    matplotlib.use('Agg')
    import io, base64
    from models import Investment, Performance

    if filtered_investments is None:
        # Grab all funded investments for the user
        investments = Investment.query.filter_by(
            customer_id=customer_id,
            is_active=True,
            is_funded=True
        ).all()
    else:
        investments = filtered_investments

    # 1) Gather all performance records for these investments
    perf_by_date = {}  # {date -> total_value_across_all_investments}

    for inv in investments:
        inv_perfs = Performance.query.filter_by(investment_id=inv.id).order_by(Performance.date).all()
        for p in inv_perfs:
            day_str = p.date.strftime('%Y-%m-%d')  # or just use p.date.date()
            perf_by_date.setdefault(day_str, 0)
            perf_by_date[day_str] += p.value  # add this investment's net_value on that date

    # 2) Convert perf_by_date into sorted lists
    sorted_dates = sorted(perf_by_date.keys())
    x_dates = []
    y_values = []

    for day_str in sorted_dates:
        x_dates.append(day_str)
        y_values.append(perf_by_date[day_str])

    # 3) Plot a single line
    plt.figure(figsize=(10, 5))
    plt.plot(x_dates, y_values, label="Combined Investments")
    plt.title('Total Customer Investment Performance Over Time')
    plt.xlabel('Date')
    plt.ylabel('Total Value ($)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.tight_layout()

    # 4) Encode as base64
    buf = io.BytesIO()
    plt.savefig(buf, format='png', dpi=100)
    buf.seek(0)
    img_str = base64.b64encode(buf.read()).decode('utf-8')
    plt.close()

    return f"data:image/png;base64,{img_str}"



def generate_allocation_chart(allocation_data):
    """
    Generate a pie chart for portfolio allocation based on the amounts invested.
    """
    plt.figure(figsize=(8, 8))
    labels = list(allocation_data.keys())
    sizes = list(allocation_data.values())
    plt.pie(sizes, autopct='%1.1f%%', startangle=90, colors=['#50b662', '#ffd97c', '#75cdcd', '#ad84ff', '#ff839d'], textprops={'fontsize': 24})
    plt.axis('equal')
    plt.legend(labels, loc="lower center", prop={'size': 24})
    plt.tight_layout()
    # plt.title('Portfolio Allocation')
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    plt.close()
    return f"data:image/png;base64,{image_base64}"


def generate_investment_report(investment_id, report_date=None):
    """
    Generate a detailed report for a specific investment.
    Uses historical Performance data stored in the database.
    """
    if report_date is None:
        report_date = datetime.utcnow()

    investment = Investment.query.get(investment_id)
    if not investment:
        return {
            'status': 'error',
            'message': f'Investment with ID {investment_id} not found'
        }

    portfolio = investment.portfolio
    allocations = portfolio.allocations.all()
    performances = Performance.query.filter_by(investment_id=investment_id).order_by(Performance.date).all()

    initial_amount = investment.amount
    current_value = investment.get_current_value()
    return_percentage = (current_value - initial_amount) / initial_amount * 100 if initial_amount > 0 else 0

    plt.figure(figsize=(10, 6))
    performance_dates = [p.date for p in performances]
    performance_values = [p.value for p in performances]
    plt.plot(performance_dates, performance_values)
    plt.title(f'Investment Performance - {portfolio.name}')
    plt.xlabel('Date')
    plt.ylabel('Value ($)')
    plt.grid(True)
    perf_buffer = io.BytesIO()
    plt.savefig(perf_buffer, format='png')
    perf_buffer.seek(0)
    perf_image_base64 = base64.b64encode(perf_buffer.getvalue()).decode('utf-8')
    plt.close()

    allocation_labels = [a.security.name for a in allocations]
    allocation_sizes = [a.percentage for a in allocations]
    plt.figure(figsize=(8, 8))
    plt.pie(allocation_sizes, labels=allocation_labels, autopct='%1.1f%%', startangle=90)
    plt.axis('equal')
    plt.title(f'Portfolio Allocation - {portfolio.name}')
    alloc_buffer = io.BytesIO()
    plt.savefig(alloc_buffer, format='png')
    alloc_buffer.seek(0)
    alloc_image_base64 = base64.b64encode(alloc_buffer.getvalue()).decode('utf-8')
    plt.close()

    return {
        'status': 'success',
        'investment_id': investment_id,
        'report_date': report_date,
        'portfolio_name': portfolio.name,
        'initial_amount': initial_amount,
        'current_value': current_value,
        'return_percentage': return_percentage,
        'performance_chart': f"data:image/png;base64,{perf_image_base64}",
        'allocation_chart': f"data:image/png;base64,{alloc_image_base64}",
        'allocations': allocations,
        'performances': performances
    }


def export_customer_data_to_csv(customer_id):
    """
    Export customer investment data and performance history to CSV.
    """
    investments = Investment.query.filter_by(customer_id=customer_id).all()
    investment_data = []
    for inv in investments:
        investment_data.append({
            'Investment ID': inv.id,
            'Portfolio': inv.portfolio.name,
            'Amount': inv.amount,
            'Start Date': inv.start_date,
            'End Date': inv.end_date,
            'Current Value': inv.get_current_value(),
            'Return %': inv.get_return(),
            'Status': 'Active' if inv.is_active else 'Closed'
        })
    investments_df = pd.DataFrame(investment_data)

    performance_data = []
    for inv in investments:
        performances = Performance.query.filter_by(investment_id=inv.id).order_by(Performance.date).all()
        for perf in performances:
            performance_data.append({
                'Investment ID': inv.id,
                'Portfolio': inv.portfolio.name,
                'Date': perf.date,
                'Value': perf.value,
                'Return %': perf.return_pct
            })
    performances_df = pd.DataFrame(performance_data)

    output = io.StringIO()
    output.write("# Investment Summary\n")
    investments_df.to_csv(output, index=False)
    output.write("\n\n# Performance History\n")
    performances_df.to_csv(output, index=False)
    return output.getvalue()
