import unittest
import os
import tempfile
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta
import math

from app import create_app, db
from models.models_customer import Customer
from models.portfolio import Portfolio
from models.investment import Investment
from models.performance import Performance, PortfolioSnapshot
from utils.performance_calculator import calculate_customer_performance


class SimpleInvestmentPlotTest(unittest.TestCase):
    """Test class for investment calculations and plot generation"""

    def setUp(self):
        """Create a test environment with 2 customers, 2 portfolios, and 4 investments"""
        # Initialize test app
        self.app = create_app('testing')
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'

        # Disable fees for cleaner calculation
        self.app.config['MANAGEMENT_FEE_YEARLY'] = 0.0
        self.app.config['PERFORMANCE_FEE_RATE'] = 0.0
        self.app.config['BROKERAGE_FEE_DAILY'] = 0.0

        # Set up app context
        self.app_context = self.app.app_context()
        self.app_context.push()

        # Create a fresh database
        db.drop_all()
        db.create_all()

        # Create customers
        self.customer1 = Customer(name="<PERSON>", email="<EMAIL>")
        self.customer1.set_password("password1")

        self.customer2 = Customer(name="Bob Smith", email="<EMAIL>")
        self.customer2.set_password("password2")

        db.session.add_all([self.customer1, self.customer2])
        db.session.commit()

        # Create portfolios
        self.portfolio1 = Portfolio(
            name="Growth Fund",
            description="High growth potential with higher volatility",
            risk_level="High"
        )

        self.portfolio2 = Portfolio(
            name="Balanced Fund",
            description="Moderate growth with reduced volatility",
            risk_level="Medium"
        )

        db.session.add_all([self.portfolio1, self.portfolio2])
        db.session.commit()

        # Define growth rates and base values for predictable testing
        self.today = datetime.now()
        self.growth_daily_rate = 0.02  # 2% daily growth
        self.balanced_daily_rate = 0.01  # 1% daily growth
        self.growth_base_value = 1000000.0
        self.balanced_base_value = 800000.0

        # Create 5 days of portfolio snapshots
        for days_ago in range(4, -1, -1):  # 4, 3, 2, 1, 0 days ago
            snapshot_date = self.today - timedelta(days=days_ago)

            # Growth Fund increases by 2% per day
            growth_value = self.growth_base_value * ((1 + self.growth_daily_rate) ** (4 - days_ago))
            growth_snapshot = PortfolioSnapshot(
                portfolio_id=self.portfolio1.id,
                date=snapshot_date,
                total_value=growth_value
            )

            # Balanced Fund increases by 1% per day
            balanced_value = self.balanced_base_value * ((1 + self.balanced_daily_rate) ** (4 - days_ago))
            balanced_snapshot = PortfolioSnapshot(
                portfolio_id=self.portfolio2.id,
                date=snapshot_date,
                total_value=balanced_value
            )

            db.session.add_all([growth_snapshot, balanced_snapshot])

        db.session.commit()

        # Create investments
        self.start_date = self.today - timedelta(days=4)

        # Alice's investments
        self.alice_growth = Investment(
            customer_id=self.customer1.id,
            portfolio_id=self.portfolio1.id,
            amount=200000.0,
            start_date=self.start_date,
            is_active=True,
            is_funded=True,
            entry_nav=1000.0,
            units=200.0  # 200,000 / 1000 = 200 units
        )

        self.alice_balanced = Investment(
            customer_id=self.customer1.id,
            portfolio_id=self.portfolio2.id,
            amount=150000.0,
            start_date=self.start_date + timedelta(days=1),  # One day later
            is_active=True,
            is_funded=True,
            entry_nav=1000.0,
            units=150.0
        )

        # Bob's investments
        self.bob_growth = Investment(
            customer_id=self.customer2.id,
            portfolio_id=self.portfolio1.id,
            amount=300000.0,
            start_date=self.start_date + timedelta(days=2),  # Two days later
            is_active=True,
            is_funded=True,
            entry_nav=1000.0,
            units=300.0
        )

        self.bob_balanced = Investment(
            customer_id=self.customer2.id,
            portfolio_id=self.portfolio2.id,
            amount=250000.0,
            start_date=self.start_date,
            is_active=True,
            is_funded=True,
            entry_nav=1000.0,
            units=250.0
        )

        db.session.add_all([
            self.alice_growth, self.alice_balanced,
            self.bob_growth, self.bob_balanced
        ])
        db.session.commit()

    def tearDown(self):
        """Clean up resources"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

        # Clean up plot files
        if hasattr(self, 'plots_dir') and os.path.exists(self.plots_dir):
            import shutil
            shutil.rmtree(self.plots_dir)

    def generate_customer_plots(self, customer_id, output_dir='plots', show_plots=False):
        """Generate investment plots for a customer"""
        import matplotlib.pyplot as plt
        import matplotlib.ticker as mticker
        import numpy as np
        from datetime import datetime, timedelta

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Get customer data
        customer = db.session.get(Customer, customer_id)
        if not customer:
            return {"error": "Customer not found"}

        # Get investments for the customer
        investments = Investment.query.filter_by(
            customer_id=customer_id, is_active=True, is_funded=True
        ).all()

        if not investments:
            return {"error": f"No active investments found for {customer.name}"}

        plot_files = {}

        # Set plotting style
        plt.style.use('seaborn-v0_8-darkgrid')

        # 1. Portfolio Allocation Pie Chart
        fig, ax = plt.subplots(figsize=(10, 7))

        # Group investments by portfolio
        portfolio_data = {}
        for inv in investments:
            portfolio_name = inv.portfolio.name
            if portfolio_name in portfolio_data:
                portfolio_data[portfolio_name] += inv.amount
            else:
                portfolio_data[portfolio_name] = inv.amount

        # Create the pie chart
        labels = list(portfolio_data.keys())
        sizes = list(portfolio_data.values())

        # Calculate percentages for labels
        total = sum(sizes)
        percentages = [(size / total) * 100 for size in sizes]

        # Custom labels with portfolio name, amount and percentage
        custom_labels = [f"{label}\n${size:,.0f} ({pct:.1f}%)"
                         for label, size, pct in zip(labels, sizes, percentages)]

        # Color map
        colors = plt.cm.tab10(np.linspace(0, 1, len(sizes)))
        explode = [0.05] * len(sizes)  # Explode all slices slightly

        wedges, texts = ax.pie(
            sizes,
            explode=explode,
            colors=colors,
            shadow=True,
            startangle=90,
            wedgeprops={'edgecolor': 'white', 'linewidth': 2}
        )

        # Add labels with lines connecting to slices
        ax.legend(wedges, custom_labels, loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))

        plt.title(f"{customer.name}'s Portfolio Allocation", fontsize=18, pad=20)

        allocation_file = os.path.join(output_dir, f"customer_{customer_id}_allocation.png")
        plt.savefig(allocation_file, dpi=150, bbox_inches='tight')
        if show_plots:
            plt.show()
        plt.close()
        plot_files["allocation"] = allocation_file

        # 2. Investment Growth Line Chart
        fig, ax = plt.subplots(figsize=(12, 7))

        # Track maximum date range for all investments
        min_date = datetime(2100, 1, 1)  # Start with future date
        max_date = datetime(2000, 1, 1)  # Start with past date

        for i, inv in enumerate(investments):
            # Get performance data
            performances = Performance.query.filter_by(investment_id=inv.id).order_by(Performance.date).all()

            if performances:
                dates = [p.date for p in performances]
                values = [p.value for p in performances]

                # Update date range
                if dates[0] < min_date:
                    min_date = dates[0]
                if dates[-1] > max_date:
                    max_date = dates[-1]

                # Plot with area and custom styles
                color = plt.cm.tab10(i)
                ax.plot(dates, values, marker='o', linewidth=2.5, color=color,
                        label=f"{inv.portfolio.name} (${inv.amount:,.0f})")
                ax.fill_between(dates, values, alpha=0.2, color=color)

                # Add ending value annotation
                if dates and values:
                    ax.annotate(
                        f"${values[-1]:,.0f}",
                        xy=(dates[-1], values[-1]),
                        xytext=(5, 5),
                        textcoords='offset points',
                        fontsize=10,
                        color=color
                    )

        # Add initial investment as reference line
        if min_date < max_date:
            for i, inv in enumerate(investments):
                color = plt.cm.tab10(i)
                ax.axhline(y=inv.amount, color=color, linestyle='--', alpha=0.5)
                ax.text(min_date, inv.amount, f"Initial: ${inv.amount:,.0f}",
                        va='bottom', ha='left', fontsize=9, color=color)

        # Format axes
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Value ($)', fontsize=12)
        ax.set_title(f"{customer.name}'s Investment Growth Over Time", fontsize=16)
        ax.grid(True, alpha=0.3)

        # Format y-axis as currency
        formatter = mticker.StrMethodFormatter('${x:,.0f}')
        ax.yaxis.set_major_formatter(formatter)

        # Enhance legend
        ax.legend(loc='upper left', frameon=True, fontsize=11)

        plt.tight_layout()

        growth_file = os.path.join(output_dir, f"customer_{customer_id}_growth.png")
        plt.savefig(growth_file, dpi=150, bbox_inches='tight')
        if show_plots:
            plt.show()
        plt.close()
        plot_files["growth"] = growth_file

        # 3. Performance Summary Dashboard
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 7))

        # Left side: Bar chart comparing initial vs current values
        portfolio_names = []
        amounts = []
        returns = []
        current_values = []

        for inv in investments:
            portfolio_names.append(inv.portfolio.name)
            amounts.append(inv.amount)
            current_values.append(inv.get_current_value())
            returns.append(inv.get_return())

        # Calculate x positions for grouped bars
        indices = np.arange(len(portfolio_names))
        bar_width = 0.35

        # Create grouped bar chart
        bars1 = ax1.bar(indices - bar_width / 2, amounts, bar_width,
                        label='Initial Investment', color='steelblue')
        bars2 = ax1.bar(indices + bar_width / 2, current_values, bar_width,
                        label='Current Value', color='seagreen')

        # Add return percentage on top of the bars
        for i, (amt, val, ret) in enumerate(zip(amounts, current_values, returns)):
            # Determine text position and color
            if val > amt:
                color = 'darkgreen'
                va = 'bottom'
                y_pos = val + (val * 0.02)  # Slightly above the bar
            else:
                color = 'darkred'
                va = 'top'
                y_pos = val - (val * 0.02)  # Slightly below the bar

            ax1.text(indices[i], y_pos, f"{ret:.1f}%",
                     ha='center', va=va, fontweight='bold', color=color)

        # Customize the plot
        ax1.set_ylabel('Amount ($)', fontsize=12)
        ax1.set_title('Investment Performance', fontsize=14)
        ax1.set_xticks(indices)
        ax1.set_xticklabels(portfolio_names)
        ax1.legend()

        # Format y-axis as currency
        ax1.yaxis.set_major_formatter(mticker.StrMethodFormatter('${x:,.0f}'))

        # Right side: Pie chart showing allocation
        wedges, _ = ax2.pie(
            sizes,
            explode=explode,
            colors=colors,
            shadow=True,
            startangle=90,
            wedgeprops={'edgecolor': 'white', 'linewidth': 1.5}
        )

        # Add percentages directly on the pie slices
        for i, p in enumerate(wedges):
            ang = (p.theta2 - p.theta1) / 2. + p.theta1
            x = p.r * 0.8 * np.cos(np.deg2rad(ang))
            y = p.r * 0.8 * np.sin(np.deg2rad(ang))
            ax2.text(x, y, f"{percentages[i]:.1f}%", ha='center', va='center', fontsize=11,
                     weight='bold', color='white')

        ax2.legend(wedges, labels, loc="center", bbox_to_anchor=(0.5, -0.1))
        ax2.set_title('Portfolio Allocation', fontsize=14)

        plt.suptitle(f"{customer.name}'s Investment Summary", fontsize=18, y=0.98)
        plt.tight_layout(rect=[0, 0, 1, 0.95])

        dashboard_file = os.path.join(output_dir, f"customer_{customer_id}_dashboard.png")
        plt.savefig(dashboard_file, dpi=150, bbox_inches='tight')
        if show_plots:
            plt.show()
        plt.close()
        plot_files["dashboard"] = dashboard_file

        return plot_files

    def calculate_expected_value(self, investment, as_of_date=None):
        """Calculate the expected value of an investment based on growth rates"""
        if as_of_date is None:
            as_of_date = self.today

        # Determine which portfolio and its growth rate
        if investment.portfolio_id == self.portfolio1.id:  # Growth fund
            daily_growth_rate = self.growth_daily_rate
        else:  # Balanced fund
            daily_growth_rate = self.balanced_daily_rate

        # Calculate days between start date and as_of_date
        days_invested = (as_of_date - investment.start_date).days
        if days_invested < 0:
            return investment.amount  # Not yet invested

        # Calculate expected value based on compound growth
        expected_value = investment.amount * ((1 + daily_growth_rate) ** days_invested)

        return expected_value

    def test_portfolio_snapshots(self):
        """Test that portfolio snapshots are created with correct values"""
        # Get the latest snapshot for Growth Fund
        growth_snapshot = PortfolioSnapshot.query.filter_by(
            portfolio_id=self.portfolio1.id
        ).order_by(PortfolioSnapshot.date.desc()).first()

        # Verify it exists
        self.assertIsNotNone(growth_snapshot, "No snapshot found for Growth Fund")

        # Calculate expected value: 1,000,000 * (1.02^4)
        expected_value = self.growth_base_value * ((1 + self.growth_daily_rate) ** 4)

        # Test with a small tolerance for floating point precision
        self.assertAlmostEqual(
            growth_snapshot.total_value,
            expected_value,
            delta=0.01,
            msg=f"Growth Fund snapshot value incorrect. Expected: {expected_value}, Got: {growth_snapshot.total_value}"
        )

        # Get the latest snapshot for Balanced Fund
        balanced_snapshot = PortfolioSnapshot.query.filter_by(
            portfolio_id=self.portfolio2.id
        ).order_by(PortfolioSnapshot.date.desc()).first()

        # Verify it exists
        self.assertIsNotNone(balanced_snapshot, "No snapshot found for Balanced Fund")

        # Calculate expected value: 800,000 * (1.01^4)
        expected_value = self.balanced_base_value * ((1 + self.balanced_daily_rate) ** 4)

        # Test with a small tolerance for floating point precision
        self.assertAlmostEqual(
            balanced_snapshot.total_value,
            expected_value,
            delta=0.01,
            msg=f"Balanced Fund snapshot value incorrect. Expected: {expected_value}, Got: {balanced_snapshot.total_value}"
        )

    def test_investment_performance(self):
        """Test investment performance calculations"""
        # Create performance records for all investments
        investments = [
            self.alice_growth, self.alice_balanced,
            self.bob_growth, self.bob_balanced
        ]

        # Make sure performances are calculated for all investments
        for inv in investments:
            # Create performance records for each day
            for days_ago in range(4, -1, -1):
                perf_date = self.today - timedelta(days=days_ago)

                # Skip if investment hasn't started yet
                if perf_date < inv.start_date:
                    continue

                # Calculate expected value based on our growth formulas
                expected_value = self.calculate_expected_value(inv, perf_date)

                # Create performance record
                perf = Performance(
                    investment_id=inv.id,
                    date=perf_date,
                    value=expected_value,
                    return_pct=((expected_value - inv.amount) / inv.amount * 100)
                )
                db.session.add(perf)

        db.session.commit()

        # Test Alice's Growth Fund investment performance
        # Expected value: Initial $200,000 growing at 2% daily for 4 days
        alice_growth_expected = self.calculate_expected_value(self.alice_growth)
        alice_growth_actual = self.alice_growth.get_current_value()

        self.assertAlmostEqual(
            alice_growth_actual,
            alice_growth_expected,
            delta=0.01,
            msg=f"Alice's Growth Fund value incorrect. Expected: {alice_growth_expected}, Got: {alice_growth_actual}"
        )

        # Test Alice's Balanced Fund investment performance (started 1 day later)
        # Expected value: Initial $150,000 growing at 1% daily for 3 days
        alice_balanced_expected = self.calculate_expected_value(self.alice_balanced)
        alice_balanced_actual = self.alice_balanced.get_current_value()

        self.assertAlmostEqual(
            alice_balanced_actual,
            alice_balanced_expected,
            delta=0.01,
            msg=f"Alice's Balanced Fund value incorrect. Expected: {alice_balanced_expected}, Got: {alice_balanced_actual}"
        )

        # Test Bob's Growth Fund investment performance (started 2 days later)
        # Expected value: Initial $300,000 growing at 2% daily for 2 days
        bob_growth_expected = self.calculate_expected_value(self.bob_growth)
        bob_growth_actual = self.bob_growth.get_current_value()

        self.assertAlmostEqual(
            bob_growth_expected,
            bob_growth_actual,
            delta=0.01,
            msg=f"Bob's Growth Fund value incorrect. Expected: {bob_growth_expected}, Got: {bob_growth_actual}"
        )

        # Test Bob's Balanced Fund investment performance
        # Expected value: Initial $250,000 growing at 1% daily for 4 days
        bob_balanced_expected = self.calculate_expected_value(self.bob_balanced)
        bob_balanced_actual = self.bob_balanced.get_current_value()

        self.assertAlmostEqual(
            bob_balanced_expected,
            bob_balanced_actual,
            delta=0.01,
            msg=f"Bob's Balanced Fund value incorrect. Expected: {bob_balanced_expected}, Got: {bob_balanced_actual}"
        )

        # Test overall calculations
        alice_total_invested = self.alice_growth.amount + self.alice_balanced.amount
        alice_total_value = self.alice_growth.get_current_value() + self.alice_balanced.get_current_value()
        alice_return_pct = ((alice_total_value - alice_total_invested) / alice_total_invested * 100)

        print(f"\nAlice's total invested: ${alice_total_invested:,.2f}")
        print(f"Alice's total current value: ${alice_total_value:,.2f}")
        print(f"Alice's overall return: {alice_return_pct:.2f}%")

        bob_total_invested = self.bob_growth.amount + self.bob_balanced.amount
        bob_total_value = self.bob_growth.get_current_value() + self.bob_balanced.get_current_value()
        bob_return_pct = ((bob_total_value - bob_total_invested) / bob_total_invested * 100)

        print(f"\nBob's total invested: ${bob_total_invested:,.2f}")
        print(f"Bob's total current value: ${bob_total_value:,.2f}")
        print(f"Bob's overall return: {bob_return_pct:.2f}%")

        # Verify investment return percentages
        alice_growth_return = self.alice_growth.get_return()
        alice_growth_expected_return = (
                    (alice_growth_expected - self.alice_growth.amount) / self.alice_growth.amount * 100)

        self.assertAlmostEqual(
            alice_growth_return,
            alice_growth_expected_return,
            delta=0.1,
            msg=f"Alice's Growth Fund return incorrect. Expected: {alice_growth_expected_return}%, Got: {alice_growth_return}%"
        )

    def test_plot_generation(self):
        """Test generation of investment plots with data verification"""
        # Ensure we have performance data
        self.test_investment_performance()

        # Create plots directory
        self.plots_dir = os.path.join(os.path.dirname(__file__), "test_investment_plots")
        if not os.path.exists(self.plots_dir):
            os.makedirs(self.plots_dir)

        print(f"\nGenerating investment plots to: {os.path.abspath(self.plots_dir)}")

        # Generate plots for each customer
        customers = [self.customer1, self.customer2]
        for customer in customers:
            print(f"\nGenerating plots for {customer.name}...")
            plots = self.generate_customer_plots(
                customer.id,
                output_dir=self.plots_dir,
                show_plots=False
            )

            # Verify plots were generated successfully
            self.assertIsInstance(plots, dict, f"Plot generation failed for {customer.name}")
            self.assertNotIn("error", plots, f"Error generating plots for {customer.name}")

            # Verify all required plot types were created
            expected_plots = ["allocation", "growth", "dashboard"]
            for plot_type in expected_plots:
                self.assertIn(plot_type, plots, f"Missing {plot_type} plot for {customer.name}")

                # Verify the files exist and have content
                plot_path = plots[plot_type]
                self.assertTrue(os.path.exists(plot_path), f"Plot file {plot_path} does not exist")
                self.assertTrue(os.path.getsize(plot_path) > 10000,
                                f"Plot file {plot_path} is too small, might be empty")

                print(f"  {plot_type}: {os.path.abspath(plot_path)}")

        # Verify Alice's allocation data
        alice_growth_pct = (self.alice_growth.amount / (self.alice_growth.amount + self.alice_balanced.amount) * 100)
        alice_balanced_pct = (
                    self.alice_balanced.amount / (self.alice_growth.amount + self.alice_balanced.amount) * 100)

        print(f"\nAlice's allocation:")
        print(f"  Growth Fund: {alice_growth_pct:.2f}%")
        print(f"  Balanced Fund: {alice_balanced_pct:.2f}%")

        # Verify sum is 100%
        self.assertAlmostEqual(
            alice_growth_pct + alice_balanced_pct,
            100.0,
            delta=0.01,
            msg="Alice's allocation percentages don't sum to 100%"
        )

        # Verify Bob's allocation data
        bob_growth_pct = (self.bob_growth.amount / (self.bob_growth.amount + self.bob_balanced.amount) * 100)
        bob_balanced_pct = (self.bob_balanced.amount / (self.bob_growth.amount + self.bob_balanced.amount) * 100)

        print(f"\nBob's allocation:")
        print(f"  Growth Fund: {bob_growth_pct:.2f}%")
        print(f"  Balanced Fund: {bob_balanced_pct:.2f}%")

        # Verify sum is 100%
        self.assertAlmostEqual(
            bob_growth_pct + bob_balanced_pct,
            100.0,
            delta=0.01,
            msg="Bob's allocation percentages don't sum to 100%"
        )

        print("\nAll tests passed! Plots generated successfully.")

        # Create an HTML report for easy viewing (optional)
        report_path = os.path.join(os.path.dirname(__file__), "test_investment_report.html")
        self.create_plot_report(self.plots_dir, report_path)
        print(f"HTML report created at: {os.path.abspath(report_path)}")

    def create_plot_report(self, plots_dir, output_file='investment_plots_report.html'):
        """Create an HTML report with all the generated plots"""
        with open(output_file, 'w') as f:
            f.write('''<!DOCTYPE html>
<html>
<head>
    <title>Investment Test Results</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
            padding: 10px;
            background-color: #eaf6ff;
            border-radius: 5px;
        }
        .plot-container {
            margin: 20px 0;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        img {
            max-width: 100%;
            display: block;
            margin: 0 auto;
        }
        .plot-title {
            color: #34495e;
            font-size: 1.2em;
            margin: 10px 0;
            text-align: center;
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 40px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .positive {
            color: green;
            font-weight: bold;
        }
        .test-summary {
            background-color: #e8f7f2;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Investment Test Results</h1>

    <div class="test-summary">
        <h2>Test Summary</h2>
        <p>This report shows the results of investment performance tests and the generated visualization plots.</p>
        <table>
            <tr>
                <th>Customer</th>
                <th>Investment</th>
                <th>Initial Amount</th>
                <th>Current Value</th>
                <th>Return</th>
            </tr>
''')

            # Add test results table
            alice_growth_value = self.alice_growth.get_current_value()
            alice_growth_return = self.alice_growth.get_return()

            alice_balanced_value = self.alice_balanced.get_current_value()
            alice_balanced_return = self.alice_balanced.get_return()

            bob_growth_value = self.bob_growth.get_current_value()
            bob_growth_return = self.bob_growth.get_return()

            bob_balanced_value = self.bob_balanced.get_current_value()
            bob_balanced_return = self.bob_balanced.get_return()

            f.write(f'''
            <tr>
                <td rowspan="2">Alice Johnson</td>
                <td>Growth Fund</td>
                <td>${self.alice_growth.amount:,.2f}</td>
                <td>${alice_growth_value:,.2f}</td>
                <td class="positive">{alice_growth_return:.2f}%</td>
            </tr>
            <tr>
                <td>Balanced Fund</td>
                <td>${self.alice_balanced.amount:,.2f}</td>
                <td>${alice_balanced_value:,.2f}</td>
                <td class="positive">{alice_balanced_return:.2f}%</td>
            </tr>
            <tr>
                <td rowspan="2">Bob Smith</td>
                <td>Growth Fund</td>
                <td>${self.bob_growth.amount:,.2f}</td>
                <td>${bob_growth_value:,.2f}</td>
                <td class="positive">{bob_growth_return:.2f}%</td>
            </tr>
            <tr>
                <td>Balanced Fund</td>
                <td>${self.bob_balanced.amount:,.2f}</td>
                <td>${bob_balanced_value:,.2f}</td>
                <td class="positive">{bob_balanced_return:.2f}%</td>
            </tr>
            ''')

            # Add totals
            alice_total_invested = self.alice_growth.amount + self.alice_balanced.amount
            alice_total_value = alice_growth_value + alice_balanced_value
            alice_return_pct = ((alice_total_value - alice_total_invested) / alice_total_invested * 100)

            bob_total_invested = self.bob_growth.amount + self.bob_balanced.amount
            bob_total_value = bob_growth_value + bob_balanced_value
            bob_return_pct = ((bob_total_value - bob_total_invested) / bob_total_invested * 100)

            f.write(f'''
            <tr style="font-weight: bold; background-color: #f0f0f0;">
                <td>Alice Total</td>
                <td></td>
                <td>${alice_total_invested:,.2f}</td>
                <td>${alice_total_value:,.2f}</td>
                <td class="positive">{alice_return_pct:.2f}%</td>
            </tr>
            <tr style="font-weight: bold; background-color: #f0f0f0;">
                <td>Bob Total</td>
                <td></td>
                <td>${bob_total_invested:,.2f}</td>
                <td>${bob_total_value:,.2f}</td>
                <td class="positive">{bob_return_pct:.2f}%</td>
            </tr>
            ''')

            f.write('''
        </table>
    </div>
''')

            # List all customers with their plots
            customer_plots = {}
            for item in os.listdir(plots_dir):
                if item.startswith('customer_') and item.endswith('.png'):
                    parts = item.split('_')
                    if len(parts) >= 2:
                        customer_id = parts[1]
                        if customer_id not in customer_plots:
                            customer_plots[customer_id] = []
                        customer_plots[customer_id].append(item)

            # Sort by customer ID
            for customer_id in sorted(customer_plots.keys()):
                # Get customer name
                customer = db.session.get(Customer, int(customer_id))
                customer_name = customer.name if customer else f"Customer {customer_id}"

                f.write(f'<h2>{customer_name}</h2>\n')

                # Group plots by type
                plot_types = {
                    'allocation': 'Portfolio Allocation',
                    'growth': 'Investment Growth',
                    'dashboard': 'Investment Dashboard'
                }

                for plot_file in sorted(customer_plots[customer_id]):
                    plot_path = os.path.join(plots_dir, plot_file)

                    # Determine plot type from filename
                    plot_type = None
                    for key in plot_types:
                        if key in plot_file:
                            plot_type = plot_types[key]
                            break

                    if not plot_type:
                        plot_type = 'Investment Plot'

                    f.write(f'<div class="plot-container">\n')
                    f.write(f'<div class="plot-title">{plot_type}</div>\n')

                    # Convert file path to data URI to avoid file system issues
                    import base64
                    with open(plot_path, 'rb') as img_file:
                        img_data = base64.b64encode(img_file.read()).decode('utf-8')
                        f.write(f'<img src="data:image/png;base64,{img_data}" alt="{plot_type}" />\n')

                    f.write('</div>\n')

            # Add timestamp
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f'<div class="timestamp">Generated on {timestamp}</div>\n')

            f.write('</body>\n</html>')

        # Try to open the report in browser
        import webbrowser
        try:
            webbrowser.open('file://' + os.path.abspath(output_file))
        except:
            print(f"Could not automatically open the browser. Please open {os.path.abspath(output_file)} manually.")


if __name__ == '__main__':
    unittest.main()