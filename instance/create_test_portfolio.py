from app import create_app
from models import db, Portfolio, Security, PortfolioAllocation

app = create_app()
with app.app_context():
    # Add a test portfolio
    portfolio = Portfolio(
        name="Test Portfolio",
        description="A test portfolio",
        risk_level="Medium"
    )
    db.session.add(portfolio)

    # Check if the ETFs already exist, if not add them
    etfs = [
        {"symbol": "SPUS", "name": "SP Funds S&P 500 Sharia Industry Exclusions ETF", "type": "ETF"},
        {"symbol": "SPWO", "name": "SP Funds Dow Jones Global Sukuk ETF", "type": "ETF"}
    ]

    for etf in etfs:
        existing = Security.query.filter_by(symbol=etf["symbol"]).first()
        if not existing:
            security = Security(
                symbol=etf["symbol"],
                name=etf["name"],
                type=etf["type"]
            )
            db.session.add(security)

    db.session.commit()

    # Add allocations
    securities = Security.query.filter(Security.symbol.in_(["SPUS", "SPWO"])).all()

    for security in securities:
        allocation = PortfolioAllocation(
            portfolio_id=portfolio.id,
            security_id=security.id,
            percentage=50  # 50% allocation to each ETF
        )
        db.session.add(allocation)

    db.session.commit()

    print("Test portfolio created successfully!")