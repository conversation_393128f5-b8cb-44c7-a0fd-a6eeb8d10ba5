import sqlite3

# Replace 'your_database.db' with your database file name
import os.path
conn = sqlite3.connect('./instance/portfolio_dashboard.db')
cursor = conn.cursor()

# Retrieve all table names in the database
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

if tables:
    for table in tables:
        table_name = table[0]
        print(f"Data from table: {table_name}")

        # Fetch all data from the current table
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()

        for row in rows:
            print(row)
        print("-" * 40)  # Divider for readability
else:
    print("No tables found in the database!")

# Close the connection
conn.close()
