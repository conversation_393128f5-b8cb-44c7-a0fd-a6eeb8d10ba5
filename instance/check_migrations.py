# add_columns.py
import sqlite3
import os

# Determine the database path (adjust as needed)
db_path = os.path.join(os.getcwd(), 'instance', 'app.db')

# Check if file exists at common locations
if not os.path.exists(db_path):
    db_path = 'portfolio_dashboard.db'
    if not os.path.exists(db_path):
        db_path = 'app.db'
        if not os.path.exists(db_path):
            print("Database file not found. Please provide the correct path.")
            exit(1)

print(f"Using database at: {db_path}")

# Connect to the database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

try:
    # Check if columns already exist
    print("Checking existing columns...")
    cursor.execute("PRAGMA table_info(investments)")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]

    print(f"Existing columns: {column_names}")

    if 'units' not in column_names:
        print("Adding 'units' column...")
        cursor.execute("ALTER TABLE investments ADD COLUMN units REAL")
    else:
        print("Column 'units' already exists.")

    if 'entry_nav' not in column_names:
        print("Adding 'entry_nav' column...")
        cursor.execute("ALTER TABLE investments ADD COLUMN entry_nav REAL")
    else:
        print("Column 'entry_nav' already exists.")

    conn.commit()
    print("Database updated successfully!")
except Exception as e:
    print(f"Error updating database: {e}")
finally:
    conn.close()