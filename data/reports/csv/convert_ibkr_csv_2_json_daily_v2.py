import csv
import json
from datetime import datetime
import os
from pathlib import Path
import re
from flask import current_app


def convert_ibkr_activity_csv_to_json(file_path: str, output_path: str = None):
    """
    Parses an IBKR Activity Statement CSV where each line has this general form:
       Section,RecordType,<columns...>
    Example sections: "Statement", "Account Information", "Net Asset Value", etc.
    Example record types: "Header", "Data", possibly "SubTotal", "Total", etc.

    We create a JSON with a top-level dictionary 'sections', whose keys are the
    names from column 0 (e.g. "Account Information", "Net Asset Value"). Within each
    section, we store a list of 'tables'. Each table is introduced by a line whose
    RecordType is 'Header' and holds the column headers. The subsequent lines with
    RecordType == 'Data' (or anything other than 'Header') are placed into that table
    as rows.

    If the same section has multiple 'Header' lines, we assume multiple sub-tables.

    By doing this, we capture all lines, even if IBKR changes or expands the format.
    """

    # Prepare a top-level JSON structure
    report_data = {
        "metadata": {
            "report_date": datetime.now().strftime("%Y-%m-%d"),
            "file_name": Path(file_path).name,
            "source": "Interactive Brokers"
        },
        # We'll store everything else in sections
        "sections": {}
    }

    with open(file_path, mode='r', encoding='utf-8-sig', newline='') as csv_file:
        reader = csv.reader(csv_file)
        # The IBKR activity statement often has a BOM; we use 'utf-8-sig' to handle that

        # For each section, we keep a list of tables. Each table has:
        #   { "headers": [...], "rows": [ {...}, {...} ] }
        # We'll track the *current* table for each section as we parse lines.
        # If we encounter a new "Header" line, we start a new table in that section.
        current_tables = {}  # maps section_name -> [list_of_tables]

        for row in reader:
            if not row:
                continue

            section = row[0].strip()
            record_type = row[1].strip() if len(row) > 1 else ""

            # The rest of the columns after the first two are what we'll parse differently
            content = row[2:] if len(row) > 2 else []

            # Make sure there's a place in the dictionary for this section
            if section not in current_tables:
                current_tables[section] = []

            if record_type == "Header":
                # Start a new "table" under this section
                new_table = {
                    "headers": content,  # we'll store these directly as a list
                    "rows": []
                }
                current_tables[section].append(new_table)

            else:
                # This is typically "Data", "Total", "SubTotal", etc. We treat them all
                # as row data lines, belonging to the last table in this section
                if not current_tables[section]:
                    # If there's no table started yet, let's create a placeholder
                    # with no headers. We'll handle it anyway so we don't lose data.
                    current_tables[section].append({
                        "headers": [],
                        "rows": []
                    })

                # The "active" table is the last one in current_tables[section]
                active_table = current_tables[section][-1]

                # Build a dict from content. If we have fewer columns than headers, some
                # fields may remain empty. If we have more columns than headers, we'll
                # store them in "extra_col_X".
                row_dict = {}
                num_headers = len(active_table["headers"])
                for i, val in enumerate(content):
                    if i < num_headers and active_table["headers"][i]:
                        col_name = active_table["headers"][i]
                        row_dict[col_name] = maybe_to_float(row_dict[col_name])
                    else:
                        # If there's no header for this column, store it in an "extra" key
                        row_dict[f"extra_col_{i}"] = val.strip()

                # We might also want to store record_type in each row, just to keep it
                # so we know if it was "Data", "SubTotal", "Total", etc.
                row_dict["_record_type"] = record_type

                # Add the row to the table
                active_table["rows"].append(row_dict)

        # Move all the tables into our final structure
        # We'll store them under: report_data["sections"][<section_name>]["tables"] = [...]
        for section_name, tables in current_tables.items():
            report_data["sections"][section_name] = {
                "tables": tables
            }

    # Finally, write out the JSON if an output_path is given
    if output_path:
        with open(output_path, 'w', encoding='utf-8') as out:
            json.dump(report_data, out, indent=4)
            print(f"[INFO] Wrote JSON to {output_path}")

    return report_data

def maybe_to_float(value):
    """
    Try to parse the string 'value' as a float. If impossible, return the original string.
    Removes commas, so e.g. '1,234.56' -> 1234.56
    """
    if not value or not isinstance(value, str):
        return value
    candidate = value.replace(",", "").strip()
    if candidate in ("", "--"):
        # Example: some IBKR fields use "--" to indicate no value
        return value
    try:
        return float(candidate)
    except ValueError:
        return value


## Need to watch the report folder and run the function to convert any new/modified CSV to json and save a Portfolio Snapshot #TODO
def convert_ibkr_activity_csv_to_json_enhanced(file_path: str, output_path: str = None):
    """
    A variant of your CSV-to-JSON parser that:
      1) Preserves the same overall 'sections' -> 'tables' -> 'rows' structure
      2) Automatically tries to parse numeric fields into floats
      3) Optionally splits data rows from 'Subtotal/Total' rows
    """
    # Read report date from the report instead of just using today's date
    report_date = None

    with open(file_path, mode='r', encoding='utf-8-sig', newline='') as csv_file:
        
        reader = csv.reader(csv_file)
        current_tables = {}  # section_name -> [list of tables]

        for row in reader:
            if not row:
                continue

            section = row[0].strip()
            record_type = row[1].strip() if len(row) > 1 else ""
            content = row[2:] if len(row) > 2 else []

            if section not in current_tables:
                current_tables[section] = []

            if record_type == "Header":
                # Create a new table
                new_table = {
                    "headers": content,
                    "rows": [],
                    # We'll store subtotals/totals separate if we want
                    "totals": []
                }
                current_tables[section].append(new_table)

            else:
                # This is typically Data, SubTotal, or Total, etc.
                if not current_tables[section]:
                    # If there's no table started, create one
                    current_tables[section].append({
                        "headers": [],
                        "rows": [],
                        "totals": []
                    })
                active_table = current_tables[section][-1]

                # Build row dictionary
                row_dict = {}
                for i, val in enumerate(content):
                    if i < len(active_table["headers"]):
                        col_name = active_table["headers"][i]
                    else:
                        col_name = f"extra_col_{i}"

                    # Convert to float if possible
                    row_dict[col_name] = maybe_to_float(val)

                    #Added (Ahmad 22nd May 2025)
                    if col_name == "Period" and not report_date:
                        report_date = datetime.strptime(val, '%Y-%m-%d')

                # Add the record_type for reference
                row_dict["_record_type"] = record_type

                # If you'd like to treat SubTotal/Total differently
                if record_type in ("SubTotal", "Total"):
                    active_table["totals"].append(row_dict)
                else:
                    active_table["rows"].append(row_dict)

        
        
        # Try to extract from metadata first (Moved from ibkr_importer) Ahmad 22nd May 2025
        if report_date is not None:
            try:
                print.info(f"Using report date from metadata: {report_date}")
            except Exception as e:
                print.warning(f"Error parsing report date from metadata: {e}")

         # If that fails, try to extract from filename (format: *_YYYYMMDD.json)
        if report_date is None:
            filename = os.path.basename(file_path)
            match = re.search(r'_(\d{8})\.json$', filename)
            if match:
                try:
                    date_str = match.group(1)
                    report_date = datetime.strptime(date_str, '%Y%m%d')
                    print.info(f"Using report date from filename: {report_date}")
                except Exception as e:
                    print.warning(f"Error parsing report date from filename: {e}")

        # If both fail, use current date
        if report_date is None:
            report_date = datetime.utcnow()
            print(f"Could not determine report date, using current date: {report_date}")

        
        # Move tables into final structure
        report_data = {
            "metadata": {
                "report_date": report_date,
                "file_name": Path(file_path).name,
                "source": "Interactive Brokers"
            },
            "sections": {}
        }
        
        for section_name, tables in current_tables.items():
            report_data["sections"][section_name] = {
                "tables": tables
            }

    # Write to JSON if requested
    if output_path:
        with open(output_path, 'w', encoding='utf-8') as out:
            json.dump(report_data, out, indent=4)
            print(f"[INFO] Enhanced JSON saved to {output_path}")

    return report_data

def main():
    folder = os.path.dirname(os.path.realpath(__file__)) #Current folder
    folder_path = Path(folder)
    print(folder_path.parent)

    # Look for all CSV files in that folder
    csv_files = sorted(folder_path.glob("*.csv"))

    if not csv_files:
        print("[WARNING] No CSV files found in folder:", folder_path)
        return

    for csv_file in csv_files:
        # The output JSON file name will have the same base name, but .json extension
        output_file = csv_file.with_suffix('.json')

        convert_ibkr_activity_csv_to_json_enhanced(str(csv_file), str(output_file))


if __name__ == "__main__":
    main()
