{"metadata": {"report_date": "2025-04-07", "file_name": "DUK415187_20250407.csv", "source": "Interactive Brokers"}, "sections": {"Statement": {"tables": [{"headers": ["Field Name", "Field Value"], "rows": [{"Field Name": "B<PERSON>r<PERSON><PERSON>", "Field Value": "", "_record_type": "Data"}, {"Field Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Field Value": "", "_record_type": "Data"}, {"Field Name": "Title", "Field Value": "Activity Summary", "_record_type": "Data"}, {"Field Name": "Period", "Field Value": "April 7, 2025", "_record_type": "Data"}, {"Field Name": "WhenGenerated", "Field Value": "2025-04-08, 02:51:46 EDT", "_record_type": "Data"}], "totals": []}]}, "Disclaimer": {"tables": [{"headers": ["Field Name", "Field Value"], "rows": [{"Field Name": "Disclaimer", "Field Value": "This Activity Summary is provided as a convenience to customers who have accounts with multiple Interactive Brokers companies in multiple jurisdictions. Positions and cash shown on this Activity Summary may be held at different Interactive Brokers companies in different countries, subject to different regulatory rules and protections. This Activity Summary does not serve as your formal customer statement. Customer statements for each IB company are available under Account Management when you log in to the IB website.", "_record_type": "Data"}], "totals": []}]}, "Account Information": {"tables": [{"headers": ["Field Name", "Field Value"], "rows": [{"Field Name": "Name", "Field Value": "Stichting Legal Owner Keheilan Fund", "_record_type": "Data"}, {"Field Name": "Account", "Field Value": "DUK415187 (Custom Consolidated)", "_record_type": "Data"}, {"Field Name": "Accounts Included", "Field Value": "DUK415187, DUK415187-P (Paxos)", "_record_type": "Data"}, {"Field Name": "Account Type", "Field Value": "Individual", "_record_type": "Data"}, {"Field Name": "Customer Type", "Field Value": "Partnership", "_record_type": "Data"}, {"Field Name": "Account Capabilities", "Field Value": "<PERSON><PERSON>", "_record_type": "Data"}, {"Field Name": "Base Currency", "Field Value": "USD", "_record_type": "Data"}], "totals": []}]}, "Net Asset Value": {"tables": [{"headers": ["Asset Class", "Prior Total", "Current Long", "Current Short", "Current Total", "Change"], "rows": [{"Asset Class": "Cash ", "Prior Total": 0.0, "Current Long": 1000000.0, "Current Short": 0.0, "Current Total": 1000000.0, "Change": 1000000.0, "_record_type": "Data"}, {"Asset Class": "Total", "Prior Total": 0.0, "Current Long": 1000000.0, "Current Short": 0.0, "Current Total": 1000000.0, "Change": 1000000.0, "_record_type": "Data"}], "totals": []}, {"headers": ["Time Weighted Rate of Return"], "rows": [{"Time Weighted Rate of Return": "0%", "_record_type": "Data"}], "totals": []}]}, "Change in Combined NAV": {"tables": [{"headers": ["Field Name", "Field Value"], "rows": [], "totals": []}]}, "Change in NAV": {"tables": [{"headers": [], "rows": [{"extra_col_0": "Starting Value", "extra_col_1": 0.0, "_record_type": "Data"}, {"extra_col_0": "Deposits & Withdrawals", "extra_col_1": 1000000.0, "_record_type": "Data"}, {"extra_col_0": "Ending Value", "extra_col_1": 1000000.0, "_record_type": "Data"}], "totals": []}]}, "Cash Report": {"tables": [{"headers": ["<PERSON><PERSON><PERSON><PERSON>ry", "<PERSON><PERSON><PERSON><PERSON>", "Total", "Securities", "Futures", "Paxos", ""], "rows": [{"Currency Summary": "Starting Cash", "Currency": "Base Currency Summary", "Total": 0.0, "Securities": 0.0, "Futures": 0.0, "Paxos": 0.0, "": "", "_record_type": "Data"}, {"Currency Summary": "Deposits", "Currency": "Base Currency Summary", "Total": 1000000.0, "Securities": 1000000.0, "Futures": 0.0, "Paxos": 0.0, "": "", "_record_type": "Data"}, {"Currency Summary": "Ending Cash", "Currency": "Base Currency Summary", "Total": 1000000.0, "Securities": 1000000.0, "Futures": 0.0, "Paxos": 0.0, "": "", "_record_type": "Data"}, {"Currency Summary": "Ending Settled Cash", "Currency": "Base Currency Summary", "Total": 1000000.0, "Securities": 1000000.0, "Futures": 0.0, "Paxos": 0.0, "": "", "_record_type": "Data"}], "totals": []}]}, "Deposits & Withdrawals": {"tables": [{"headers": ["<PERSON><PERSON><PERSON><PERSON>", "Account", "Settle Date", "Description", "Amount"], "rows": [{"Currency": "USD", "Account": "DUK415187", "Settle Date": "2025-04-07", "Description": "Adjustment: Cash Receipt/Disbursement/Transfer", "Amount": 1000000.0, "_record_type": "Data"}, {"Currency": "Total", "Account": "", "Settle Date": "", "Description": "", "Amount": 1000000.0, "_record_type": "Data"}], "totals": []}]}}}