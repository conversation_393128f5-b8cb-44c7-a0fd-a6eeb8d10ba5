#!/usr/bin/env python3
import os
import sys


def create_directory(path):
    """Create directory if it doesn't exist"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"Created directory: {path}")
    else:
        print(f"Directory already exists: {path}")


def create_file(path, content=''):
    """Create file with optional content"""
    dirname = os.path.dirname(path)
    if dirname and not os.path.exists(dirname):
        os.makedirs(dirname)

    # Only create the file if it doesn't exist
    if not os.path.exists(path):
        with open(path, 'w') as f:
            f.write(content)
        print(f"Created file: {path}")
    else:
        print(f"File already exists: {path}")


def setup_portfolio_dashboard_project():
    # Set project root directory
    project_root = 'portfolio_dashboard'
    create_directory(project_root)

    # Create main directories
    directories = [
        'models',
        'routes',
        'static/css',
        'static/js',
        'static/img',
        'templates/admin',
        'templates/auth',
        'templates/customer',
        'templates/errors',
        'utils',
        'instance',
        'migrations',
        'uploads',
        'data/reports',
        'tests'
    ]

    for directory in directories:
        create_directory(os.path.join(project_root, directory))

    # Create main Python files
    python_files = [
        'app.py',
        'config.py',
        'forms.py',
        'routes/__init__.py',
        'routes/auth.py',
        'routes/admin.py',
        'routes/routes_customer.py',
        'routes/api.py',
        'models/__init__.py',
        'models/routes_customer.py',
        'models/portfolio.py',
        'models/investment.py',
        'models/performance.py',
        'utils/__init__.py',
        'utils/ibkr_importer.py',
        'utils/performance_calculator.py',
        'utils/report_generator.py',
        'tests/__init__.py',
        'tests/test_models.py',
        'tests/test_ibkr_import.py',
        'tests/test_performance_calc.py'
    ]

    for file in python_files:
        create_file(os.path.join(project_root, file))

    # Create templates
    template_files = [
        'templates/base.html',
        'templates/index.html',
        'templates/errors/404.html',
        'templates/errors/500.html',
        'templates/auth/login.html',
        'templates/auth/register.html',
        'templates/admin/customer_dashboard.html',
        'templates/admin/customers.html',
        'templates/admin/customer_detail.html',
        'templates/admin/portfolios_dashboard.html',
        'templates/admin/portfolio_detail.html',
        'templates/admin/portfolio_allocations.html',
        'templates/admin/securities.html',
        'templates/admin/security_form.html',
        'templates/admin/security_prices.html',
        'templates/admin/import.html',
        'templates/customer/customer_dashboard.html',
        'templates/customer/investments_dashboard.html',
        'templates/customer/customer_investment_detail.html',
        'templates/customer/investment_form.html',
        'templates/customer/portfolio_detail.html',
        'templates/customer/portfolios_dashboard.html',
        'templates/customer/reports.html',
        'templates/customer/report_summary.html',
        'templates/customer/report_detailed.html',
        'templates/customer/report_performance.html'
    ]

    for file in template_files:
        create_file(os.path.join(project_root, file))

    # Create static files
    static_files = [
        'static/css/style.css',
        'static/js/dashboard.js',
        'static/js/portfolio.js'
    ]

    for file in static_files:
        create_file(os.path.join(project_root, file))

    # Create requirements.txt
    requirements_content = """Flask==2.3.3
Flask-SQLAlchemy==3.1.1
Flask-Login==0.6.2
Flask-WTF==1.2.1
Flask-Migrate==4.0.5
pandas==2.1.1
numpy==1.26.0
matplotlib==3.8.0
werkzeug==2.3.7
psycopg2-binary==2.9.9
python-dateutil==2.8.2
email-validator==2.0.0
Flask-Mail==0.9.1
gunicorn==21.2.0
pytest==7.4.2
python-dotenv==1.0.0
wtforms==3.1.0
"""
    create_file(os.path.join(project_root, 'requirements.txt'), requirements_content)

    # Create README.md
    readme_content = """# Portfolio Dashboard

A Flask-based web application for tracking and reporting investment portfolios across multiple customers.

## Getting Started

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Set up environment variables:
   Create a `.env` file with:
   ```
   FLASK_APP=app.py
   FLASK_CONFIG=development
   SECRET_KEY=your-secure-secret-key
   DATABASE_URI=sqlite:///portfolio_dashboard.db
   ```

3. Run the application:
   ```
   flask run
   ```

The application will be available at http://localhost:5000
"""
    create_file(os.path.join(project_root, 'README.md'), readme_content)

    # Create .env file
    env_content = """FLASK_APP=app.py
FLASK_CONFIG=development
SECRET_KEY=dev-secure-key-change-in-production
DATABASE_URI=sqlite:///portfolio_dashboard.db
"""
    create_file(os.path.join(project_root, '.env'), env_content)

    # Create .gitignore
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
.env
*.db

# Flask
instance/
.webassets-cache

# Project specific
uploads/
data/reports/

# Development
.vscode/
.idea/
*.swp
"""
    create_file(os.path.join(project_root, '.gitignore'), gitignore_content)

    print("\nProject structure created successfully!")
    print(f"You can now copy your code into the appropriate files in the '{project_root}' directory.")
    print("\nTo get started:")
    print(f"1. cd {project_root}")
    print("2. python -m venv venv")
    print("3. source venv/bin/activate  # On Windows: venv\\Scripts\\activate")
    print("4. pip install -r requirements.txt")
    print("5. flask run")


if __name__ == '__main__':
    setup_portfolio_dashboard_project()