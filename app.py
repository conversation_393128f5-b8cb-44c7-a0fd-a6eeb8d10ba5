import os
import nest_asyncio
from flask import Flask, render_template, redirect, url_for
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user
from datetime import datetime
from config import config
from utils import convert_ibkr_csv_2_json_daily_v2
from utils.report_loader import process_all_reports
nest_asyncio.apply()
from models.models_customer import Customer
from flask_migrate import Migrate
from models import db

"""
File: app.py
Author: HH
Date: 2025-04-13
Description: Keheilan

Standard Information:
- Version: 26.0
- Python Version: 3.11
- License: Author Approval

Notes:
- Reporting platform from Keheilan
"""

def create_app(config_name='default'):
    """Application factory function"""
    app = Flask(__name__,
                static_folder='static',  # Make sure this matches your folder name
                static_url_path='/static')  # Make sure this is correct

    app.config.from_object(config[config_name])

    # Initialize extensions
    db.init_app(app)

    # Initialize login manager
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(Customer, int(user_id))

    # Register blueprints
    from routes.auth import auth_bp
    from routes.admin import admin_bp
    from routes.routes_customer import customer_bp
    from routes.api import api_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(customer_bp)
    app.register_blueprint(api_bp, url_prefix='/api')

    # Error handlers
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('errors/500.html'), 500

    # Add this somewhere in your app.py, perhaps in the create_app function
    @app.context_processor
    def inject_now():
        return {'now': datetime.utcnow()}

    # Add custom template filters
    @app.template_filter('format_number')
    def format_number_filter(value):
        """Format a number with thousand separators"""
        return f"{value:,.2f}"

    # Main routes
    @app.route('/')
    def index():
        # Check if the user is authenticated:
        if current_user.is_authenticated:
            # If the user is an admin, redirect to the admin dashboard
            if current_user.is_admin:
                return redirect(url_for('admin.dashboard'))
            # Otherwise, redirect to the customer dashboard
            else:
                return redirect(url_for('customer.dashboard'))
        # If no user is logged in, show a public landing page.
        return render_template('index.html')

    # Create database tables if they don't exist
    with app.app_context():
        convert_ibkr_csv_2_json_daily_v2.main()
        processed = process_all_reports()
        # for fp, rpt_date, status in processed:
        #     app.logger.info(f"Processed report: {fp} - Date: {rpt_date}, Status: {status}")
        db.create_all()
        # Create admin user if it doesn't exist
        admin_exists = Customer.query.filter_by(email=app.config['ADMIN_EMAIL']).first()
        if not admin_exists:
            admin = Customer(
                name='Admin',
                email=app.config['ADMIN_EMAIL'],
                is_admin=True
            )
            admin.set_password(app.config['ADMIN_PASSWORD'])  # Change this in production!
            db.session.add(admin)
            db.session.commit()

    return app


# Create the application instance
app = create_app(os.environ.get('FLASK_CONFIG', 'default'))
migrate = Migrate(app, db)

if __name__ == '__main__':
    app.run(debug=True, use_reloader=False, threaded=True)
