"""Initial migration after reset

Revision ID: 93f10fde3350
Revises: 
Create Date: 2025-04-10 18:39:50.635573

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '93f10fde3350'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('investments', schema=None) as batch_op:
        batch_op.add_column(sa.Column('high_water_mark', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('entry_fee_applied', sa.<PERSON>an(), nullable=True))
        batch_op.add_column(sa.Column('exit_fee_applied', sa.<PERSON>(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('investments', schema=None) as batch_op:
        batch_op.drop_column('exit_fee_applied')
        batch_op.drop_column('entry_fee_applied')
        batch_op.drop_column('high_water_mark')

    # ### end Alembic commands ###
