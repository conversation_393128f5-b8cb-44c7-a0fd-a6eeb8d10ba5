import tempfile
import unittest
from datetime import datetime, timedelta
import os
import logging

from app import create_app, db
from models.models_customer import Customer
from models.portfolio import Portfolio
from models.investment import Investment
from models.performance import Performance, PortfolioSnapshot

from utils.performance_calculator import calculate_customer_performance
from utils.report_generator import generate_customer_report


def generate_customer_plots(customer_id, output_dir='plots'):
    """
    Generate comprehensive investment visualization plots for a customer.
    """
    import matplotlib.pyplot as plt
    import matplotlib.ticker as mticker
    import numpy as np
    import os
    from datetime import datetime, timedelta

    # Suppress matplotlib font warnings
    import logging
    logging.getLogger('matplotlib.font_manager').setLevel(logging.ERROR)

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get customer data - using newer SQLAlchemy approach
    customer = db.session.get(Customer, customer_id)
    if not customer:
        return {"error": "Customer not found"}

    # Get investments for the customer
    investments = Investment.query.filter_by(
        customer_id=customer_id, is_active=True, is_funded=True
    ).all()

    if not investments:
        return {"error": f"No active investments found for {customer.name}"}

    plot_files = {}

    # Set plotting style
    plt.style.use('fivethirtyeight')

    # 1. Portfolio Allocation Pie Chart
    fig, ax = plt.subplots(figsize=(10, 8))

    # Group investments by portfolio
    portfolio_data = {}
    for inv in investments:
        portfolio_name = inv.portfolio.name
        if portfolio_name in portfolio_data:
            portfolio_data[portfolio_name] += inv.amount
        else:
            portfolio_data[portfolio_name] = inv.amount

    # Create the pie chart
    labels = list(portfolio_data.keys())
    sizes = list(portfolio_data.values())

    # Calculate percentages for labels
    total = sum(sizes)
    percentages = [(size / total) * 100 for size in sizes]

    # Custom labels with portfolio name, amount and percentage
    custom_labels = [f"{label}\n${size:,.0f} ({pct:.1f}%)"
                     for label, size, pct in zip(labels, sizes, percentages)]

    # Color map
    colors = plt.cm.Spectral(np.linspace(0.1, 0.9, len(sizes)))
    explode = [0.05] * len(sizes)  # Explode all slices slightly

    wedges, texts = ax.pie(
        sizes,
        explode=explode,
        colors=colors,
        shadow=True,
        startangle=90,
        wedgeprops={'edgecolor': 'white', 'linewidth': 2}
    )

    # Add labels with lines connecting to slices
    ax.legend(wedges, custom_labels, loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))

    plt.title(f"{customer.name}'s Portfolio Allocation", fontsize=18, pad=20)

    allocation_file = os.path.join(output_dir, f"customer_{customer_id}_allocation.png")
    plt.savefig(allocation_file, dpi=150, bbox_inches='tight')
    plt.close()
    plot_files["allocation"] = allocation_file

    # 2. Investment Growth Line Chart with Area
    fig, ax = plt.subplots(figsize=(12, 8))

    # Track maximum date range for all investments
    min_date = datetime(2100, 1, 1)  # Start with future date
    max_date = datetime(2000, 1, 1)  # Start with past date

    for i, inv in enumerate(investments):
        performances = Performance.query.filter_by(investment_id=inv.id).order_by(Performance.date).all()

        if performances:
            dates = [p.date for p in performances]
            values = [p.value for p in performances]

            # Update date range
            if dates[0] < min_date:
                min_date = dates[0]
            if dates[-1] > max_date:
                max_date = dates[-1]

            # Plot with area and custom styles
            color = plt.cm.viridis(i / len(investments))
            ax.plot(dates, values, marker='o', linewidth=3, color=color,
                    label=f"{inv.portfolio.name} (${inv.amount:,.0f})")
            ax.fill_between(dates, values, alpha=0.2, color=color)

    # Add initial investment as reference line
    if min_date < max_date:
        for i, inv in enumerate(investments):
            ax.axhline(y=inv.amount, color=plt.cm.viridis(i / len(investments)),
                       linestyle='--', alpha=0.5)
            ax.text(min_date, inv.amount, f"Initial: ${inv.amount:,.0f}",
                    va='bottom', ha='left', fontsize=9)

    # Format axes
    ax.set_xlabel('Date', fontsize=14)
    ax.set_ylabel('Value ($)', fontsize=14)
    ax.set_title(f"{customer.name}'s Investment Growth Over Time", fontsize=18)
    ax.grid(True, alpha=0.3)

    # Add some padding to the date range
    if min_date < max_date:
        date_range = (max_date - min_date).days
        ax.set_xlim(min_date - timedelta(days=date_range * 0.05),
                    max_date + timedelta(days=date_range * 0.05))

    # Format y-axis as currency
    formatter = mticker.StrMethodFormatter('${x:,.0f}')
    ax.yaxis.set_major_formatter(formatter)

    # Enhance legend
    ax.legend(loc='upper left', frameon=True, fontsize=12)

    plt.tight_layout()

    growth_file = os.path.join(output_dir, f"customer_{customer_id}_growth.png")
    plt.savefig(growth_file, dpi=150, bbox_inches='tight')
    plt.close()
    plot_files["growth"] = growth_file

    # 3. Investment Return Comparison Chart
    fig, ax = plt.subplots(figsize=(12, 6))

    # Prepare data
    portfolio_names = []
    amounts = []
    returns = []
    current_values = []

    for inv in investments:
        portfolio_names.append(inv.portfolio.name)
        amounts.append(inv.amount)
        returns.append(inv.get_return())
        current_values.append(inv.get_current_value())

    # Calculate x positions for grouped bars
    indices = np.arange(len(portfolio_names))
    bar_width = 0.35

    # Create grouped bar chart
    bars1 = ax.bar(indices - bar_width / 2, amounts, bar_width,
                   label='Initial Investment', color='cornflowerblue')
    bars2 = ax.bar(indices + bar_width / 2, current_values, bar_width,
                   label='Current Value', color='lightseagreen')

    # Add return percentage on top of the bars
    for i, (amt, val, ret) in enumerate(zip(amounts, current_values, returns)):
        # Determine text position and color
        if val > amt:
            color = 'darkgreen'
            va = 'bottom'
            y_pos = val + (val * 0.02)  # Slightly above the bar
        else:
            color = 'darkred'
            va = 'top'
            y_pos = val - (val * 0.02)  # Slightly below the bar

        ax.text(indices[i], y_pos, f"{ret:.1f}%",
                ha='center', va=va, fontweight='bold', color=color)

    # Customize the plot
    ax.set_ylabel('Amount ($)', fontsize=14)
    ax.set_title(f"{customer.name}'s Investment Performance", fontsize=18)
    ax.set_xticks(indices)
    ax.set_xticklabels(portfolio_names)
    ax.legend()

    # Format y-axis as currency
    ax.yaxis.set_major_formatter(mticker.StrMethodFormatter('${x:,.0f}'))

    plt.tight_layout()

    performance_file = os.path.join(output_dir, f"customer_{customer_id}_performance.png")
    plt.savefig(performance_file, dpi=150, bbox_inches='tight')
    plt.close()
    plot_files["performance"] = performance_file

    # 4. Comprehensive Dashboard - Fixed layout issues
    fig = plt.figure(figsize=(16, 12))

    # Add title manually to avoid layout issues
    fig.text(0.5, 0.97, f"{customer.name}'s Investment Dashboard",
             fontsize=22, ha='center', va='top')

    # Create grid for subplots with more space
    gs = fig.add_gridspec(3, 4, hspace=0.5, wspace=0.4)

    # Calculate overall metrics
    total_invested = sum(inv.amount for inv in investments)
    total_current = sum(inv.get_current_value() for inv in investments)
    total_return = ((total_current - total_invested) / total_invested * 100) if total_invested > 0 else 0

    # Add summary stats
    ax_stats = fig.add_subplot(gs[0, :2])
    ax_stats.axis('off')

    # Create styled text boxes for key metrics
    summary_text = (
        f"Summary Statistics\n\n"
        f"Total Invested: ${total_invested:,.2f}\n"
        f"Current Value: ${total_current:,.2f}\n"
        f"Total Return: {total_return:.2f}%\n"
        f"Number of Investments: {len(investments)}\n"
        f"Report Date: {datetime.now().strftime('%Y-%m-%d')}"
    )

    props = dict(boxstyle='round', facecolor='lightskyblue', alpha=0.5)
    ax_stats.text(0.05, 0.95, summary_text, transform=ax_stats.transAxes, fontsize=14,
                  verticalalignment='top', bbox=props)

    # Add allocation pie chart
    ax_pie = fig.add_subplot(gs[0, 2:])
    wedges, _ = ax_pie.pie(
        sizes,
        explode=explode,
        colors=colors,
        shadow=True,
        startangle=90,
        wedgeprops={'edgecolor': 'white', 'linewidth': 1.5}
    )
    ax_pie.legend(wedges, labels, loc="center", bbox_to_anchor=(0.5, 0))
    ax_pie.set_title("Portfolio Allocation", fontsize=16)

    # Add growth chart
    ax_growth = fig.add_subplot(gs[1, :])

    for i, inv in enumerate(investments):
        performances = Performance.query.filter_by(investment_id=inv.id).order_by(Performance.date).all()

        if performances:
            dates = [p.date for p in performances]
            values = [p.value for p in performances]

            color = plt.cm.viridis(i / len(investments))
            ax_growth.plot(dates, values, marker='o', linewidth=2, color=color,
                           label=f"{inv.portfolio.name}")
            ax_growth.fill_between(dates, values, alpha=0.1, color=color)

    ax_growth.set_title("Investment Growth Over Time", fontsize=16)
    ax_growth.yaxis.set_major_formatter(mticker.StrMethodFormatter('${x:,.0f}'))
    ax_growth.legend(loc='upper left')

    # Add return comparison
    ax_return = fig.add_subplot(gs[2, :2])

    # Create horizontal bar chart for returns
    sorted_indices = np.argsort(returns)
    sorted_names = [portfolio_names[i] for i in sorted_indices]
    sorted_returns = [returns[i] for i in sorted_indices]

    bars = ax_return.barh(sorted_names, sorted_returns)

    # Color bars based on return value
    for i, bar in enumerate(bars):
        if sorted_returns[i] >= 0:
            bar.set_color('green')
        else:
            bar.set_color('red')

    # Add a vertical line at x=0
    ax_return.axvline(x=0, color='black', linestyle='--', alpha=0.3)

    # Add return values
    for i, v in enumerate(sorted_returns):
        ax_return.text(v + (1 if v >= 0 else -1), i, f"{v:.1f}%",
                       va='center', fontweight='bold')

    ax_return.set_title("Investment Returns (%)", fontsize=16)

    # Add risk/performance metrics
    ax_metrics = fig.add_subplot(gs[2, 2:])
    ax_metrics.axis('off')

    # Create a table with risk metrics (sample values)
    metrics_text = (
        f"Performance Metrics\n\n"
        f"Annualized Return: {total_return / 0.25:.2f}%\n"  # Assuming 3-month data
        f"Volatility: Low\n"
        f"Sharpe Ratio: 1.2\n"
        f"Max Drawdown: 3.5%\n"
        f"Projected 1yr Value: ${total_current * 1.08:.2f}"
    )

    metrics_props = dict(boxstyle='round', facecolor='lightgreen', alpha=0.5)
    ax_metrics.text(0.05, 0.95, metrics_text, transform=ax_metrics.transAxes, fontsize=14,
                    verticalalignment='top', bbox=metrics_props)

    # Use subplots_adjust instead of tight_layout
    plt.subplots_adjust(top=0.9, bottom=0.08, left=0.1, right=0.9, hspace=0.4, wspace=0.4)

    dashboard_file = os.path.join(output_dir, f"customer_{customer_id}_dashboard.png")
    plt.savefig(dashboard_file, dpi=150, bbox_inches='tight')
    plt.close()
    plot_files["dashboard"] = dashboard_file

    return plot_files

class PlatformTestCase(unittest.TestCase):
    def setUp(self):
        # Set up logging for better debugging
        logging.basicConfig(level=logging.DEBUG)
        self.logger = logging.getLogger(__name__)

        # Create test app with proper configuration
        self.app = create_app('testing')

        # Use in-memory SQLite for testing
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'

        # Zero out fees for easier testing
        self.app.config['MANAGEMENT_FEE_YEARLY'] = 0.0
        self.app.config['PERFORMANCE_FEE_RATE'] = 0.0
        self.app.config['BROKERAGE_FEE_DAILY'] = 0.0
        self.app.config['EXIT_FEE_RATE'] = 0.0

        # Create temp directory with proper structure
        self.test_reports_dir = tempfile.mkdtemp()
        os.makedirs(os.path.join(self.test_reports_dir, 'json'), exist_ok=True)
        self.app.config['IBKR_REPORTS_DIR'] = self.test_reports_dir

        # Push app context
        self.app_context = self.app.app_context()
        self.app_context.push()

        # Create clean database
        db.drop_all()
        db.create_all()

        # Create test data
        self._create_test_data()

    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

        # Clean up temp directory
        import shutil
        if os.path.exists(self.test_reports_dir):
            shutil.rmtree(self.test_reports_dir)

    def _create_test_data(self):
        """Create four customers, two portfolios, IBKR snapshots and multiple investments."""
        try:
            # Create four customers.
            customer_a = Customer(name="CustomerA", email="<EMAIL>")
            customer_a.set_password("passwordA")
            customer_b = Customer(name="CustomerB", email="<EMAIL>")
            customer_b.set_password("passwordB")
            customer_c = Customer(name="CustomerC", email="<EMAIL>")
            customer_c.set_password("passwordC")
            customer_d = Customer(name="CustomerD", email="<EMAIL>")
            customer_d.set_password("passwordD")
            db.session.add_all([customer_a, customer_b, customer_c, customer_d])
            db.session.commit()
            self.customer_a = customer_a
            self.customer_b = customer_b
            self.customer_c = customer_c
            self.customer_d = customer_d

            # Create two portfolios.
            portfolio1 = Portfolio(name="Keheilan Fund",
                                   description="Test portfolio from IBKR reports",
                                   risk_level="Medium")
            portfolio2 = Portfolio(name="Alpha Growth Fund",
                                   description="Another test portfolio",
                                   risk_level="High")
            db.session.add_all([portfolio1, portfolio2])
            db.session.commit()
            self.portfolio1 = portfolio1
            self.portfolio2 = portfolio2

            # Create PortfolioSnapshots (simulate IBKR reports).
            # For portfolio1 ("Keheilan Fund"):
            snap1_p1 = PortfolioSnapshot(
                portfolio_id=portfolio1.id,
                date=datetime(2025, 4, 8, 16, 0),
                total_value=1000000.0  # Day 1 value
            )
            snap2_p1 = PortfolioSnapshot(
                portfolio_id=portfolio1.id,
                date=datetime(2025, 4, 9, 16, 0),
                total_value=1050000.0  # Day 2 value
            )
            snap3_p1 = PortfolioSnapshot(
                portfolio_id=portfolio1.id,
                date=datetime(2025, 4, 10, 16, 0),
                total_value=1100000.0  # Day 3 value
            )
            snap4_p1 = PortfolioSnapshot(
                portfolio_id=portfolio1.id,
                date=datetime(2025, 4, 11, 16, 0),
                total_value=1150000.0  # Day 4 value
            )

            # For portfolio2 ("Alpha Growth Fund"):
            snap1_p2 = PortfolioSnapshot(
                portfolio_id=portfolio2.id,
                date=datetime(2025, 4, 8, 16, 0),
                total_value=800000.0
            )
            snap2_p2 = PortfolioSnapshot(
                portfolio_id=portfolio2.id,
                date=datetime(2025, 4, 9, 16, 0),
                total_value=820000.0
            )
            snap3_p2 = PortfolioSnapshot(
                portfolio_id=portfolio2.id,
                date=datetime(2025, 4, 10, 16, 0),
                total_value=840000.0
            )
            snap4_p2 = PortfolioSnapshot(
                portfolio_id=portfolio2.id,
                date=datetime(2025, 4, 11, 16, 0),
                total_value=860000.0
            )

            db.session.add_all([
                snap1_p1, snap2_p1, snap3_p1, snap4_p1,
                snap1_p2, snap2_p2, snap3_p2, snap4_p2
            ])
            db.session.commit()

            # Create investments with explicit entry_nav and units
            # For portfolio1 ("Keheilan Fund"):
            inv_a_p1 = Investment(
                customer_id=customer_a.id,
                portfolio_id=portfolio1.id,
                amount=200000.0,
                start_date=datetime(2025, 4, 8, 10, 0),
                is_active=True,
                is_funded=True,
                entry_nav=1000.0,  # Set explicitly to avoid calculation issues
                units=200.0  # 200,000 / 1000 = 200 units
            )

            inv_b_p1 = Investment(
                customer_id=customer_b.id,
                portfolio_id=portfolio1.id,
                amount=330000.0,
                start_date=datetime(2025, 4, 9, 11, 0),
                is_active=True,
                is_funded=True,
                entry_nav=1000.0,
                units=330.0
            )

            inv_c_p1 = Investment(
                customer_id=customer_c.id,
                portfolio_id=portfolio1.id,
                amount=150000.0,
                start_date=datetime(2025, 4, 9, 11, 0),
                is_active=True,
                is_funded=True,
                entry_nav=1000.0,
                units=150.0
            )

            # For portfolio2 ("Alpha Growth Fund"):
            inv_d_p2 = Investment(
                customer_id=customer_d.id,
                portfolio_id=portfolio2.id,
                amount=100000.0,
                start_date=datetime(2025, 4, 8, 9, 30),
                is_active=True,
                is_funded=True,
                entry_nav=1000.0,
                units=100.0
            )

            inv_a_p2 = Investment(
                customer_id=customer_a.id,
                portfolio_id=portfolio2.id,
                amount=250000.0,
                start_date=datetime(2025, 4, 10, 10, 30),
                is_active=True,
                is_funded=True,
                entry_nav=1000.0,
                units=250.0
            )

            inv_b_p2 = Investment(
                customer_id=customer_b.id,
                portfolio_id=portfolio2.id,
                amount=200000.0,
                start_date=datetime(2025, 4, 11, 12, 0),
                is_active=True,
                is_funded=True,
                entry_nav=1000.0,
                units=200.0
            )

            inv_c_p2 = Investment(
                customer_id=customer_c.id,
                portfolio_id=portfolio2.id,
                amount=150000.0,
                start_date=datetime(2025, 4, 11, 12, 0),
                is_active=True,
                is_funded=True,
                entry_nav=1000.0,
                units=150.0
            )

            db.session.add_all([inv_a_p1, inv_b_p1, inv_c_p1, inv_d_p2, inv_a_p2, inv_b_p2, inv_c_p2])
            db.session.commit()

            self.inv_a_p1 = inv_a_p1
            self.inv_b_p1 = inv_b_p1
            self.inv_c_p1 = inv_c_p1
            self.inv_d_p2 = inv_d_p2
            self.inv_a_p2 = inv_a_p2
            self.inv_b_p2 = inv_b_p2
            self.inv_c_p2 = inv_c_p2

        except Exception as e:
            self.logger.error(f"Error creating test data: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise

    def test_customer_plots(self):

        """Test generation of customer investment visualizations."""
        # Ensure we have all necessary imports
        import matplotlib.pyplot as plt
        import numpy as np
        import os
        import tempfile

        plots_dir = os.path.join(os.path.dirname(__file__), "test_plots")
        os.makedirs(plots_dir, exist_ok=True)
        print(f"Saving plots to: {plots_dir}")

        # First ensure we have performance data
        investments = [self.inv_a_p1, self.inv_b_p1, self.inv_c_p1, self.inv_d_p2,
                       self.inv_a_p2, self.inv_b_p2, self.inv_c_p2]
        for inv in investments:
            result = calculate_customer_performance(inv.id)
            self.assertEqual(result.get('status'), 'success')

        # Create temporary directory for plots
        plots_dir = tempfile.mkdtemp()
        print(f"Saving plots to: {plots_dir}")

        # Generate plots for each customer
        customers = [self.customer_a, self.customer_b, self.customer_c, self.customer_d]
        for customer in customers:
            print(f"Generating plots for {customer.name}...")
            plots = generate_customer_plots(customer.id, output_dir=plots_dir)

            # Verify plots were created successfully
            self.assertNotIn("error", plots, f"Error generating plots for {customer.name}")

            # Check that expected plot files exist
            expected_plots = ["allocation", "growth", "performance", "dashboard"]
            for plot_type in expected_plots:
                self.assertIn(plot_type, plots, f"Missing {plot_type} plot for {customer.name}")
                plot_path = plots[plot_type]
                self.assertTrue(os.path.exists(plot_path), f"Plot file {plot_path} not found")

                # Print the path for easy viewing of plots
                print(f"  {plot_type}: {plot_path}")

        print(f"All customer investment plots generated successfully in {plots_dir}")

    def test_performance_calculations(self):
        """
        Calculate performance for each investment and generate customer reports.
        """
        # Run performance calculations for all investments.
        investments = [self.inv_a_p1, self.inv_b_p1, self.inv_c_p1, self.inv_d_p2,
                       self.inv_a_p2, self.inv_b_p2, self.inv_c_p2]
        for inv in investments:
            self.logger.info(f"Calculating performance for investment {inv.id}")
            result = calculate_customer_performance(inv.id)
            self.assertEqual(result.get('status'), 'success')

        # Refresh investments.
        for inv in investments:
            db.session.refresh(inv)

        # Compute individual current values and returns.
        cur_val_a_p1 = self.inv_a_p1.get_current_value()
        cur_val_b_p1 = self.inv_b_p1.get_current_value()
        cur_val_c_p1 = self.inv_c_p1.get_current_value()
        cur_val_d_p2 = self.inv_d_p2.get_current_value()
        cur_val_a_p2 = self.inv_a_p2.get_current_value()
        cur_val_b_p2 = self.inv_b_p2.get_current_value()
        cur_val_c_p2 = self.inv_c_p2.get_current_value()

        # Log values for debugging
        self.logger.info(f"CustomerA P1 value: {cur_val_a_p1} (expected ~338236)")
        self.logger.info(f"CustomerB P1 value: {cur_val_b_p1} (expected ~557059)")
        self.logger.info(f"CustomerC P1 value: {cur_val_c_p1} (expected ~253677)")
        self.logger.info(f"CustomerD P2 value: {cur_val_d_p2} (expected ~122857)")
        self.logger.info(f"CustomerA P2 value: {cur_val_a_p2} (expected ~307143)")
        self.logger.info(f"CustomerB P2 value: {cur_val_b_p2} (expected ~245714)")
        self.logger.info(f"CustomerC P2 value: {cur_val_c_p2} (expected ~184286)")

        # Allowable tolerance.
        value_tol = 3000  # dollars
        percent_tol = 2.0  # percent

        # Test assertions with detailed error messages
        self.assertAlmostEqual(cur_val_a_p1, 338236, delta=value_tol,
                               msg=f"CustomerA P1 value expected ~338236, got {cur_val_a_p1}")
        self.assertAlmostEqual(cur_val_b_p1, 557059, delta=value_tol,
                               msg=f"CustomerB P1 value expected ~557059, got {cur_val_b_p1}")
        self.assertAlmostEqual(cur_val_c_p1, 253677, delta=value_tol,
                               msg=f"CustomerC P1 value expected ~253677, got {cur_val_c_p1}")
        self.assertAlmostEqual(cur_val_d_p2, 122857, delta=value_tol,
                               msg=f"CustomerD P2 value expected ~122857, got {cur_val_d_p2}")
        self.assertAlmostEqual(cur_val_a_p2, 307143, delta=value_tol,
                               msg=f"CustomerA P2 value expected ~307143, got {cur_val_a_p2}")
        self.assertAlmostEqual(cur_val_b_p2, 245714, delta=value_tol,
                               msg=f"CustomerB P2 value expected ~245714, got {cur_val_b_p2}")
        self.assertAlmostEqual(cur_val_c_p2, 184286, delta=value_tol,
                               msg=f"CustomerC P2 value expected ~184286, got {cur_val_c_p2}")

        # Calculate combined totals for each customer with better logging
        invested_a = self.inv_a_p1.amount + self.inv_a_p2.amount
        current_a = cur_val_a_p1 + cur_val_a_p2
        return_a = ((current_a - invested_a) / invested_a * 100)
        self.logger.info(f"CustomerA total: invested={invested_a}, current={current_a}, return={return_a}%")

        invested_b = self.inv_b_p1.amount + self.inv_b_p2.amount
        current_b = cur_val_b_p1 + cur_val_b_p2
        return_b = ((current_b - invested_b) / invested_b * 100)
        self.logger.info(f"CustomerB total: invested={invested_b}, current={current_b}, return={return_b}%")

        invested_c = self.inv_c_p1.amount + self.inv_c_p2.amount
        current_c = cur_val_c_p1 + cur_val_c_p2
        return_c = ((current_c - invested_c) / invested_c * 100)
        self.logger.info(f"CustomerC total: invested={invested_c}, current={current_c}, return={return_c}%")

        invested_d = self.inv_d_p2.amount
        current_d = cur_val_d_p2
        return_d = ((current_d - invested_d) / invested_d * 100)
        self.logger.info(f"CustomerD total: invested={invested_d}, current={current_d}, return={return_d}%")

        # Test customer reports with robust error handling
        try:
            report_a = generate_customer_report(self.customer_a.id)
            report_b = generate_customer_report(self.customer_b.id)
            report_c = generate_customer_report(self.customer_c.id)
            report_d = generate_customer_report(self.customer_d.id)

            # Print results for inspection.
            print("CustomerA:", report_a)
            print("CustomerB:", report_b)
            print("CustomerC:", report_c)
            print("CustomerD:", report_d)

            # Add plot generation at the end of the test
            print("\nGenerating visualization plots for all customers...")
            import tempfile
            plots_dir = tempfile.mkdtemp()
            print(f"Saving plots to: {plots_dir}")

            # Generate plots for each customer
            customers = [self.customer_a, self.customer_b, self.customer_c, self.customer_d]
            for customer in customers:
                print(f"Generating plots for {customer.name}...")
                plots = generate_customer_plots(customer.id, output_dir=plots_dir)

                # Print the plot paths for easy access
                for plot_type, plot_path in plots.items():
                    print(f"  {plot_type}: {plot_path}")

            print(f"All customer investment plots generated successfully in {plots_dir}")

        except Exception as e:
            self.logger.error(f"Error generating reports: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise


if __name__ == '__main__':
    unittest.main()